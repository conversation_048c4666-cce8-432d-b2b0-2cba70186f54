#' @title labCalc_b_bgb_b3227_101test
#' @description Create a updated lab crf
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param ae This the ae dataframe
#' @param lab This is the lab dataframe
#' @param lb_toxgrades This is the lb_toxgrades dataframe
#' @param AnalytePT This is the AnalytePT dataframe
#' @param SubjectInfo_tmp This is the SubjectInfo dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return lb_calc
#'
#' @export labCalc_b_bgb_b3227_101test
#'
#' @importFrom dplyr mutate row_number filter select if_else case_when distinct pull rowwise left_join group_by arrange ungroup
#' @importFrom stringr str_to_upper
#' @importFrom tibble as_tibble
#' @importFrom lubridate days as_date
#' @importFrom glue glue_collapse
#' @importFrom runner max_run
#' @importFrom purrr map_chr
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_directory_exists assert_data_frame
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom stats sd
#' @importFrom rlang .data
#' @importFrom tibble tribble
#'
#' @examples
#' \dontrun{
#'labCalc_b_bgb_b3227_101test(studyId = studyIdVar, sourceLocation = OutFolder,
#'                       tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                       ae = ae, lab = lab, lb_toxgrades = lb_toxgrades,
#'                       AnalytePT = AnalytePT, SubjectInfo_tmp = SubjectInfo, develop.f = develop.f,
#'                       vpath = vpath)
#' }
#'
#'
labCalc_b_bgb_b3227_101test <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime,
                                   ae, lab, lb_toxgrades, AnalytePT, SubjectInfo_tmp, develop.f = develop.f,
                                   vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "labCalc_b_bgb_b3227_101test"

      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))
      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))
      # Assert lab has min.rows and min.cols
      checkmate::assert_data_frame(lab, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm lab has min.rows and min.cols."))
      # Assert lb_toxgrades has min.rows and min.cols
      checkmate::assert_data_frame(lb_toxgrades, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm lb_toxgrades has min.rows and min.cols."))
      # Assert AnalytePT has min.rows and min.cols
      checkmate::assert_data_frame(AnalytePT, min.rows = 1, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm AnalytePT has min.rows and min.cols."))


      # lb_toxgrades Table -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating rowid for lb_toxgrades dataframe"))
      lb_toxgrades <- lb_toxgrades %>%
        dplyr::mutate(rowid = dplyr::row_number())

      # AnalytePT_tmp Table -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating AnalytePT_tmp dataframe"))
      AnalytePT_tmp <- AnalytePT

      # lab_small_tmp Table -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating lab_small_tmp dataframe"))
      lab_small_tmp <- lb_toxgrades %>%
        dplyr::filter(!is.na(.data$RecordDate)) %>%
        dplyr::filter(.data$`Dictionary-Derived Lab Name` %in% AnalytePT_tmp$lbtest) %>%
        dplyr::mutate(LBToxGr = .data$`LB Toxicity-CTCAE`) %>%
        dplyr::filter(grepl("[3-4]", .data$LBToxGr)) %>%
        dplyr::mutate(`LBToxGr_Upper` =
                        stringr::str_to_upper(.data$LBToxGr)) %>%
        tibble::as_tibble() %>%
        dplyr::select(.data$Subject, .data$rowid, .data$RecordDate, .data$`Dictionary-Derived Lab Name`,
                      .data$LBToxGr, .data$`LBToxGr_Upper`)


      # ae_small_tmp Dataframe  -------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_small_tmp dataframe"))
      ae_small_tmp <- ae %>%
        dplyr::filter(!is.na(.data$AESTDAT_INT)) %>%
        dplyr::filter(.data$AETERM_PT %in% AnalytePT_tmp$PT) %>%
        tibble::as_tibble() %>%
        dplyr::left_join(.,SubjectInfo_tmp %>% dplyr::select(Subject = .data$`Subject name or identifier`, .data$`Death Date`), by = c("Subject")) %>%
        dplyr::mutate(stdt = lubridate::as_date(.data$AESTDAT_INT) - lubridate::days(9),
                      enddt = dplyr::case_when(
                        !is.na(lubridate::as_date(.data$AEENDAT_INT)) ~ lubridate::as_date(.data$AEENDAT_INT) + lubridate::days(9),
                        is.na( lubridate::as_date(.data$AEENDAT_INT)) & !is.na(lubridate::as_date(.data$`Death Date`)) ~ lubridate::as_date(.data$`Death Date`),
                        is.na( lubridate::as_date(.data$AEENDAT_INT)) & is.na( lubridate::as_date(.data$`Death Date`)) ~ lubridate::as_date(NA),
                        TRUE ~ lubridate::as_date(NA)
                      ),
                      ongoing = dplyr::if_else(is.na(.data$AEENDAT_INT) & is.na(.data$`Death Date`),"Y", NA_character_),
                      AEToxStd = dplyr::case_when(
                        grepl("1", .data$AETOXGR) ~ "Grade 1: Mild",
                        grepl("2", .data$AETOXGR) ~ "Grade 2: Moderate",
                        grepl("3", .data$AETOXGR) ~ "Grade 3: Severe",
                        grepl("4", .data$AETOXGR) ~ "Grade 4: Life-Threatening",
                        grepl("5", .data$AETOXGR) ~ "Grade 5: Fatal",
                        TRUE ~ as.character(.data$AETOXGR))
        ) %>%
        dplyr::select(.data$Subject, .data$RecordId, aePT = .data$AETERM_PT, .data$stdt, .data$enddt, .data$AEToxStd, .data$ongoing)

      # ae_small_tmp_upper Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_small_tmp_upper dataframe"))
      ae_small_tmp_upper <- ae_small_tmp %>%
        dplyr::mutate(AEToxStd = stringr::str_to_upper(.data$AEToxStd) )

      # getAEMatch Function -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating getAEMatch Function"))
      getAEMatch <- function(sub_tmp, lbDerName_tmp, LBToxGr_tmp , lbRecordDate,  LBToxGr_Upper_tmp){
        aelist <- AnalytePT_tmp %>%
          dplyr::filter(.data$lbtest == lbDerName_tmp) %>%
          dplyr::select(.data$PT) %>%
          dplyr::distinct() %>%
          dplyr::pull()

        nr <- ae_small_tmp_upper %>%
          dplyr::filter(.data$Subject == sub_tmp &
                          .data$aePT %in% aelist &
                          .data$AEToxStd == LBToxGr_Upper_tmp) %>%
          dplyr::filter(lubridate::as_date(.data$stdt) <= lubridate::as_date(lbRecordDate) &(
            (lubridate::as_date(.data$enddt) >= lubridate::as_date(lbRecordDate))|
              (.data$ongoing == "Y"))) %>%
          nrow()

        nr2 <- ae_small_tmp %>%
          dplyr::filter(.data$Subject == sub_tmp &
                          .data$aePT %in% aelist ) %>%
          dplyr::filter(lubridate::as_date(.data$stdt) <= lubridate::as_date(lbRecordDate) &(
            (lubridate::as_date(.data$enddt) >= lubridate::as_date(lbRecordDate))|
              (.data$ongoing == "Y"))) %>%
          nrow()

        grades <- ae_small_tmp %>%
          dplyr::filter(.data$Subject == sub_tmp &
                          .data$aePT %in% aelist ) %>%
          dplyr::filter(lubridate::as_date(.data$stdt) <= lubridate::as_date(lbRecordDate) &(
            (lubridate::as_date(.data$enddt) >= lubridate::as_date(lbRecordDate))|
              (.data$ongoing == "Y"))) %>%
          dplyr::select(.data$AEToxStd) %>%
          dplyr::distinct() %>%
          dplyr::pull() %>%
          glue::glue_collapse(sep = " ")

        if (nr > 0) {
          return_val <- "Yes"
        }else if (nr == 0 & nr2 > 0) {
          return_val <- paste("Lab toxgr", LBToxGr_tmp, "vs. AE toxgr:",grades)
        }else if (nr == 0 & nr2 == 0) {
          return_val <- paste("No AE near the LB RecordDate")
        }
        return(return_val)
      }

          # Running getAEMatch for each row of lab_small Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Starting getAEMatch for each row of lab_small_tmp dataframe"))

      if(nrow(lab_small_tmp)>0) {
        lb_AEToxFlag <- lab_small_tmp %>%
          dplyr::rowwise() %>%
          dplyr::mutate(AEToxFlag =
                          getAEMatch(sub_tmp = .data$Subject,
                                     lbDerName_tmp = .data$`Dictionary-Derived Lab Name`,
                                     LBToxGr_tmp = .data$`LBToxGr`,
                                     lbRecordDate = .data$RecordDate,
                                     LBToxGr_Upper_tmp = .data$`LBToxGr_Upper`)
          ) %>%
          dplyr::select(.data$Subject, .data$rowid, .data$AEToxFlag)

      }else{

        lb_AEToxFlag <- tibble::tribble(
          ~Subject, ~rowid, ~AEToxFlag,
          "abc", 123, NA_character_
        )

      }



      # Finished getLabMatch for each row of ae_small_tmp Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Finished getAEMatch for each row of lab_small_tmp dataframe"))

      # Create lb_calc_tmp ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Create lb_calc_tmp dataframe"))
      lb_calc_tmp <- dplyr::left_join(lb_toxgrades, lb_AEToxFlag, by = c("Subject", "rowid"))

      # Insert Calculated Columns -----------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Insert Calculated Columns lb_calc_tmp dataframe"))
      lb_calc_tmp <- lb_calc_tmp %>%
        dplyr::mutate(xULN_orig = dplyr::if_else(.data$LabHigh > 0, .data$NumericValue/.data$LabHigh, NA_real_),
                      `Lab Name - Units` = paste(.data$AnalyteName, .data$LabUnits, sep = "-"),
                      `Lab Name - StdUnits` = paste(.data$AnalyteName, .data$StdUnits, sep = "-"),
                      RecordDate = as.Date(.data$RecordDate))
      # Calculate rolling max for xULN using a 7 day time window -----------
      liverlab <- lb_calc_tmp %>%
        dplyr::filter(.data$`Dictionary-Derived Lab Name` == "Alkaline Phosphatase" |
                        .data$`Dictionary-Derived Lab Name` == "Aspartate Aminotransferase" |
                        .data$`Dictionary-Derived Lab Name` == "Bilirubin" |
                        .data$`Dictionary-Derived Lab Name` == "Alanine Aminotransferase"  ) %>%
        dplyr::select(.data$Subject, .data$`Dictionary-Derived Lab Name`, .data$RecordDate, .data$xULN_orig, .data$rowid )

      lb_calc_tmp <- liverlab %>%
        dplyr::group_by(.data$Subject, .data$`Dictionary-Derived Lab Name`) %>%
        dplyr::arrange(.data$RecordDate, .by_group = T) %>%
        dplyr::mutate(xULN = runner::max_run(.data$xULN_orig, k = 8, idx = .data$RecordDate))  %>%
        dplyr::ungroup() %>%
        dplyr::left_join(lb_calc_tmp,., by = c("Subject", "Dictionary-Derived Lab Name", "RecordDate", "rowid","xULN_orig"))


      # Create labzscore -----------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Create labzscore dataframe"))
      labzscore <- lb_calc_tmp %>%
        dplyr::select(.data$NumericValue, .data$`Lab Name - Units`) %>%
        dplyr::group_by(.data$`Lab Name - Units`) %>%
        dplyr::mutate(`Z-Score` = (.data$NumericValue - mean(.data$NumericValue))/stats::sd(.data$NumericValue)) %>%
        dplyr::ungroup() %>%
        dplyr::distinct()

      #add in label attributes so rep_col_w_label doesn't error out
      attr(lab$LastChangeDate, "label") <- "LastChangeDate"
      attr(lab$WhatChanged, "label") <- "WhatChanged"
      
      # Create lb_calc ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Create lb_calc dataframe"))
      lb_calc <- dplyr::left_join(lb_calc_tmp, labzscore, by = c("NumericValue", "Lab Name - Units" ))


      # Create function to get labels from lab Dataframe rep_col_w_label -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","rep_col_w_label on lab dataframe"))
      rep_col_w_label <- function(myDF){
        n <- ncol(myDF)
        varlabel <- purrr::map_chr(1:n, function(x) unique(attr(myDF[[x]], "label")))
        varname <- colnames(myDF)
        labelookup <- cbind(varname ,varlabel)
        #colnames(myDF) <- labels_vector
        return(labelookup)
      }
      lblabel <- rep_col_w_label(lab)

      # Replace Column names with SAS Label Names ----------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Replace Column names with SAS Label Names lb_calc dataframe"))
      foundLabel <- tibble::as_tibble(lblabel)

      availMetadatavarlabels <- foundLabel %>%
        dplyr::select(.data$varlabel) %>%
        dplyr::pull()

      for (jj in seq_along(colnames(lb_calc))) {
        # If the "SASName" isn't in the `varlabel` found in the metadata, use the `varlabel`as label attribute, otherwise use the "SASName" as label attribute.
        if (!(colnames(lb_calc)[jj] %in% availMetadatavarlabels)) {
          myvar <- foundLabel %>%
            dplyr::filter(.data$varname == colnames(lb_calc)[jj]) %>%
            dplyr::select(.data$varlabel) %>%
            dplyr::pull()
          if ( length(myvar) > 0) {
            names(lb_calc)[jj] <- myvar
          } else {
            names(lb_calc)[jj] <- colnames(lb_calc)[jj]
          }
        } else {# just leave it as it is...
          names(lb_calc)[jj] <- colnames(lb_calc)[jj]
        }
      }


      # Assign lb_calc to calling envir ----------------------------------------------------
      assign("lb_calc", lb_calc, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," lb_calc returned"))

      # End of labCalc Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
