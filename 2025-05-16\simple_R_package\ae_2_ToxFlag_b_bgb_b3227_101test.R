#' @title ae_2_ToxFlag_b_bgb_b3227_101test
#' @description Create ae_to_lab_toxflag
#'
#' @param studyId This is a character field, studyId
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param ae This the ae dataframe
#' @param lb_toxgrades This is the lb_toxgrades dataframe
#' @param AnalytePT This is the AnalytePT dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return ae_to_lab_toxflag
#'
#' @export ae_2_ToxFlag_b_bgb_b3227_101test
#'
#' @importFrom dplyr case_when distinct filter if_else left_join mutate pull rowwise select ungroup
#' @importFrom checkmate assert_data_frame
#' @importFrom glue glue_collapse
#' @importFrom log4r error info warn
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom lubridate as_date days
#' @importFrom magrittr %>%
#' @importFrom stringr str_to_upper
#' @importFrom rlang .data
#' @importFrom tibble tribble
#'
#' @examples
#' \dontrun{
#' ae_2_ToxFlag_b_bgb_b3227_101test(studyId = studyIdVar,
#'                             tempLogger = CentralLogger,
#'                             jobdatetime = jobdatetime,
#'                             ae = ae,
#'                             lb_toxgrades = lb_toxgrades,
#'                             AnalytePT = AnalytePT, develop.f = develop.f,
#'                             vpath = vpath)
#'                              }
#'
#'
ae_2_ToxFlag_b_bgb_b3227_101test <- function(studyId, tempLogger, jobdatetime = jobdatetime, ae,
                                        lb_toxgrades, AnalytePT, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "ae_2_ToxFlag_b_bgb_b3227_101test"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))
      # Assert lb_toxgrades has min.rows and min.cols
      checkmate::assert_data_frame(lb_toxgrades, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm lb_toxgrades has min.rows and min.cols."))
      # Assert AnalytePT has min.rows and min.cols
      checkmate::assert_data_frame(AnalytePT, min.rows = 1, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm AnalytePT has min.rows and min.cols."))

      # AnalytePT_tmp Table -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating AnalytePT_tmp dataframe"))
      AnalytePT_tmp <- AnalytePT %>%
        dplyr::filter(.data$domain == "lab")

      # lab_tox_tmp Table -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating lab_tox_tmp dataframe"))
      lab_tox_tmp <-  lb_toxgrades %>%
        dplyr::filter(.data$`Dictionary-Derived Lab Name` %in% AnalytePT_tmp$lbtest) %>%
        dplyr::select(.data$Subject, .data$`Dictionary-Derived Lab Name`, .data$`LB Toxicity-CTCAE`, .data$RecordDate )

      # lab_tox_tmp_upper Table LB Toxicity-CTCAE is upper case----------------------------
      lab_tox_tmp_upper <- lab_tox_tmp %>%
        dplyr::mutate("LB Toxicity-CTCAE" =
                        stringr::str_to_upper(.data$`LB Toxicity-CTCAE`))

      # getLabMatch Function -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating getLabMatch Function"))
      getLabMatch <- function(sub_tmp, aeTermPT_tmp, aeStart_tmp, aeEnd_tmp, aeToxGr_tmp, aeToxGr_tmp_upper){
        lblist <- AnalytePT_tmp %>%
          dplyr::filter(.data$PT == aeTermPT_tmp) %>%
          dplyr::select(.data$lbtest) %>%
          dplyr::distinct() %>%
          dplyr::pull()

        nr <- lab_tox_tmp_upper %>%
          dplyr::filter(.data$Subject == sub_tmp &
                          .data$`Dictionary-Derived Lab Name` %in% lblist &
                          .data$`LB Toxicity-CTCAE` == aeToxGr_tmp_upper &
                          .data$RecordDate >= lubridate::as_date(aeStart_tmp) &
                          .data$RecordDate <= lubridate::as_date(aeEnd_tmp)) %>%
          nrow()

        matched_labs <- lab_tox_tmp %>%
          dplyr::filter(.data$`Dictionary-Derived Lab Name` %in% lblist &
                          .data$Subject == sub_tmp &
                          .data$RecordDate >= lubridate::as_date(aeStart_tmp) &
                          .data$RecordDate <= lubridate::as_date(aeEnd_tmp))
        nr2 <- matched_labs %>%
          nrow()

        grades <- matched_labs %>%
          dplyr::select(.data$`LB Toxicity-CTCAE`) %>%
          dplyr::distinct() %>%
          dplyr::pull() %>%
          glue::glue_collapse(sep = " ")

        if (nr > 0) {
          return_val <- "Yes"
        }else if (nr == 0 & nr2 > 0) {
          if (grades == "Not a CTCAE test") {
            return_val <- "Not defined by CTCAE5"
          } else if (grades == "Missing CTCAE grades") {
            return_val <- "Lab Tox Gr Not Calculated - Please investigate further"
          } else {
            return_val <- paste("AE toxgr", aeToxGr_tmp, "vs. Lab toxgr:", grades)
          }
        }else if (nr == 0 & nr2 == 0) {
          return_val <- paste("No lab near the AE Dates")
        }
        return(return_val)
      }

      # ae_small_tmp Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_small_tmp dataframe"))
      #UpdateVars ----
      ae_small_tmp <- ae %>%
        dplyr::filter(!is.na(.data$AESTDAT_INT)) %>%
        dplyr::filter(.data$AETERM_PT %in% AnalytePT_tmp$PT) %>%
        dplyr::mutate(stdt = .data$AESTDAT_INT - lubridate::days(3),
                      enddt = dplyr::if_else(!is.na(.data$AEENDAT_INT), .data$AEENDAT_INT + lubridate::days(3),
                                             .data$AESTDAT_INT + lubridate::days(7) ),
                      `AEToxStd` = dplyr::case_when(
                        grepl("1", .data$AETOXGR) ~ "Grade 1: Mild",
                        grepl("2", .data$AETOXGR) ~ "Grade 2: Moderate",
                        grepl("3", .data$AETOXGR) ~ "Grade 3: Severe",
                        grepl("4", .data$AETOXGR) ~ "Grade 4: Life-Threatening",
                        grepl("5", .data$AETOXGR) ~ "Grade 5: Fatal",
                        TRUE ~ as.character(.data$AETOXGR))
        ) %>%
        dplyr::mutate( `AEToxStd_UPPER` = stringr::str_to_upper(.data$`AEToxStd`)) %>%
        dplyr::select(.data$Subject, .data$RecordId, aePT = .data$AETERM_PT, .data$stdt, .data$enddt, .data$`AEToxStd`, .data$`AEToxStd_UPPER`)

      # Running getLabMatch for each row of ae_small_tmp Dataframe  -------------------------------------------------------------
       if (nrow(ae_small_tmp)>0){
        ae_small_tmp <- ae_small_tmp %>%
          dplyr::rowwise() %>%
          dplyr::mutate(LABToxFlag =
                          getLabMatch(sub_tmp = .data$Subject,
                                      aeTermPT_tmp = .data$aePT,
                                      aeStart_tmp = as.Date(.data$stdt),
                                      aeEnd_tmp = as.Date(.data$enddt),
                                      aeToxGr_tmp = .data$`AEToxStd`,
                                      aeToxGr_tmp_upper = .data$`AEToxStd_UPPER`)
          ) %>%
          dplyr::ungroup() %>%
          dplyr::select(.data$Subject, .data$RecordId, .data$stdt, .data$enddt, .data$LABToxFlag)

        #UpdateVars
        ae_to_lab_toxflag <- ae_small_tmp %>%
          dplyr::left_join(ae %>% dplyr::select(.data$Subject, .data$RecordId, .data$AETERM_PT),., by = c("Subject","RecordId")) %>%
          dplyr::mutate(LABToxFlag =
                          dplyr::if_else(!(.data$AETERM_PT) %in% AnalytePT_tmp$PT,
                                         "This AE is not converted by AnalytePT Table",
                                         .data$LABToxFlag)) %>%
          dplyr::select(-.data$AETERM_PT)}
      else{

        ae_to_lab_toxflag <- tibble::tribble(
          ~Subject, ~RecordId, ~stdt, ~enddt, ~LABToxFlag,
          "abc", 123, as.POSIXct(NA_character_),as.POSIXct(NA_character_), NA_character_
        )

      }

      # Finished getLabMatch for each row of ae_small_tmp Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Finished getLabMatch for each row of ae_small_tmp dataframe"))


      # Assign ae_to_lab_toxflag to calling envir ----------------------------------------------------
      assign("ae_to_lab_toxflag", ae_to_lab_toxflag, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," ae_to_lab_toxflag returned"))

      # End of ae_2_ToxFlag Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
