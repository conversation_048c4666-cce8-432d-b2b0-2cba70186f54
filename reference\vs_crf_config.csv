﻿study_name,r_name,crf_name,r_col_name,crf_col_name,crf_col_type,r_col_val_name,crf_col_val_name,crf_col_desc
b_bgb_b3227_101,eg1,eg,EGPERF,EGPERF_STD,character,Yes,Yes,Was an ECG performed?
b_bgb_b3227_101,eg1,eg,EGPERF,EGPERF_STD,character,No,No,Was an ECG performed?
b_bgb_b3227_101,eg1,eg,InstanceName,InstanceName,character,,,Name of the folder instance
b_bgb_b3227_101,eg1,eg,LastChangeDate,LastChangeDate,date,,,LastChangeDate
b_bgb_b3227_101,eg1,eg,RecordDate,RecordDate,date,,,RecordDate
b_bgb_b3227_101,eg1,eg,Subject,Subject,character,,,Unique subject identifier
b_bgb_b3227_101,eg1,eg,TESTDAT_INT,EGDAT_INT,date,,,Date of ECG
b_bgb_b3227_101,eg1,eg,WhatChanged,WhatChanged,character,,,WhatChanged
b_bgb_b3227_101,eg1,eg,instanceId,instanceId,numeric,,,Unique folder instance identifier
b_bgb_b3227_101,eg1,eg,stdvalue,QTCFAG_EGORRES,numeric,,,Aggregate QTcF interval
b_bgb_b3227_101,eg2,eg_trp,EGPERF,TRP_EGPERF_STD,character,Yes,Yes,Was an ECG performed?
b_bgb_b3227_101,eg2,eg_trp,EGPERF,TRP_EGPERF_STD,character,No,No,Was an ECG performed?
b_bgb_b3227_101,eg2,eg_trp,InstanceName,InstanceName,character,,,Name of the folder instance
b_bgb_b3227_101,eg2,eg_trp,LastChangeDate,LastChangeDate,date,,,LastChangeDate
b_bgb_b3227_101,eg2,eg_trp,RecordDate,RecordDate,date,,,RecordDate
b_bgb_b3227_101,eg2,eg_trp,Subject,Subject,character,,,Unique subject identifier
b_bgb_b3227_101,eg2,eg_trp,TESTDAT_INT,TRP_EGDAT_INT,date,,,Date of ECG
b_bgb_b3227_101,eg2,eg_trp,WhatChanged,WhatChanged,character,,,WhatChanged
b_bgb_b3227_101,eg2,eg_trp,instanceId,instanceId,numeric,,,Unique folder instance identifier
b_bgb_b3227_101,eg2,eg_trp,stdvalue,TRP_QTCFAG_EGORRES,numeric,,,Aggregate QTcF interval
b_bgb_b3227_101,vs1,vs,DiastolicBP_stdvalue,DIABP_VSORRES1,numeric,,,Diastolic blood pressure
b_bgb_b3227_101,vs1,vs,InstanceName,InstanceName,character,,,Name of the folder instance
b_bgb_b3227_101,vs1,vs,LastChangeDate,LastChangeDate,date,,,LastChangeDate
b_bgb_b3227_101,vs1,vs,RecordDate,RecordDate,date,,,RecordDate
b_bgb_b3227_101,vs1,vs,Subject,Subject,character,,,Unique subject identifier
b_bgb_b3227_101,vs1,vs,SystolicBP_stdvalue,SYSBP_VSORRES1,numeric,,,Systolic blood pressure
b_bgb_b3227_101,vs1,vs,TESTDAT_INT,VSDAT1_INT,date,,,Date and time of vital signs
b_bgb_b3227_101,vs1,vs,Timepoint,VSTPT1_STD,character,,,Time point
b_bgb_b3227_101,vs1,vs,VSYN,VSPERF1_STD,character,Yes,Yes,Were vital signs taken?
b_bgb_b3227_101,vs1,vs,VSYN,VSPERF1_STD,character,No,No,Were vital signs taken?
b_bgb_b3227_101,vs1,vs,WhatChanged,WhatChanged,character,,,WhatChanged
b_bgb_b3227_101,vs1,vs,instanceId,instanceId,numeric,,,Unique folder instance identifier
b_bgb_b3227_101,vs1,vs,temp_stdvalue,TEMP_VSORRES1_STD,numeric,,,Temperature
b_bgb_b3227_101,vs1,vs,temp_unit,TEMP_VSORRES1_STD_UN,character,,,Standard unit of temperature
b_bgb_b3227_101,vs1,vs,weight_stdvalue,WEIGHT_VSORRES1_STD,numeric,,,Weight
b_bgb_b3227_101,vs1,vs,weight_unit,WEIGHT_VSORRES1_STD_UN,character,,,Standard unit of weight
b_bgb_b3227_101,vs2,vs2,DiastolicBP_stdvalue,DIABP_VSORRES3,numeric,,,Diastolic blood pressure
b_bgb_b3227_101,vs2,vs2,InstanceName,InstanceName,character,,,Name of the folder instance
b_bgb_b3227_101,vs2,vs2,LastChangeDate,LastChangeDate,date,,,LastChangeDate
b_bgb_b3227_101,vs2,vs2,RecordDate,RecordDate,date,,,RecordDate
b_bgb_b3227_101,vs2,vs2,Subject,Subject,character,,,Unique subject identifier
b_bgb_b3227_101,vs2,vs2,SystolicBP_stdvalue,SYSBP_VSORRES3,numeric,,,Systolic blood pressure
b_bgb_b3227_101,vs2,vs2,TESTDAT_INT,VSDAT3_INT,date,,,Date and time of vital signs
b_bgb_b3227_101,vs2,vs2,Timepoint,VSTPT3_STD,character,,,Time point
b_bgb_b3227_101,vs2,vs2,VSYN,VSPERF3_STD,character,Yes,Yes,Were vital signs taken?
b_bgb_b3227_101,vs2,vs2,VSYN,VSPERF3_STD,character,No,No,Were vital signs taken?
b_bgb_b3227_101,vs2,vs2,WhatChanged,WhatChanged,character,,,WhatChanged
b_bgb_b3227_101,vs2,vs2,instanceId,instanceId,numeric,,,Unique folder instance identifier
b_bgb_b3227_101,vs2,vs2,temp_stdvalue,TEMP_VSORRES3_STD,numeric,,,Temperature
b_bgb_b3227_101,vs2,vs2,temp_unit,TEMP_VSORRES3_STD_UN,character,,,Standard unit of temperature
b_bgb_b3227_101,vs2,vs2,weight_stdvalue,WEIGHT_VSORRES3_STD,numeric,,,Weight
b_bgb_b3227_101,vs2,vs2,weight_unit,WEIGHT_VSORRES3_STD_UN,character,,,Standard unit of weight
b_bgb_b3227_101,vs3,vs_scr,DiastolicBP_stdvalue,DIABP_VSORRES,numeric,,,Diastolic blood pressure
b_bgb_b3227_101,vs3,vs_scr,InstanceName,InstanceName,character,,,Name of the folder instance
b_bgb_b3227_101,vs3,vs_scr,LastChangeDate,LastChangeDate,date,,,LastChangeDate
b_bgb_b3227_101,vs3,vs_scr,RecordDate,RecordDate,date,,,RecordDate
b_bgb_b3227_101,vs3,vs_scr,Subject,Subject,character,,,Unique subject identifier
b_bgb_b3227_101,vs3,vs_scr,SystolicBP_stdvalue,SYSBP_VSORRES,numeric,,,Systolic blood pressure
b_bgb_b3227_101,vs3,vs_scr,TESTDAT_INT,VSDAT_INT,date,,,Date of vital signs
b_bgb_b3227_101,vs3,vs_scr,VSYN,VSPERF_STD,character,Yes,Yes,Were vital signs taken?
b_bgb_b3227_101,vs3,vs_scr,VSYN,VSPERF_STD,character,No,No,Were vital signs taken?
b_bgb_b3227_101,vs3,vs_scr,WhatChanged,WhatChanged,character,,,WhatChanged
b_bgb_b3227_101,vs3,vs_scr,instanceId,instanceId,numeric,,,Unique folder instance identifier
b_bgb_b3227_101,vs3,vs_scr,temp_stdvalue,TEMP_VSORRES_STD,numeric,,,Temperature
b_bgb_b3227_101,vs3,vs_scr,temp_unit,TEMP_VSORRES_STD_UN,character,,,Standard unit of temperature
b_bgb_b3227_101,vs3,vs_scr,weight_stdvalue,WEIGHT_VSORRES_STD,numeric,,,Weight
b_bgb_b3227_101,vs3,vs_scr,weight_unit,WEIGHT_VSORRES_STD_UN,character,,,Standard unit of weight
