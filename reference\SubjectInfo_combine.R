library(readxl)
library(dplyr)
library(magrittr)
library(stringr)
library(tidyr)

ALS_field<-read_excel("BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx",sheet = "Fields") %>% 
  dplyr::select(.data$FormOID,.data$FieldOID,.data$SASLabel) %>% 
  dplyr::mutate(Form = tolower(.data$FormOID)) %>% 
  dplyr::select(-.data$FormOID) %>% 
  tidyr::drop_na()

ALS_form<- read_excel("BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx",sheet = "Forms") %>% 
  dplyr::select(.data$OID,.data$DraftFormName) %>% 
  dplyr::mutate(Form =tolower(.data$OID)) %>% 
  dplyr::select(-.data$OID) %>% 
  tidyr::drop_na()


var_label_form <- ALS_field %>% 
  dplyr::left_join(ALS_form, by = "Form") 

enr_summary <- var_label_form %>% 
  dplyr::filter(grepl("Enrollment",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Was the subject enrolled|Phase|Cohort|Dose Level",.data$SASLabel)) %>% 
  dplyr::mutate(part = "enr_summary")

subject_summary <- var_label_form %>% 
  dplyr::filter(grepl("Subject",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Country",.data$SASLabel)) %>% 
  dplyr::mutate(part = "subject_summary")

sd_summary <- var_label_form %>% 
  dplyr::filter(grepl("End of Study",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Date of subject completion/discontinuation from the study|primary reason|Other",.data$SASLabel)) %>% 
  dplyr::mutate(part = "sd_summary")

dd_summary <- var_label_form %>% 
  dplyr::filter(grepl("Death",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Date of death|Primary cause of death|Other",.data$SASLabel)) %>% 
  dplyr::mutate(part = "dd_summary")

treatments<- unique(sub("Study Drug Administration - ", "", var_label_form$DraftFormName[grepl("Study Drug Administration - ",  var_label_form$DraftFormName)])) 

dm_summary<- var_label_form %>% 
  dplyr::filter(grepl("Demographics",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Age|Sex|child-bearing potential|Ethnicity|American Indian or Alaska Native|^Asian$|Black or African American|Native Hawaiian or other Pacific Islander|^White$|^Other$|Not reported|Unknown",.data$SASLabel)) %>% 
  dplyr::mutate(part = "dm_summary")

rsrc_summary <- var_label_form %>% 
  dplyr::filter(grepl("Disease Assessment|Time-point Response Assessment",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Date of .*response",.data$SASLabel)) %>% 
  dplyr::mutate(part = "rsrc_summary")


candig_summary <- var_label_form %>% 
  dplyr::filter(grepl("Disease History",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Medical history term|Type of solid tumor|Disease type at study entry",.data$SASLabel)) %>% 
  dplyr::mutate(part = "candig_summary")

SubjectInfo_combine <- bind_rows(enr_summary,subject_summary,sd_summary,dd_summary,dm_summary,rsrc_summary,candig_summary) %>% 
  bind_cols(treatment = treatments)






















  