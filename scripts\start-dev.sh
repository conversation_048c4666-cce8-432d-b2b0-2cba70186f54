#!/bin/bash

echo "========================================"
echo " MDR R Package Generator - Development"
echo "========================================"
echo

cd "$(dirname "$0")/.."

echo "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Checking Python installation..."
if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
    echo "ERROR: Python is not installed or not in PATH"
    echo "Please install Python from https://python.org/"
    exit 1
fi

# Use python3 if available, otherwise python
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

echo "Checking if node_modules exists..."
if [ ! -d "node_modules" ]; then
    echo "Installing Node.js dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to install Node.js dependencies"
        exit 1
    fi
fi

echo "Checking if python-env exists..."
if [ ! -d "python-env" ]; then
    echo "Setting up development environment..."
    npm run setup-dev
    if [ $? -ne 0 ]; then
        echo "ERROR: Failed to setup development environment"
        exit 1
    fi
fi

echo
echo "Starting MDR R Package Generator in development mode..."
echo
echo "The application will open in a new window."
echo "Press Ctrl+C to stop the application."
echo

npm run dev

echo
echo "Application stopped."
