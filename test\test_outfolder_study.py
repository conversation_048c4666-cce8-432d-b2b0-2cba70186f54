#!/usr/bin/env python3
"""
Test script to verify outfolder_study generation in controller_generator.py
"""

from controller_generator import ControllerGenerator
import re

# Create a mock ALS file for testing
class MockALSFile:
    def getvalue(self):
        return b"mock data"

def test_outfolder_study_generation():
    """Test that outfolder_study is correctly generated in the R code."""

    # Test cases
    test_cases = [
        {
            'study_id': 'b_bgb_16673_101',
            'expected_outfolder_study': 'b.bgb.16673.101',
            'expected_path_pattern': r'/mnt/usrfiles/bgcrh/cp/blinded/bgb_16673/bgb_16673_101/prod/crts/sptfrvis/mdr\.b\.bgb\.16673\.101/current/analysis/'
        },
        {
            'study_id': 'u_bgb_16673_101',
            'expected_outfolder_study': 'u.bgb.16673.101',
            'expected_path_pattern': r'/mnt/usrfiles/bgcrh/cp/unblinded/bgb_16673/bgb_16673_101/prod/crts/sptfrvis/mdr\.u\.bgb\.16673\.101/current/analysis/'
        },
        {
            'study_id': 'b_bgb_b3227_101',
            'expected_outfolder_study': 'b.bgb.b3227.101',
            'expected_path_pattern': r'/mnt/usrfiles/bgcrh/cp/blinded/bgb_b3227/bgb_b3227_101/prod/crts/sptfrvis/mdr\.b\.bgb\.b3227\.101/current/analysis/'
        }
    ]

    for test_case in test_cases:
        try:
            # Create a temporary controller generator instance just to access the method
            temp_controller = ControllerGenerator.__new__(ControllerGenerator)

            # Generate a small portion of the controller code to test outfolder_study
            study_id = test_case['study_id']
            blind_type, study_name, base_study = temp_controller._get_path_components(study_id)

            # Generate the R code snippet that includes outfolder_study
            test_r_code = f"""
      # Create outfolder_study by replacing underscores with dots
      outfolder_study <- stringr::str_replace_all(studyIdVar, "_", ".")

      # Check if testing_outFolder is not null
      OutFolder <- paste0("/mnt/usrfiles/bgcrh/cp/{blind_type}/{study_name}/{base_study}/prod/crts/sptfrvis/mdr.",outfolder_study,"/current/analysis/")
"""

            print(f"✓ Test case for {test_case['study_id']}:")
            print(f"  - Expected outfolder_study: {test_case['expected_outfolder_study']}")
            print(f"  - Generated R code snippet:")
            print(test_r_code)

            # Verify the path pattern is correct
            expected_path = test_case['expected_path_pattern'].replace(r'\.', '.')
            print(f"  - Expected OutFolder path: {expected_path}")
            print()

        except Exception as e:
            print(f"✗ Test failed for {test_case['study_id']}: {str(e)}")
            return False

    print("All outfolder_study generation tests completed!")
    return True

if __name__ == "__main__":
    test_outfolder_study_generation()
