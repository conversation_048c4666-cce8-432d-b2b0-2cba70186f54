import os
import sys
import pandas as pd
from patient_profile_generator import PatientProfileGenerator

def create_mock_als_file(file_path, custom_response_values=False):
    """Create a mock ALS file with response values for testing."""
    # Create a writer to save the Excel file
    writer = pd.ExcelWriter(file_path, engine='openpyxl')
    
    # Create Forms sheet
    forms_data = {
        'OID': ['F.AE', 'F.CM', 'F.RS_R', 'F.PPROC'],
        'DraftFormName': [
            'Adverse Events', 
            'Prior/Concomitant Medications', 
            'Time-point Response Assessment', 
            'Prior/Concomitant Procedures/Surgeries'
        ],
        'Form': ['ae', 'cm', 'rs_r', 'pproc']
    }
    forms_df = pd.DataFrame(forms_data)
    forms_df.to_excel(writer, sheet_name='Forms', index=False)
    
    # Create Fields sheet
    fields_data = {
        'FormOID': [
            'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE',
            'F.CM', 'F.CM', 'F.CM', 'F.CM', 'F.CM',
            'F.RS_R', 'F.RS_R',
            'F.PPROC', 'F.PPROC'
        ],
        'FieldOID': [
            'AESTDAT', 'AEENDAT', 'AETERM', 'AETERM_PT', 'AETERM_SOC', 'AETOXGR', 'AESDTH', 'AESER',
            'CMSTDAT', 'CMENDAT', 'CMTRT', 'CMTRT_PROD', 'CMONGO',
            'RSORRES', 'RSDAT',
            'PRSTDAT', 'PRTRT'
        ],
        'SASLabel': [
            'AE Start date', 'AE End date', 'Adverse event', 'AE Dictionary-Derived Term', 'AE SOC', 'Toxicity grade', 'Result in death', 'Was adverse event serious',
            'CM Start date', 'CM End date', 'Medication name', 'CM Preferred Term', 'Ongoing',
            'Overall response', 'Date of overall response',
            'Date of procedure/surgery', 'Type or name of procedure/surgery'
        ]
    }
    fields_df = pd.DataFrame(fields_data)
    fields_df.to_excel(writer, sheet_name='Fields', index=False)
    
    # Create DataDictionaryEntries sheet with standard or custom response values
    if custom_response_values:
        # Custom response values
        data_dic_data = {
            'DataDictionaryName': ['RSORRES'] * 5,
            'CodedData': ['COMPLETE', 'PARTIAL', 'STABLE', 'PROGRESS', 'NOT_EVAL'],
            'UserDataString': [
                'Complete Response (COMPLETE)', 
                'Partial Response (PARTIAL)', 
                'Stable Disease (STABLE)', 
                'Progressive Disease (PROGRESS)', 
                'Not Evaluable (NOT_EVAL)'
            ]
        }
    else:
        # Standard response values
        data_dic_data = {
            'DataDictionaryName': ['RSORRES'] * 5,
            'CodedData': ['CR', 'PR', 'SD', 'PD', 'NE'],
            'UserDataString': [
                'Complete response (CR)', 
                'Partial response (PR)', 
                'Stable disease (SD)', 
                'Progressive disease (PD)', 
                'Not evaluable (NE)'
            ]
        }
    
    data_dic_df = pd.DataFrame(data_dic_data)
    data_dic_df.to_excel(writer, sheet_name='DataDictionaryEntries', index=False)
    
    # Save the Excel file
    writer.close()
    
    return file_path

def main():
    # Test with standard response values
    mock_als_file1 = "mock_als_standard_responses.xlsx"
    create_mock_als_file(mock_als_file1, custom_response_values=False)
    
    # Test with custom response values
    mock_als_file2 = "mock_als_custom_responses.xlsx"
    create_mock_als_file(mock_als_file2, custom_response_values=True)
    
    # Generate R functions for each case
    for i, file_path in enumerate([mock_als_file1, mock_als_file2], 1):
        generator = PatientProfileGenerator(file_path)
        
        # Print the response values from the mapping table
        rs_vars = generator._get_variable_mapping('rs_std')
        print(f"\nCase {i} - Response values from mapping table:")
        if 'response_values' in rs_vars:
            for user_data_string, coded_data in rs_vars['response_values'].items():
                print(f"  {user_data_string} -> {coded_data}")
        else:
            print("  No response values found in mapping table")
        
        # Generate the R function
        study_id = f"test_response_values_{i}"
        r_function = generator.generate_function(study_id)
        
        output_file = f"patientProfile_{study_id}_generated.R"
        with open(output_file, "w") as f:
            f.write(r_function)
        
        print(f"\nR function for case {i} generated and saved to '{output_file}'")
    
    # Clean up
    for file_path in [mock_als_file1, mock_als_file2]:
        os.remove(file_path)

if __name__ == "__main__":
    main()
