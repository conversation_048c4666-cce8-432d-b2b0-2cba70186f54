{"name": "mdr-r-package-generator", "version": "1.0.0", "description": "Desktop application for MDR R Package Generator - Streamline clinical trial data analysis with automated R function generation", "main": "main.js", "homepage": "./", "author": {"name": "Your Organization", "email": "<EMAIL>"}, "license": "MIT", "private": true, "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "node build-scripts/build.js", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "build-all": "electron-builder --win --mac --linux", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "setup-dev": "node build-scripts/setup-dev.js", "clean": "rimraf dist build python-env node_modules/.cache", "test": "echo \"No tests specified\" && exit 0", "lint": "echo \"No linting configured\" && exit 0", "postinstall": "electron-builder install-app-deps"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "rimraf": "^5.0.5"}, "dependencies": {"electron-log": "^5.0.1", "electron-updater": "^6.1.7", "find-free-port": "^2.0.0"}, "build": {"appId": "com.yourorg.mdr-r-package-generator", "productName": "MDR R Package Generator", "directories": {"output": "dist", "buildResources": "build-resources"}, "files": ["main.js", "preload.js", "start.py", "app.py", "requirements.txt", "*.py", "R package/**/*", "simple_R_package/**/*", "templates/**/*", "Config_MDR_var_replacement.xlsx", "*.R", "*.csv", "*.xlsx", "node_modules/**/*"], "extraResources": [{"from": "python-env", "to": "python-env", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build-resources/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "build-resources/icon.icns", "category": "public.app-category.developer-tools"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "build-resources/icon.png", "category": "Development"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "MDR R Package Generator"}, "dmg": {"title": "MDR R Package Generator", "backgroundColor": "#ffffff", "window": {"width": 600, "height": 400}}, "publish": null}}