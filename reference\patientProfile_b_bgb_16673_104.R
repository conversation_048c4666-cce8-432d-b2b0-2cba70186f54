#' @title patientProfile_b_bgb_16673_104
#' @description Create a Patient Profile
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param SubjectInfo This the SubjectInfo dataframe
#' @param ae This is the ae dataframe
#' @param cm This is the cm dataframe
#' @param rscll This is the rs_cll dataframe
#' @param rsnhl This is the rs_nhl dataframe
#' @param rswm This is the rs_wm dataframe
#' @param lb_calc This is the lb_calc dataframe
#' @param pproc This is the pr_pc dataframe
#' @param td_merged This is the td_merged dataframe
#' @param dose_merged This is the dose_merged dataframe
#' @param Treated This is the Treaded dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#' @param EDCextractDate This is the EDC extract Date
#'
#' @return PPandCorevars
#' @return PatientProfile
#' @return aePatientProfile
#'
#' @export patientProfile_b_bgb_16673_104
#'
#' @importFrom dplyr select mutate if_else row_number filter group_by distinct bind_rows left_join case_when
#' @importFrom tidyselect starts_with everything
#' @importFrom tidyr pivot_longer unite
#' @importFrom lubridate as_date
#' @importFrom tibble add_row
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#'patientProfile_b_bgb_16673_104(studyId = studyIdVar, sourceLocation = OutFolder,
#'                              tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                              EDCextractDate = EDCextractDate,
#'                              SubjectInfo = SubjectInfo, ae = ae,
#'                              cm = cm, rscll =rs_cll,rsnhl=rs_nhl, rswm = rs_wm,
#'                              lb_calc = lb_calc, pproc = pproc,
#'                              td_merged = td_merged, dose_merged = dose_merged,
#'                              Treated = Treated,
#'                              develop.f = develop.f, vpath = vpath)
#'}
#'
patientProfile_b_bgb_16673_104 <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime,
                                  EDCextractDate, SubjectInfo, ae, cm, rscll,rsnhl,rswm,
                                  lb_calc, pproc, td_merged, dose_merged,
                                  Treated, develop.f = develop.f, vpath = vpath ) {
  withCallingHandlers(
    expr = {
      calledFun = "patientProfile_b_bgb_16673_104"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert SubjectInfo has min.rows and min.cols
      checkmate::assert_data_frame(SubjectInfo, min.rows = 1, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm SubjectInfo has min.rows and min.cols."))
      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))
      # Assert cm has min.rows and min.cols
      checkmate::assert_data_frame(cm, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm cm has min.rows and min.cols."))
      # Assert rscll has min.rows and min.cols
      checkmate::assert_data_frame(rscll, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm rscll has min.rows and min.cols."))
      # Assert rsnhl has min.rows and min.cols
      checkmate::assert_data_frame(rsnhl, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm rsnhl has min.rows and min.cols."))
      # Assert rswm has min.rows and min.cols
      checkmate::assert_data_frame(rswm, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm rswm has min.rows and min.cols."))
      # Assert lb_calc has min.rows and min.cols
      checkmate::assert_data_frame(lb_calc, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm lb_calc has min.rows and min.cols."))
      # Assert pproc has min.rows and min.cols
      checkmate::assert_data_frame(pproc, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm proc has min.rows and min.cols."))
      # Assert td_merged has min.rows and min.cols
      checkmate::assert_data_frame(td_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm td_merged has min.rows and min.cols."))
      # Assert dose_merged has min.rows and min.cols
      checkmate::assert_data_frame(dose_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm dose_merged has min.rows and min.cols."))
      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))

      # ae_std Data Table ----------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_std dataframe"))
      ae_std <- ae %>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `AE Start Date` = .data$AESTDAT1_INT,
                      `AE End Date` = .data$AEENDAT1_INT,
                      `AE Dictionary-Derived Term` = .data$AETERM1_PT,
                      AETerm = .data$AETERM1,
                      `AE SOC` = .data$AETERM1_SOC,
                      `Tox Grade` = .data$AETOXGR1,
                      `AE Death` = .data$AESDTH1,
                      Serious = .data$AESER1,
                      Outcome = .data$AEOUT1,
                      DLT = .data$AEDLT1,
                      `Clinical interest` =.data$AECI
                      ) %>%
        dplyr::mutate(`CRF Origin` = "ae_mix",
                      Event = dplyr::if_else(is.na(.data$`AE Dictionary-Derived Term`),
                                             as.character(.data$`AETerm`),
                                             as.character(.data$`AE Dictionary-Derived Term`)),
                      rowid = paste("ae", dplyr::row_number()),
                      domain = "ae")

      # cm_std Data Table ----------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating cm_std dataframe"))
      cm_std <- cm %>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `CM Start Date` = .data$CMSTDAT1_INT,
                      `CM End Date` = .data$CMENDAT1_INT,
                      `CM Term` = .data$CMTRT1,
                      `CM Preferred Term` = .data$CMTRT1_PROD,
                      `Ongoing` = .data$CMONGO1) %>%
        dplyr::mutate(`CRF Origin` = "cm", Event = dplyr::if_else(is.na(.data$`CM Preferred Term`),
                                                                  as.character(.data$`CM Term`),
                                                                  as.character(.data$`CM Preferred Term`)),
                      rowid = paste("cm", dplyr::row_number()),
                      domain = "cm")

      # rs_std Data Table ----------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating rs_std dataframe"))

      #prepping rscll
      rscll_std <- rscll %>%
        dplyr::mutate(`Overall response (Coded)` = dplyr::case_when(.data$RSORRES3 == "Complete response (CR)" ~ "CR",
                                                                    .data$RSORRES3 =="Complete response with incomplete marrow recovery (CRi)" ~"CRi",
                                                                    .data$RSORRES3 == "Nodular partial response" ~ "nPR",
                                                                    .data$RSORRES3 =="Partial response (PR)" ~"PR",
                                                                    .data$RSORRES3 == "Partial response with lymphocytosis (PR-L)"  ~ "PR-L",
                                                                    .data$RSORRES3 == "Stable disease" ~ "SD",
                                                                    .data$RSORRES3 == "Progressive disease" ~ "PD",
                                                                    .data$RSORRES3 == "Not evaluable" ~ "NE",
                                                                    TRUE ~ as.character(.data$RSORRES3) # Not Done, Indetermine due to dose hold - use default values
        ))%>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `Date of Response Interpolated` = .data$MinCreated,
                      .data$`Overall response (Coded)` ) %>%
        dplyr::mutate(`CRF Origin` = "rs_cll",
                      Event = "rs",
                      rowid = paste("rs", dplyr::row_number()),
                      domain = "rs",
                      EventType = "rs",
                      Date = lubridate::as_date(.data$`Date of Response Interpolated`))

      ####preping rsnhl
      rsnhl_std <- rsnhl%>%
        dplyr::mutate(`Overall response (Coded)` = dplyr::case_when(.data$OVRLRESP_RSORRES4== "Partial response (PR)"  ~ "PR",
                                                                    .data$OVRLRESP_RSORRES4== "Complete response (CR)" ~ "CR",
                                                                    .data$OVRLRESP_RSORRES4== "Stable disease (SD)" ~ "SD",
                                                                    .data$OVRLRESP_RSORRES4== "Progressive disease (PD)" ~ "PD",
                                                                    .data$OVRLRESP_RSORRES4== "Not evaluable" ~ "NE",
                                                                    TRUE ~ as.character(.data$OVRLRESP_RSORRES4) # Not Done, Indetermine due to dose hold - use default values
        ))%>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `Date of Response Interpolated` = .data$MinCreated,
                      .data$`Overall response (Coded)` ) %>%
        dplyr::mutate(`CRF Origin` = "rs_nhl",
                      Event = "rs",
                      rowid = paste("rs", dplyr::row_number()),
                      domain = "rs",
                      EventType = "rs",
                      Date = lubridate::as_date(.data$`Date of Response Interpolated`))

      ####preping rswm
      rswm_std <- rswm%>%
        dplyr::mutate(`Overall response (Coded)` = dplyr::case_when(.data$RSORRES2== "Partial response (PR)"  ~ "PR",
                                                                    .data$RSORRES2== "Complete response (CR)" ~ "CR",
                                                                    .data$RSORRES2== "Very good partial response (VGPR)" ~ "VGPR",
                                                                    .data$RSORRES2== "Minor response (MR)" ~ "MR",
                                                                    .data$RSORRES2== "Stable disease (SD)" ~ "SD",
                                                                    .data$RSORRES2== "Progressive disease (PD)" ~ "PD",
                                                                    .data$RSORRES2== "Not evaluable" ~ "NE",
                                                                    TRUE ~ as.character(.data$RSORRES2) # Not Done, Indetermine due to dose hold - use default values
        ))%>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `Date of Response Interpolated` = .data$MinCreated,
                      .data$`Overall response (Coded)` ) %>%
        dplyr::mutate(`CRF Origin` = "rs_wm",
                      Event = "rs",
                      rowid = paste("rs", dplyr::row_number()),
                      domain = "rs",
                      EventType = "rs",
                      Date = lubridate::as_date(.data$`Date of Response Interpolated`))


      rs_std <- dplyr::bind_rows(rsnhl_std,rscll_std,rswm_std)


      # lab_std Data Table ----------------------------------------------------------------
      # UpdateVars if `LB Toxicity-CTCAE` is not available from ctcae scripts
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating lb_std dataframe"))
      lb_std <- lb_calc %>%
        dplyr::select(.data$`Subject name or identifier`,
                      .data$AnalyteName,
                      .data$`Clinical date of record (ex: visit date)`,
                      .data$NumericValue,
                      .data$LabUnits,
                      .data$StdValue,
                      .data$StdUnits,
                      `Tox Grade` = .data$`LB Toxicity-CTCAE`,
                      .data$`Lab Category` ) %>%
        dplyr::mutate(`CRF Origin` = "lb",
                      Event = paste(.data$AnalyteName),
                      rowid = paste("lb", dplyr::row_number()),
                      domain = "lab",
                      EventType = "lab",
                      Date = lubridate::as_date(.data$`Clinical date of record (ex: visit date)`))
      # proc_std Data Table ----------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating proc_std dataframe"))
      pproc_std <- pproc %>%

         dplyr::select(`Subject name or identifier` = .data$Subject,
                      `Date of Procedure/Surgery` = .data$PRSTDAT2_INT,
                      `Type or Name of Procedure` = .data$PRTRT2) %>%
        dplyr::mutate(`CRF Origin` = "pr_pc",
                      Event = as.character(.data$`Type or Name of Procedure`),
                      rowid = paste("proc", dplyr::row_number()),
                      domain = "proc",
                      EventType = "proc",
                      Date = lubridate::as_date(.data$`Date of Procedure/Surgery`))

      # FirstDoseDate Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating FirstDoseDate dataframe"))
      FirstDoseDate <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data$FirstDoseDate)

       # sd_std Data Table  ----------------------------------------------------------------
      # UpdateVars (CRF Origin if different from sd)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating sd_std dataframe"))
      sd_std <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`EOS Date`, .data$`EOS Reason` ) %>%
        dplyr::filter(!is.na(.data$`EOS Date`) |
                        !is.na(.data$`EOS Reason`) ) %>%
        dplyr::mutate(`CRF Origin` = "sd", domain = "EOT/EOS", subdomain = "EOS")
      # td_std Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_std dataframe"))
      td_std <- td_merged %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`EOT Reason`, .data$`EOT Date`, .data$Treatment,
                      .data$`CRF Origin`) %>%
        dplyr::mutate(domain = "EOT/EOS", subdomain = "EOT")
      # death_std Data Table  ----------------------------------------------------------------
      # UpdateVars (CRF Origin if different from sd)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating death_std dataframe"))
      death_std <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`Death Date`, .data$`Death Cause` ) %>%
        dplyr::filter(!is.na(.data$`Death Date`) |
                        !is.na(.data$`Death Cause`) ) %>%
        dplyr::mutate(`CRF Origin` = "dd", domain = "EOT/EOS", subdomain = "Death")

      # ex_std Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_std dataframe"))
      ex_std <- dose_merged %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`Dose Start Date (Interpolated)`,
                      .data$`Dose End Date (Interpolated)`, .data$`Planned Dose`, .data$`Actual Dose`, .data$`Dose Units`, .data$`Cycle Number`,
                      .data$`Was Dose Missed?`, .data$`Reason Dose Missed`, .data$`Was Dose Interrupted/delayed/withheld?`,
                      .data$`Reason Dose Interrupted/delayed/withheld`, .data$`Was Dose Modified/reduced/decreased?`,
                      .data$`Reason Dose Modified/reduced/decreased`,  .data$`Was Dose Discontinued?`, .data$`Reason Dose Discontinued`,
                      .data$Treatment, .data$`CRF Origin`) %>%
        tidyr::unite(col = "Event" , c("Treatment", "Planned Dose"), sep = "-", remove = FALSE) %>%
        tidyr::unite(col = "Event" , c("Event", "Dose Units"), sep = "", remove = FALSE) %>%
        dplyr::select(-.data$Event, tidyselect::everything()) %>%
        dplyr::mutate(rowid = paste("ex", dplyr::row_number()), domain = "dose")
      # ae1 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae1 dataframe"))
      ae1 <- ae_std %>%
        dplyr::mutate(EventType = "start",  Date = lubridate::as_date(.data$`AE Start Date`))
      # ae2 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae2 dataframe"))
     ae2 <-  ae_std %>%
        dplyr::left_join(.,SubjectInfo %>% dplyr::select(.data$`Subject name or identifier`, .data$`Death Date`, .data$`EOS Date`), by = c("Subject name or identifier")) %>%
        dplyr::mutate(EventType = dplyr::case_when(
          !is.na(.data$`AE End Date`) ~ "end",
          is.na(.data$`AE End Date`) & !is.na(.data$`Death Date`) ~ "death",
          is.na(.data$`AE End Date`) & is.na(.data$`Death Date`) ~ "ongoing",
          TRUE ~ NA_character_),
          Date = dplyr::case_when(
            !is.na(.data$`AE End Date`) ~ lubridate::as_date(.data$`AE End Date`),
            is.na(.data$`AE End Date`) & !is.na(.data$`Death Date`) ~ lubridate::as_date(.data$`Death Date`),
            is.na(.data$`AE End Date`) & !is.na(.data$`EOS Date`) ~ lubridate::as_date(.data$`EOS Date`),
            is.na(.data$`AE End Date`) & is.na(.data$`Death Date`) & is.na(.data$`EOS Date`) ~ lubridate::as_date(EDCextractDate),
            TRUE ~ lubridate::as_date(NA))
        ) %>%
        dplyr::select(-.data$`Death Date`, -.data$`EOS Date`)

      # cm1 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating cm1 dataframe"))
      cm1 <- cm_std %>%
        dplyr::mutate(EventType = "start",  Date = lubridate::as_date(.data$`CM Start Date`))
      # cm2 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating cm2 dataframe"))
      cm2 <- cm_std %>%
        dplyr::left_join(.,SubjectInfo %>% dplyr::select(.data$`Subject name or identifier`, .data$`Death Date`, .data$`EOS Date`), by = c("Subject name or identifier")) %>%
        dplyr::mutate(EventType = dplyr::if_else(.data$Ongoing == "Yes", "ongoing", "end"),
                      Date = dplyr::case_when(
                        !is.na(.data$`CM End Date`) ~ lubridate::as_date(.data$`CM End Date`),
                        is.na(.data$`CM End Date`) & !is.na(.data$`Death Date`) ~ lubridate::as_date(.data$`Death Date`),
                        is.na(.data$`CM End Date`) & !is.na(.data$`EOS Date`) ~ lubridate::as_date(.data$`EOS Date`),
                        is.na(.data$`CM End Date`) & is.na(.data$`Death Date`)& is.na(.data$`EOS Date`) ~ lubridate::as_date(EDCextractDate),
                        TRUE ~ lubridate::as_date(NA)))%>%
        dplyr::select(-.data$`Death Date`, -.data$`EOS Date`)
      # ex1 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex1 dataframe"))
      ex1 <- ex_std %>%
        dplyr::mutate(EventType = "start",  Date = lubridate::as_date(.data$`Dose Start Date (Interpolated)`))
      # ex2 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex2 dataframe"))
      ex2 <- ex_std %>%
        dplyr::mutate(EventType = "end",  Date = lubridate::as_date(.data$`Dose End Date (Interpolated)`))

      # PPandCorevars Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating PPandCorevars dataframe"))
  PPandCorevars <- dplyr::bind_rows(ae1, ae2, cm1, cm2, ex1, ex2, rs_std, lb_std,
                                        pproc_std, sd_std, death_std, td_std)
      # Add label to remove warning on join
      attr(PPandCorevars$`Subject name or identifier` , "label") <- "Subject name or identifier"

     PPandCorevars <- dplyr::left_join(PPandCorevars, FirstDoseDate,
                                        by = "Subject name or identifier") %>%
        dplyr::mutate(`EOS Day` = dplyr::if_else(!is.na(.data$`EOS Date`),
                                                 as.integer(difftime(.data$`EOS Date`,
                                                                     .data$FirstDoseDate,
                                                                     units = "days")), NULL),
                      `EOT Day` = dplyr::if_else(!is.na(.data$`EOT Date`),
                                                 as.integer(difftime(.data$`EOT Date`,
                                                                     .data$FirstDoseDate,
                                                                     units = "days")), NULL),
                      `Death Day` = dplyr::if_else(!is.na(.data$`Death Date`),
                                                   as.integer(difftime(.data$`Death Date`,
                                                                       .data$FirstDoseDate,
                                                                       units = "days")), NULL),
                      StudyDay = dplyr::case_when(
                        !is.na(.data$`EOT Day`) ~ .data$`EOT Day`,
                        !is.na(.data$`EOS Day`) & .data$subdomain == "EOS" ~ .data$`EOS Day`,
                        !is.na(.data$`Death Day`)& .data$subdomain == "Death"~ .data$`Death Day`,
                        TRUE ~ as.integer(difftime(.data$Date, lubridate::as_date(.data$FirstDoseDate), units = "days"))
                      ))


       PatientProfile <- PPandCorevars %>%
          dplyr::select(
          .data$`Subject name or identifier`, .data$Event, .data$EventType, .data$Date, .data$Serious, .data$`AE Dictionary-Derived Term`, .data$`AE SOC`,
          .data$`Tox Grade`, .data$DLT,.data$`Clinical interest`,.data$Outcome, .data$`CM Preferred Term`,.data$`CM Term`,
          .data$`Planned Dose`, .data$`Actual Dose`, .data$`Dose Units`, .data$`Was Dose Missed?`, .data$`Reason Dose Missed`, .data$`Was Dose Interrupted/delayed/withheld?`,
          .data$`Reason Dose Interrupted/delayed/withheld`, .data$`Was Dose Modified/reduced/decreased?`,
          .data$`Reason Dose Modified/reduced/decreased`,  .data$`Was Dose Discontinued?`, .data$`Reason Dose Discontinued`,
          .data$`Overall response (Coded)`, .data$`Lab Category`,.data$AnalyteName, .data$NumericValue, .data$LabUnits, .data$StdValue, .data$StdUnits,.data$`Type or Name of Procedure`,
          .data$`EOS Day`, .data$`EOS Reason`, .data$`EOT Day`, .data$`EOT Reason`, .data$`Death Day`, .data$`Death Cause`, .data$StudyDay,  .data$`CRF Origin`, .data$rowid, .data$domain) %>%
        tibble::add_row(
          `Subject name or identifier` = NA_character_,
          `Event` = ".All",
          `EventType` = ".All",
          `Serious` = ".All",
          `AE Dictionary-Derived Term`= ".All",
          `AE SOC`= ".All",
          `Tox Grade` = ".All",
          `DLT` = ".All",
          `Clinical interest` =".All",
          `Outcome` = ".All",
          `CM Preferred Term`= ".All",
          `CM Term` = ".All",
          `Planned Dose` = ".All",
          `Actual Dose` = ".All",
          `Dose Units` = ".All",
          `Was Dose Missed?` = ".All",
          `Reason Dose Missed` = ".All",
          `Was Dose Interrupted/delayed/withheld?` = ".All",
          `Reason Dose Interrupted/delayed/withheld` = ".All",
          `Was Dose Modified/reduced/decreased?` = ".All",
          `Reason Dose Modified/reduced/decreased` = ".All",
          `Was Dose Discontinued?` = ".All",
          `Reason Dose Discontinued` = ".All",
          `Overall response (Coded)` = ".All" ,
          `Lab Category` = ".All" ,
           AnalyteName= ".All" ,
          NumericValue = 1.1,
          LabUnits = ".All",
          StdValue = 1.1,
          StdUnits =  ".All",
          `Type or Name of Procedure` = ".All",
          `EOS Day` = 1,
          `EOS Reason` = ".All",
          `EOT Day` = 1,
          `EOT Reason` = ".All",
          `Death Day`= 1,
          `Death Cause`= ".All",
          StudyDay = 1,
          `CRF Origin` = ".All",
          rowid = ".All",
          domain = "EOT/EOS",
          .before = 1
        )

      # aePatientProfile for the standard high-level MDR -------
      aePatientProfile <- PPandCorevars %>%
        dplyr::filter(.data$`CRF Origin` == "ae_mix") %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`AE Start Date`, .data$`AE End Date`,
                      .data$`AE Dictionary-Derived Term`, .data$AETerm, .data$`AE SOC`, .data$`Tox Grade`, .data$Outcome,
                      .data$Serious, .data$`AE Death`,  .data$rowid, .data$EventType, .data$StudyDay, .data$DLT,.data$`Clinical interest`) %>%
        dplyr::mutate(`AE Duration (days)` = dplyr::if_else(!is.na(.data$`AE Start Date`) & !is.na(.data$`AE End Date`),
                                                            as.integer(difftime(.data$`AE End Date`,
                                                                                .data$`AE Start Date`,
                                                                                units = "days")), NULL) ) %>%
        dplyr::left_join(Treated %>%
                           dplyr::select(.data$`Subject name or identifier`, .data$Treated) , by = "Subject name or identifier") %>%
        dplyr::filter(.data$Treated == "Yes")




      # Assign PPandCorevars to calling envir ----------------------------------------------------
      assign("PPandCorevars", PPandCorevars, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," PPandCorevars returned"))

      # Assign PatientProfile to calling envir ----------------------------------------------------
      assign("PatientProfile", PatientProfile, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," PatientProfile returned"))

      # Assign aePatientProfile to calling envir ----------------------------------------------------
      assign("aePatientProfile", aePatientProfile, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," aePatientProfile returned"))

      # End of patientProfile Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
