#' @title tdMerge_b_bgb_b3227_101
#' @description Merge all treatment discontinuation (td) crfs together
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param td_1 This the first td dataframe
#' @param td_2 This the second td dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return td_merged
#'
#' @export tdMerge_b_bgb_b3227_101
#'
#' @importFrom dplyr mutate group_by ungroup
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#'tdMerge_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
#'                       tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                       td_1 = td, td_2 = td2,
#'                       develop.f = develop.f, vpath = vpath)
#'}
#'
tdMerge_b_bgb_b3227_101 <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime, td_1, td_2, develop.f = develop.f, vpath = vpath){

  withCallingHandlers(
    expr = {
      calledFun = "tdMerge_b_bgb_b3227_101"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert td_1 has min.rows and min.cols
      checkmate::assert_data_frame(td_1, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm td_1 has min.rows and min.cols."))
      # Assert td_2 has min.rows and min.cols
      checkmate::assert_data_frame(td_2, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm td_2 has min.rows and min.cols."))


      # td_1: Add CRF Origin and Treatment names -----
      # UpdateVars for Treatment and CRF Origin
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_1 dataframe"))
      
      if (nrow(td_1) > 0){
        td_1 <- td_1 %>%
          #for some studies, treatment might exist in a column
          #if the td form has multiple drugs, and no treatment column
          #assign "Combo" to Treatment value.
          dplyr::mutate(Treatment = "Tislelizumab",
                        `CRF Origin` = "td") %>%
          dplyr::group_by(.data$Subject) %>%
          dplyr::mutate("EOT Date" = min(.data$TDEDAT_INT)) %>%
          dplyr::ungroup()
      }else {
        td_1 <- td_1 %>%
          dplyr::mutate(Treatment = character(0),
                        `CRF Origin` = character(0),
                        `EOT Date` = as.Date(integer(0),
                                             origin = "1970-01-01"))
      }

      # Assign new labels to td_1 ----
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Assign new labels to td_1 dataframe"))
      attr(td_1$TDEDAT, "label") <- "Date of Treatment Discontinuation"
      attr(td_1$TDEDAT_INT, "label") <- "Date of Treatment Discontinuation (Interpolated)"
      attr(td_1$TDRSN, "label") <- "EOT Reason"

      td_1 <- GSDSUtilities::raiseLabels(td_1, "label", isNullC = NA)

####################
      # td_2: Add CRF Origin and Treatment names -----
      # UpdateVars for Treatment and CRF Origin
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_2 dataframe"))

      if (nrow(td_2) > 0) {
        td_2 <- td_2 %>%
          #for some studies, treatment might exist in a column
          #if the td form has multiple drugs and no treatment column
          #assign "Combo" to Treatment value.
          dplyr::mutate(Treatment = "A1217",
                        `CRF Origin` = "td2") %>%
          dplyr::group_by(.data$Subject) %>%
          dplyr::mutate("EOT Date" = min(.data$TDDAT2_INT)) %>%
          dplyr::ungroup()
      }else {
        td_2 <- td_2 %>%
          dplyr::mutate(Treatment = character(0),
                        `CRF Origin` = character(0),
                        `EOT Date` = as.Date(integer(0),
                                             origin = "1970-01-01"))
      }

      # Assign new labels to td_1 ----
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Assign new labels to td_2 dataframe"))
      attr(td_2$TDDAT2, "label") <- "Date of Treatment Discontinuation"
      attr(td_2$TDDAT2_INT, "label") <- "Date of Treatment Discontinuation (Interpolated)"
      attr(td_2$TDRSN, "label") <- "EOT Reason"

     td_2 <- GSDSUtilities::raiseLabels(td_2, "label", isNullC = NA)

###############
      # RaiseLabels (if avail) as the Column name ------------------------------------------------------------------------------
      # UpdateVars & UpdateScript
      # If multiple td forms exist, repeat steps above as in td_1:
      # Perform row binds after raiseLabels for each td
      # i.e. td_merged <- dplyr::bind_rows(td_1,td2,...)

     # log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of td_1 dataframe"))
     td_merged <- dplyr::bind_rows(td_1,td_2)

      # Assign td_merged to calling envir ----------------------------------------------------
      assign("td_merged", td_merged, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," td_merged returned"))

      # End of tdMerge Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },
    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}


