import os
import sys
import pandas as pd
from patient_profile_generator import PatientProfileGenerator

def create_mock_als_file(file_path):
    """Create a mock ALS file with various form names for testing."""
    # Create a writer to save the Excel file
    writer = pd.ExcelWriter(file_path, engine='openpyxl')
    
    # Create Forms sheet with various form names
    forms_data = {
        'OID': [
            'F.AE', 'F.AE_SUMM', 'F.CM', 'F.CM_SUMM', 
            'F.RS_R', 'F.RS_SUMM', 'F.PPROC', 'F.PPROC_SUMM'
        ],
        'DraftFormName': [
            'Adverse Events', 'Adverse Events Summary',
            'Prior/Concomitant Medications', 'Prior/Concomitant Medications Summary',
            'Time-point Response Assessment', 'Response Assessment Summary',
            'Prior/Concomitant Procedures/Surgeries', 'Prior/Concomitant Procedures/Surgeries Summary'
        ],
        'Form': [
            'ae', 'ae_summ', 'cm', 'cm_summ', 
            'rs_r', 'rs_summ', 'pproc', 'pproc_summ'
        ]
    }
    forms_df = pd.DataFrame(forms_data)
    forms_df.to_excel(writer, sheet_name='Forms', index=False)
    
    # Create Fields sheet with fields for all forms
    fields_data = {
        'FormOID': [],
        'FieldOID': [],
        'SASLabel': []
    }
    
    # Add fields for Adverse Events
    fields_data['FormOID'].extend(['F.AE'] * 8)
    fields_data['FieldOID'].extend(['AESTDAT', 'AEENDAT', 'AETERM', 'AETERM_PT', 'AETERM_SOC', 'AETOXGR', 'AESDTH', 'AESER'])
    fields_data['SASLabel'].extend([
        'AE Start date', 'AE End date', 'Adverse event', 'AE Dictionary-Derived Term', 
        'AE SOC', 'Toxicity grade', 'Result in death', 'Was adverse event serious'
    ])
    
    # Add fields for Adverse Events Summary
    fields_data['FormOID'].extend(['F.AE_SUMM'] * 3)
    fields_data['FieldOID'].extend(['AESUMM1', 'AESUMM2', 'AESUMM3'])
    fields_data['SASLabel'].extend(['AE Summary 1', 'AE Summary 2', 'AE Summary 3'])
    
    # Add fields for Prior/Concomitant Medications
    fields_data['FormOID'].extend(['F.CM'] * 5)
    fields_data['FieldOID'].extend(['CMSTDAT', 'CMENDAT', 'CMTRT', 'CMTRT_PROD', 'CMONGO'])
    fields_data['SASLabel'].extend([
        'CM Start date', 'CM End date', 'Medication name', 'CM Preferred Term', 'Ongoing'
    ])
    
    # Add fields for Response Assessment
    fields_data['FormOID'].extend(['F.RS_R'] * 2)
    fields_data['FieldOID'].extend(['RSORRES', 'RSDAT'])
    fields_data['SASLabel'].extend(['Overall response', 'Date of overall response'])
    
    # Add fields for Procedures
    fields_data['FormOID'].extend(['F.PPROC'] * 2)
    fields_data['FieldOID'].extend(['PRSTDAT', 'PRTRT'])
    fields_data['SASLabel'].extend(['Date of procedure/surgery', 'Type or name of procedure/surgery'])
    
    fields_df = pd.DataFrame(fields_data)
    fields_df.to_excel(writer, sheet_name='Fields', index=False)
    
    # Create DataDictionaryEntries sheet
    data_dic_data = {
        'DataDictionaryName': ['RSORRES'] * 5,
        'CodedData': ['CR', 'PR', 'SD', 'PD', 'NE'],
        'UserDataString': [
            'Complete response (CR)', 
            'Partial response (PR)', 
            'Stable disease (SD)', 
            'Progressive disease (PD)', 
            'Not evaluable (NE)'
        ]
    }
    data_dic_df = pd.DataFrame(data_dic_data)
    data_dic_df.to_excel(writer, sheet_name='DataDictionaryEntries', index=False)
    
    # Save the Excel file
    writer.close()
    
    return file_path

def main():
    # Create a mock ALS file with various form names
    mock_als_file = "mock_als_with_summary_forms.xlsx"
    create_mock_als_file(mock_als_file)
    
    # Create a PatientProfileGenerator instance
    generator = PatientProfileGenerator(mock_als_file)
    
    # Print the form names that were selected for each part
    print("\nForms selected for ae_std:")
    ae_std_df = generator.patient_profile_df[generator.patient_profile_df['part'] == 'ae_std']
    print(ae_std_df[['DraftFormName', 'Form']].drop_duplicates().to_string(index=False))
    
    print("\nForms selected for cm_std:")
    cm_std_df = generator.patient_profile_df[generator.patient_profile_df['part'] == 'cm_std']
    print(cm_std_df[['DraftFormName', 'Form']].drop_duplicates().to_string(index=False))
    
    print("\nForms selected for rs_std:")
    rs_std_df = generator.patient_profile_df[generator.patient_profile_df['part'] == 'rs_std']
    print(rs_std_df[['DraftFormName', 'Form']].drop_duplicates().to_string(index=False))
    
    print("\nForms selected for pproc_std:")
    pproc_std_df = generator.patient_profile_df[generator.patient_profile_df['part'] == 'pproc_std']
    print(pproc_std_df[['DraftFormName', 'Form']].drop_duplicates().to_string(index=False))
    
    # Generate the R function
    study_id = "test_form_filtering"
    r_function = generator.generate_function(study_id)
    
    output_file = f"patientProfile_{study_id}_generated.R"
    with open(output_file, "w") as f:
        f.write(r_function)
    
    print(f"\nR function generated and saved to '{output_file}'")
    
    # Clean up
    os.remove(mock_als_file)

if __name__ == "__main__":
    main()
