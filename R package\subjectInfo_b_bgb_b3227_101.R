#' @title subjectInfo_b_bgb_b3227_101
#' @description Create a Subject Info file and dataframe
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param dose_merged This the dose_merged dataframe
#' @param enr This is the enr dataframe
#' @param subject This is the subject dataframe
#' @param sd This is the sd dataframe
#' @param td_merged This is the td_merged dataframe
#' @param dm This is the dm dataframe
#' @param rsrc This is the rsrc dataframe
#' @param candig This is the candig dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return SubjectInfo
#'
#' @export subjectInfo_b_bgb_b3227_101
#'
#' @importFrom dplyr select distinct mutate case_when filter group_by summarise left_join vars mutate_at n_distinct count pull
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom tidyr pivot_wider
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data :=
#' @importFrom lubridate as_datetime
#'
#' @examples
#' \dontrun{
#'subjectInfo_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
#'                           tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                           dose_merged = dose_merged, enr = enr, subject = subject,
#'                           sd = sd, td_merged = td_merged, dm = dm,
#'                           rsrc = rsrc, candig = candig,
#'                           develop.f = develop.f, vpath = vpath)
#' }
#'
#'
subjectInfo_b_bgb_b3227_101 <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime, dose_merged,
                                       enr, subject, sd, td_merged, dm, rsrc, candig, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "subjectInfo_b_bgb_b3227_101"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert dose_merged has min.rows and min.cols
      checkmate::assert_data_frame(dose_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm dose_merged has min.rows and min.cols."))
      # Assert enr has min.rows and min.cols
      checkmate::assert_data_frame(enr, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm enr has min.rows and min.cols."))
      # Assert subject has min.rows and min.cols
      checkmate::assert_data_frame(subject, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm subject has min.rows and min.cols."))
      # Assert sd has min.rows and min.cols
      checkmate::assert_data_frame(sd, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm sd has min.rows and min.cols."))
      # Assert td_merged has min.rows and min.cols
      checkmate::assert_data_frame(td_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm td_merged has min.rows and min.cols."))
      # Assert dm has min.rows and min.cols
      checkmate::assert_data_frame(dm, min.rows = 1, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm dm has min.rows and min.cols."))
      # Assert rsrc has min.rows and min.cols
      checkmate::assert_data_frame(rsrc, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm rsrc has min.rows and min.cols."))
      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))
      #Update Vars --------------
      # if the study has no dh summary, but does have individual dh forms
      #pass each form through and left join selected columns to subject info (no merging)

      # enr_summary Table -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating enr_summary dataframe"))
      # UpdateVars
      # Enrolled is mandatory for SubjectInfo - If enr table isn't available, please use other crf data to derive "Enrolled" flag
      enr_summary <- enr %>%
        dplyr::select(.data$Subject, .data$ENRYN)
      # UpdateVars
      # Assign new lables to enr_summary
      attr(enr_summary$ENRYN, "label") <- "Enrolled"
      # Assign Labels (if avail) as the Column name.
      enr_summary <- GSDSUtilities::raiseLabels(enr_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of enr_summary dataframe"))


      # subject_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating subject_summary dataframe"))
      # UpdateVars
      # Country is mandatory for SubjectInfo - If subject table isn't available, please use other crf data to locate Country information
      subject_summary <- subject %>%
            dplyr::select(.data$Subject, .data$COUNTRY)
      # UpdateVars
      # Assign new lables to subject_summary
      attr(subject_summary$COUNTRY, "label") <- "Country"

      # Assign Labels (if avail) as the Column name.
      subject_summary <- GSDSUtilities::raiseLabels(subject_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of subject_summary dataframe"))

      # sd_summary Table --------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating sd_summary dataframe"))
      # UpdateVars
      sd_summary <- sd %>%
        dplyr::select(.data$Subject, .data$DSSTDAT_INT, .data$DSDECOD, .data$DDDAT2_INT, .data$DDRES)
      # UpdateVars
      # Assign new lables to sd_summary
      attr(sd_summary$DSSTDAT_INT, "label") <- "EOS Date"
      attr(sd_summary$DSDECOD, "label") <- "EOS Reason"
      attr(sd_summary$DDDAT2_INT, "label") <- "Death Date"
      attr(sd_summary$DDRES, "label") <- "Death Cause"
      # Assign Labels (if avail) as the Column name.
      sd_summary <- GSDSUtilities::raiseLabels(sd_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of sd_summary dataframe"))

      # td_tmp Table -----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_tmp dataframe"))
      treatments <- c("Tislelizumab",
                      "A1217")
                      
      eot_reason_col_names <- paste0("EOT Reason -",
                                     treatments)
                                     
      if(nrow(td_merged) == 0){
        td_tmp <- tibble::tibble("Subject name or identifier" = character(0))
        
        for (treat_name in eot_reason_col_names) {
          td_tmp[[treat_name]] <- character(0)
        }
        
      }else{
        td_tmp <- td_merged %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`EOT Reason`, .data$Treatment)  %>%
        tidyr::pivot_wider(id_cols = .data$`Subject name or identifier`, names_from = .data$Treatment,
                           names_prefix = "EOT Reason -",values_from = "EOT Reason") %>%
        dplyr::distinct()
        
        if(!all(eot_reason_col_names) %in% names(td_tmp)){
          cols_to_add <- setdiff(eot_reason_col_names, names(td_tmp))
          
          for (col_name in cols_to_add) {
            td_tmp <- td_tmp %>%
              dplyr::mutate(!!col_name := NA_character_)
          }
        }
      }

      # dm_summary Table --------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dm_summary dataframe"))
      # UpdateVars
      # UpdateScript for Race derivation if all 8 categories are not available
      dm_summary <- dm %>%
        dplyr::select(.data$Subject, .data$AGE, .data$SEX, .data$DMCBP, .data$ETHNIC, .data$RACASN, .data$RACAIAN,
                      .data$RACBAA, .data$RACNHOPI, .data$RACWC, .data$RACNR, .data$RACUNK, .data$RACOTH, .data$SiteNumber, .data$Site, .data$project) %>%
        dplyr::mutate(rcsum = rowSums(dplyr::select(., .data$RACASN, .data$RACAIAN, .data$RACBAA, .data$RACNHOPI, .data$RACWC, .data$RACOTH))) %>%
        dplyr::mutate(Race = dplyr::case_when(.data$rcsum > 1 ~ "Multiple",
                                              .data$RACAIAN == 1 ~ "American Indian or Alaska Native",
                                              .data$RACASN == 1 ~ "Asian" ,
                                              .data$RACBAA == 1 ~ "Black or African American" ,
                                              .data$RACNHOPI == 1 ~ "Native Hawaiian or Other Pacific Islander",
                                              .data$RACWC == 1 ~ "White",
                                              .data$RACOTH == 1 ~ "Other",
                                              .data$RACNR == 1 ~ "Not Reported",
                                              .data$RACUNK == 1 ~ "Unknown"
        )) %>% dplyr::select(.data$Subject, .data$AGE, .data$SEX, .data$DMCBP, .data$ETHNIC, .data$Race, .data$SiteNumber, .data$Site, .data$project)
      # UpdateVars
      # Assign new lables to dm_summary
      attr(dm_summary$AGE, "label") <- "Age"
      attr(dm_summary$SEX, "label") <- "Sex"
      attr(dm_summary$DMCBP, "label") <- "Child-bearing potential"
      attr(dm_summary$ETHNIC, "label") <- "Ethnicity"
      # Assign Labels (if avail) as the Column name.
      dm_summary <- GSDSUtilities::raiseLabels(dm_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of dm_summary dataframe"))

      # First Dose Date  ex_FirstDoseDate -------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_summary dataframe"))
      #log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_FirstDoseDate dataframe"))
      ex_FirstDoseDate <- dose_merged %>%
        dplyr::filter(!is.na(.data$`Subject name or identifier`)) %>%
        dplyr::filter(!is.na(.data$`Planned Dose`)) %>%
        dplyr::filter(.data$`Actual Dose` != "0mg" | .data$`Actual Dose` != "0" | .data$`Actual Dose` != "0 mg" ) %>%
        dplyr::group_by(.data$`Subject name or identifier`) %>%
        dplyr::summarise(FirstDoseDate = if (all(is.na(.data$`Dose Start Date (Interpolated)`))) lubridate::as_datetime(NA)
                         else min(.data$`Dose Start Date (Interpolated)`, na.rm = T))


      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_LastDoseDate dataframe"))
      ex_LastDoseDate <- dose_merged %>%
        dplyr::filter(!is.na(.data$`Subject name or identifier`)) %>%
        dplyr::filter(!is.na(.data$`Planned Dose`)) %>%
        dplyr::filter(.data$`Actual Dose` != "0mg" | .data$`Actual Dose` != "0" | .data$`Actual Dose` != "0 mg" ) %>%
        dplyr::group_by(.data$`Subject name or identifier`) %>%
        dplyr::summarise(MaxEndDate = if (all(is.na(.data$`Dose End Date (Interpolated)`))) lubridate::as_datetime(NA)
                         else max(.data$`Dose End Date (Interpolated)`, na.rm = T),
                         MaxStartDate = if (all(is.na(.data$`Dose Start Date (Interpolated)`))) lubridate::as_datetime(NA)
                         else max(.data$`Dose Start Date (Interpolated)`, na.rm = T)) %>%
        dplyr::mutate(LastDoseDate = dplyr::case_when(.data$MaxStartDate > .data$MaxEndDate ~ .data$MaxStartDate,
                                                      .data$MaxEndDate >= .data$MaxStartDate ~ .data$MaxEndDate)) %>%
        dplyr::select(.data$`Subject name or identifier`, .data$LastDoseDate)


      # Max Actual Dose ----
      #UpdateVars
      MaxActualDose <- dose_merged %>%
        dplyr::filter(!is.na(.data$`Subject name or identifier`), .data$Treatment == "A1217") %>%
        dplyr::filter(!is.na(.data$`Planned Dose`)) %>%
        dplyr::filter(.data$`Actual Dose` != "0") %>%
        dplyr::mutate(`ActualDoseNumeric` = as.numeric(stringr::str_extract(.data$`Actual Dose`, pattern = "(\\d|\\.)*"))) %>%
        dplyr::group_by(.data$`Subject name or identifier`) %>%
        dplyr::summarise(MaxActualDose = max(.data$`ActualDoseNumeric`, na.rm = T) ) %>%
        dplyr::select(.data$`Subject name or identifier`, .data$MaxActualDose)


      # ex_summary by joining ex_FirstDoseDate ex_LastDoseDate ---------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_summary dataframe"))
      ex_summary <- dplyr::left_join(ex_FirstDoseDate,ex_LastDoseDate,by = "Subject name or identifier")
      ex_summary <- dplyr::left_join(ex_summary,  MaxActualDose, by = "Subject name or identifier")
      # Assign new lables to ex_summary
      attr(ex_summary$`Subject name or identifier`, "label") <- "Subject name or identifier"


      # First and Last Response Date Calculation by --------------------------------------
      # taking the min and max of "Date of Response Interpolated"
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating rsrc_FirstLast dataframe"))
      # UpdateVars
      rsrc_summary <- rsrc %>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `Date of Response Interpolated` = .data$RSRCDAT_INT) %>%
        dplyr::filter(!is.na(.data$`Subject name or identifier`)) %>%
        dplyr::group_by(.data$`Subject name or identifier`) %>%
        dplyr::summarise(FirstResponseDate = min(.data$`Date of Response Interpolated`),
                         LastResponseDate = max(.data$`Date of Response Interpolated`))


      # EOT Date --------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating EOTDbyTreatment dataframe"))
      
      eot_date_col_names <- paste0("EOT Date -", treatments)
      if(nrow(td_merged) == 0){
        EOTDbyTreatment <- tibble::tibble("Subject name or identifier" = character(0))
        
        for (col_name in eot_date_col_names) {
          EOTDbyTreatment[[col_name]] <- as.Date(x = integer(0),
                                                 origin = "1970-01-01")
        }
      }else{
        EOTDbyTreatment <- td_merged %>%
          dplyr::select(.data$`EOT Date`, .data$`Subject name or identifier`, .data$Treatment) %>%
          dplyr::mutate(`EOT Date` = as.character(.data$`EOT Date`)) %>%
          tidyr::pivot_wider(id_cols = .data$`Subject name or identifier`, names_from = .data$Treatment,
                             names_prefix = "EOT Date -",values_from = "EOT Date") %>%
          dplyr::mutate_at(dplyr::vars(grep("Date", names(.), value = TRUE)), .funs = as.Date)
          
        if (!all(eot_date_col_names) %in% names(EOTDbyTreatment)) {
          cols_to_add <- setdiff(eot_date_col_names, names(EOTDbyTreatment))
          
          for (col_name in cols_to_add) {
            EOTDbyTreatment <- EOTDbyTreatment %>%
              dplyr::mutate(!!col_name := as.Date(x = NA_character_, 
                                                  origin = "1970-01-01"))
          }
        }
      }

      # td_summary --------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_summary dataframe"))
      td_summary <- dplyr::left_join(EOTDbyTreatment, td_tmp,by = "Subject name or identifier")
      attr(td_summary$`Subject name or identifier`, "label") <-  "Subject name or identifier"


      # candig_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating candig_summary dataframe"))
      # UpdateVars
      candig_summary <- candig %>%
        dplyr::select(.data$Subject, .data$DHXTYP)
      # UpdateVars
      # Assign new lables to candig_summary
      attr(candig_summary$DHXTYP, "label") <- "Type of Tumor"

      # Assign Labels (if avail) as the Column name.
      candig_summary <- GSDSUtilities::raiseLabels(candig_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of candig_summary dataframe"))



      # SubjectInfo dataframe -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating SubjectInfo dataframe"))
      SubjectInfo <- dplyr::left_join(dm_summary, enr_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, subject_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, rsrc_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, sd_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, td_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, ex_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, candig_summary, by = "Subject name or identifier")


      #Calculate SiteEnrolled,TotalEnrolled, and # of Females Enrolled-----------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Calculating EnrollmentbySite"))

      # UpdateVars
      # SiteNumber variable name may not be the same for all studies
      EnrollmentbySite <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier` , .data$SiteNumber , .data$Enrolled) %>%
        dplyr::filter(substr(.data$Enrolled, 1, 1) == "Y") %>%
        dplyr::group_by(.data$SiteNumber) %>%
        dplyr::summarise(SiteEnrolled = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Join EnrollmentbySite into SubjectInfo"))
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Calculate Females Enrolled"))
      # UpdateVars
      # SiteNumber variable name may not be the same for all studies

      SubjectInfo <- dplyr::left_join(SubjectInfo, EnrollmentbySite, by = "SiteNumber") %>%
        dplyr::mutate(TotalEnrolled = (SubjectInfo %>%
                                         dplyr::filter(substr(.data$Enrolled, 1, 1) == "Y") %>%
                                         dplyr::select(.data$`Subject name or identifier`) %>%
                                         dplyr::distinct() %>%
                                         dplyr::count() %>%
                                         dplyr::pull())) %>%
        dplyr::mutate(TotalFemalesEnrolled  = (SubjectInfo %>%
                                                 dplyr::filter(substr(.data$Enrolled, 1, 1) == "Y" & substr(.data$Sex, 1, 1) == "F") %>%
                                                 dplyr::select(.data$`Subject name or identifier`) %>%
                                                 dplyr::distinct() %>%
                                                 dplyr::count() %>%
                                                 dplyr::pull()))

      #Calculate Active Treatment Status -----
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Calculate OnTreatment Status"))

      # UpdateVars
      # For Combo therapies, the column name for EOT Date will be different (i.e. `EOT Date - A317`)
      # Use the EOT Date column name that you want to use to decide on Active Treatment Status
      SubjectInfo <- SubjectInfo %>%
        dplyr::mutate(`OnTreatment`= dplyr::case_when(
        substr(.data$Enrolled,1,1) == "Y" & (!is.na(.data$`EOT Date -Tislelizumab`) |  !is.na(.data$`EOS Date`) | !is.na(.data$`Death Date`)) ~ "N",
        substr(.data$Enrolled,1,1) == "Y" & is.na(.data$`EOT Date -Tislelizumab`) ~"Y",
        substr(.data$Enrolled,1,1) == "N" ~ "Not Enrolled"
      )) %>%
        dplyr::select(-.data$project, tidyselect::everything())

      # UpdateVars
      # Cohort variable name may not be the same for all studies

      CohortEnroll <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier` , .data$Cohort , .data$Enrolled) %>%
        dplyr::filter(substr(.data$Enrolled,1,1)=="Y") %>%
        dplyr::group_by(.data$Cohort) %>%
        dplyr::summarise(`TotalEnrolled by Cohort/Arm` = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"| ","Calculate `TotalEnrolled by Cohort/Arm` "))

      # UpdateVars
      # Cohort variable name may not be the same for all studies
      SubjectInfo <- dplyr::left_join(SubjectInfo, CohortEnroll, by = "Cohort")

      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"| ","Join CohortEnroll into SubjectInfo"))

      # UpdateVars
      # Cohort variable name may not be the same for all studies
      Treated <- SubjectInfo %>%
        dplyr::filter(!is.na(.data$FirstDoseDate)) %>%
        dplyr::select(.data$`Subject name or identifier`, .data$Cohort) %>%
        dplyr::mutate(TotalTreated = dplyr::n_distinct(.data$`Subject name or identifier`),
                      Treated = "Yes") %>%
        dplyr::distinct()

      # UpdateVars
      # Cohort variable name may not be the same for all studies
      SubjectInfo <-  dplyr::left_join(SubjectInfo, Treated, by = c("Subject name or identifier","Cohort"))

      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"| ","Join Treated into SubjectInfo"))

      CohortTreated <- Treated %>%
        dplyr::group_by(.data$Cohort) %>%
        dplyr::summarise(`TotalTreated by Cohort/Arm` = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"| ","Calculate TotalTreated,CohortTreated"))

      # UpdateVars
      # Cohort variable name may not be the same for all studies
      SubjectInfo <- dplyr::left_join(SubjectInfo, CohortTreated, by = "Cohort")

      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"| ","Join CohortTreated into SubjectInfo"))

      # Assign SubjectInfo to calling envir ----------------------------------------------------
      assign("SubjectInfo", SubjectInfo, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," SubjectInfo returned"))


      # End of subjectInfo Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
