import os
import sys
import pandas as pd
from patient_profile_generator import PatientProfileGenerator

def create_mock_als_file(file_path):
    """Create a mock ALS file with custom form names for testing."""
    # Create a writer to save the Excel file
    writer = pd.ExcelWriter(file_path, engine='openpyxl')
    
    # Create Forms sheet with custom form names
    forms_data = {
        'OID': ['F.CUSTOM_AE', 'F.CUSTOM_CM', 'F.CUSTOM_RS', 'F.CUSTOM_PPROC'],
        'DraftFormName': [
            'Adverse Events', 
            'Prior/Concomitant Medications', 
            'Response Assessment', 
            'Prior/Concomitant Procedures/Surgeries'
        ],
        'Form': ['custom_ae', 'custom_cm', 'custom_rs', 'custom_pproc']
    }
    forms_df = pd.DataFrame(forms_data)
    forms_df.to_excel(writer, sheet_name='Forms', index=False)
    
    # Create Fields sheet with custom field names
    fields_data = {
        'FormOID': [
            'F.CUSTOM_AE', 'F.CUSTOM_AE', 'F.CUSTOM_AE', 'F.CUSTOM_AE', 'F.CUSTOM_AE', 'F.CUSTOM_AE', 'F.CUSTOM_AE', 'F.CUSTOM_AE',
            'F.CUSTOM_CM', 'F.CUSTOM_CM', 'F.CUSTOM_CM', 'F.CUSTOM_CM', 'F.CUSTOM_CM',
            'F.CUSTOM_RS', 'F.CUSTOM_RS',
            'F.CUSTOM_PPROC', 'F.CUSTOM_PPROC'
        ],
        'FieldOID': [
            'AESTDAT', 'AEENDAT', 'AETERM', 'AETERM_PT', 'AETERM_SOC', 'AETOXGR', 'AESDTH', 'AESER',
            'CMSTDAT', 'CMENDAT', 'CMTRT', 'CMTRT_PROD', 'CMONGO',
            'RSORRES', 'RSDAT',
            'PRSTDAT', 'PRTRT'
        ],
        'SASLabel': [
            'AE Start date', 'AE End date', 'Adverse event', 'AE Dictionary-Derived Term', 'AE SOC', 'Toxicity grade', 'Result in death', 'Was adverse event serious',
            'CM Start date', 'CM End date', 'Medication name', 'CM Preferred Term', 'Ongoing',
            'Overall response', 'Date of overall response',
            'Date of procedure/surgery', 'Type or name of procedure/surgery'
        ]
    }
    fields_df = pd.DataFrame(fields_data)
    fields_df.to_excel(writer, sheet_name='Fields', index=False)
    
    # Create DataDictionaryEntries sheet
    data_dic_data = {
        'DataDictionaryName': ['RSORRES'],
        'CodedData': ['CR', 'PR', 'SD', 'PD', 'NE'],
        'UserDataString': [
            'Complete response (CR)', 
            'Partial response (PR)', 
            'Stable disease (SD)', 
            'Progressive disease (PD)', 
            'Not evaluable (NE)'
        ]
    }
    data_dic_df = pd.DataFrame(data_dic_data)
    data_dic_df.to_excel(writer, sheet_name='DataDictionaryEntries', index=False)
    
    # Save the Excel file
    writer.close()
    
    return file_path

def main():
    # Create a mock ALS file with custom form names
    mock_als_file = "mock_als_with_custom_forms.xlsx"
    create_mock_als_file(mock_als_file)
    
    # Create a PatientProfileGenerator instance
    generator = PatientProfileGenerator(mock_als_file)
    
    # Print the table mappings to verify form names are mapped correctly
    print("Table Mappings:")
    for key, value in generator.table_mappings.items():
        print(f"  {key}: {value}")
    
    # Generate the R function for a specific study ID
    study_id = "test_crf_origin"
    r_function = generator.generate_function(study_id)
    
    # Save the R function to a file
    output_file = f"patientProfile_{study_id}_generated.R"
    with open(output_file, "w") as f:
        f.write(r_function)
    
    print(f"\nR function generated and saved to '{output_file}'")
    
    # Clean up
    os.remove(mock_als_file)

if __name__ == "__main__":
    main()
