library(readxl)
library(dplyr)
library(magrittr)
library(stringr)
library(tidyr)

ALS_field<-read_excel("BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx",sheet = "Fields") %>% 
  dplyr::select(.data$FormOID,.data$FieldOID,.data$SASLabel) %>% 
  dplyr::mutate(Form = tolower(.data$FormOID)) %>% 
  dplyr::select(-.data$FormOID) %>% 
  tidyr::drop_na()

ALS_form<- read_excel("BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx",sheet = "Forms") %>% 
  dplyr::select(.data$OID,.data$DraftFormName) %>% 
  dplyr::mutate(Form =tolower(.data$OID)) %>% 
  dplyr::select(-.data$OID) %>% 
  tidyr::drop_na()

ALS_dataDic <-read_excel("BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx",sheet = "DataDictionaryEntries")

var_label_form <- ALS_field %>% 
  dplyr::left_join(ALS_form, by = "Form") 

ae_std<-var_label_form %>% 
  dplyr::filter(grepl("Adverse Events(?: \\{Mixed form\\})?",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Start date|Stop date|^Adverse event$|Toxicity grade|Result in death|Was adverse event serious|Outcome|DLT|clinical interest|special interest",.data$SASLabel)) %>% 
  dplyr::filter(!grepl("RSG",.data$SASLabel)) %>% 
  dplyr::mutate(part = "ae_std")

cm_std <- var_label_form %>% 
  dplyr::filter(grepl("Prior/Concomitant Medications",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Start date|Stop date|Medication name|Ongoing",.data$SASLabel)) %>% 
  dplyr::mutate(part = "cm_std")

rs_std <- var_label_form %>% 
  dplyr::filter(grepl("Time-point Response Assessment - RECIST|Disease Assessment",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Overall response",.data$SASLabel)) %>% 
  dplyr::left_join(ALS_dataDic %>% dplyr::select(.data$UserDataString,.data$CodedData,.data$DataDictionaryName),by = c("FieldOID"="DataDictionaryName")) %>% 
  dplyr::mutate(part = "rs_std")

pproc_std <- var_label_form %>% 
  dplyr::filter(grepl("Prior/Concomitant Procedures/Surgeries",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Date of procedure/surgery|Type or name of procedure/surgery and location",.data$SASLabel))

patientProfile_info <-bind_rows(ae_std,cm_std, rs_std, pproc_std)









  