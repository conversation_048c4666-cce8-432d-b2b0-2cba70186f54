import os
import sys
from ae_writeout_generator import AEWriteOutGenerator

def main():
    # Check if ALS file path is provided as command line argument
    if len(sys.argv) > 1:
        als_file = sys.argv[1]
    else:
        # Use a default ALS file for testing
        als_file = "BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx"
    
    # Check if the ALS file exists
    if not os.path.exists(als_file):
        print(f"Error: ALS file '{als_file}' not found.")
        return
    
    # Create an AEWriteOutGenerator instance
    generator = AEWriteOutGenerator(als_file, "Solid Tumor")
    
    # Generate the R function for a specific study ID
    study_id = "b_bgb_16673_104"
    r_function = generator.generate_function(study_id)
    
    # Save the R function to a file
    output_file = f"ae_5_AEWriteOut_{study_id}_generated.R"
    with open(output_file, "w") as f:
        f.write(r_function)
    
    print(f"Generated R function saved to {output_file}")
    
    # Print the drug action mappings for debugging
    drug_actions = generator._get_drug_action_mappings()
    print("\nDrug Action Mappings:")
    for i, drug in enumerate(drug_actions, 1):
        print(f"Drug {i}: {drug.get('drug', 'Unknown')}")
        for key, value in drug.items():
            if key != 'drug':
                print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
