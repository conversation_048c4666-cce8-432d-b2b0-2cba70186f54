import os
import sys
import pandas as pd
from patient_profile_generator import PatientProfileGenerator

def create_mock_als_file(file_path):
    """Create a mock ALS file with custom field names for testing."""
    # Create a writer to save the Excel file
    writer = pd.ExcelWriter(file_path, engine='openpyxl')
    
    # Create Forms sheet
    forms_data = {
        'OID': ['F.AE', 'F.CM', 'F.RS_R', 'F.PPROC'],
        'DraftFormName': [
            'Adverse Events', 
            'Prior/Concomitant Medications', 
            'Response Assessment', 
            'Prior/Concomitant Procedures/Surgeries'
        ],
        'Form': ['ae', 'cm', 'rs_r', 'pproc']
    }
    forms_df = pd.DataFrame(forms_data)
    forms_df.to_excel(writer, sheet_name='Forms', index=False)
    
    # Create Fields sheet with custom field names
    fields_data = {
        'FormOID': [
            'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE',
            'F.CM', 'F.CM', 'F.CM', 'F.CM', 'F.CM',
            'F.RS_R', 'F.RS_R',
            'F.PPROC', 'F.PPROC'
        ],
        'FieldOID': [
            'CUSTOM_AESTDAT', 'CUSTOM_AEENDAT', 'CUSTOM_AETERM', 'CUSTOM_AETERM_PT', 'CUSTOM_AETERM_SOC', 'CUSTOM_AETOXGR', 'CUSTOM_AESDTH', 'CUSTOM_AESER',
            'CUSTOM_CMSTDAT', 'CUSTOM_CMENDAT', 'CUSTOM_CMTRT', 'CUSTOM_CMTRT_PROD', 'CUSTOM_CMONGO',
            'CUSTOM_RSORRES', 'CUSTOM_RSDAT',
            'CUSTOM_PRSTDAT', 'CUSTOM_PRTRT'
        ],
        'SASLabel': [
            'AE Start date', 'AE End date', 'Adverse event', 'AE Dictionary-Derived Term', 'AE SOC', 'Toxicity grade', 'Result in death', 'Was adverse event serious',
            'CM Start date', 'CM End date', 'Medication name', 'CM Preferred Term', 'Ongoing',
            'Overall response', 'Date of overall response',
            'Date of procedure/surgery', 'Type or name of procedure/surgery'
        ]
    }
    fields_df = pd.DataFrame(fields_data)
    fields_df.to_excel(writer, sheet_name='Fields', index=False)
    
    # Create DataDictionaryEntries sheet
    data_dic_data = {
        'DataDictionaryName': ['CUSTOM_RSORRES'],
        'CodedData': ['CR', 'PR', 'SD', 'PD', 'NE'],
        'UserDataString': [
            'Complete response (CR)', 
            'Partial response (PR)', 
            'Stable disease (SD)', 
            'Progressive disease (PD)', 
            'Not evaluable (NE)'
        ]
    }
    data_dic_df = pd.DataFrame(data_dic_data)
    data_dic_df.to_excel(writer, sheet_name='DataDictionaryEntries', index=False)
    
    # Save the Excel file
    writer.close()
    
    return file_path

def main():
    # Create a mock ALS file with custom field names
    mock_als_file = "mock_als_with_custom_fields.xlsx"
    create_mock_als_file(mock_als_file)
    
    # Create a PatientProfileGenerator instance
    generator = PatientProfileGenerator(mock_als_file)
    
    # Generate the R function for a specific study ID
    study_id = "test_dynamic_mapping"
    r_function = generator.generate_function(study_id)
    
    # Save the R function to a file
    output_file = f"patientProfile_{study_id}_generated.R"
    with open(output_file, "w") as f:
        f.write(r_function)
    
    print(f"\nR function generated and saved to '{output_file}'")
    
    # Clean up
    os.remove(mock_als_file)

if __name__ == "__main__":
    main()
