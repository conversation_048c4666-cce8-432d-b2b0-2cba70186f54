#' @title vitalctcae_config_b_bgb_b3227_101test
#' @description config file processing
#' @param studyId This is a character field, studyId
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param config_path The folder path of config file
#'
#' @return crf_config
#' @export vitalctcae_config_b_bgb_b3227_101test
#' @importFrom magrittr %>%
#' @importFrom dplyr filter select
#' @importFrom rlang .data
#' @importFrom tibble tibble
#' @importFrom checkmate assert_tibble assert_directory_exists
#' @importFrom utils read.csv
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#' vitalctcae_config_b_bgb_b3227_101test(studyId = studyIdVar,
#'                   tempLogger = CentralLogger,
#'                   config_path = config_path)
#'}
#'
#'
#'

vitalctcae_config_b_bgb_b3227_101test <- function(studyId,tempLogger,config_path){
  calledFun <- "vitalctcae_config_b_bgb_b3227_101test"
  # Start Function --------------------------------------------------------------------
  log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))
  # Assertions -----------------------------------------------------------------------
  checkmate::assert_file_exists(config_path)
  
  # Read in Config ----------------
  config_tb <- read.csv(config_path,
                        header = TRUE,
                        stringsAsFactors = FALSE,
                        na.strings = "",
                        skipNul = TRUE) %>%
    tibble::tibble()
  
  
  r_tb_name_lst <- config_tb %>%
    dplyr::filter(!is.na(.data$r_name)) %>%
    dplyr::select(.data$r_name) %>%
    dplyr::distinct() %>%
    dplyr::pull()
  
  #turn the user-friendly tabular config into
  #program friendly list config
  crf_config <- vector("list", length(r_tb_name_lst))
  names(crf_config) <- r_tb_name_lst
  
  
  for (i in seq_along(r_tb_name_lst)) {
    crf_name <- config_tb %>%
      dplyr::filter(.data$r_name == r_tb_name_lst[[i]]) %>%
      dplyr::select(.data$crf_name) %>%
      dplyr::distinct() %>%
      dplyr::pull()
    
    crf_config[[r_tb_name_lst[[i]]]]$crf_name <- crf_name
    
    r_col_name_lst <- config_tb %>%
      dplyr::filter(.data$r_name == r_tb_name_lst[[i]] &
                      !is.na(.data$r_col_name)) %>%
      dplyr::select(.data$r_col_name) %>%
      dplyr::distinct() %>%
      dplyr::pull()
    
    for (j in seq_along(r_col_name_lst)) {
      crf_col_name_tb <- config_tb %>%
        dplyr::filter(.data$r_name == r_tb_name_lst[[i]] &
                        .data$r_col_name == r_col_name_lst[[j]]) %>%
        dplyr::select(.data$crf_col_name, .data$crf_col_type) %>%
        dplyr::distinct()
      
      crf_config[[r_tb_name_lst[[i]]]][[r_col_name_lst[[j]]]]$crf_col_name <- crf_col_name_tb$crf_col_name
      crf_config[[r_tb_name_lst[[i]]]][[r_col_name_lst[[j]]]]$crf_col_type <- crf_col_name_tb$crf_col_type
      
      r_col_val_tb <- config_tb %>%
        dplyr::filter(.data$r_name == r_tb_name_lst[[i]] &
                        .data$r_col_name == r_col_name_lst[[j]] &
                        !is.na(.data$r_col_val_name)) %>%
        dplyr::select(.data$r_col_val_name, .data$crf_col_val_name) %>%
        dplyr::distinct()
      
      #create named list for easy re-naming
      r_col_val_lst <- r_col_val_tb$r_col_val_name
      names(r_col_val_lst) <- r_col_val_tb$crf_col_val_name
      
      if (length(r_col_val_lst) > 0) {
        crf_config[[r_tb_name_lst[[i]]]][[r_col_name_lst[[j]]]]$val_lst <- r_col_val_lst
        
      }
    }
  }
  
  
  # Return ----------------
  assign("crf_config", crf_config, envir = parent.frame())
  return(crf_config)
  log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," crf_config returned"))
}





#' @title vitalctcae_datastandardization_b_bgb_b3227_101test
#' @description Data Preparation
#' @param studyId This is a character field, studyId
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param da_crf_lst A list of all of the crfs
#' @param crf_config The study crf config list.
#'
#' @return standard_da_crf_lst
#' @export vitalctcae_datastandardization_b_bgb_b3227_101test
#' @importFrom magrittr %>%
#' @importFrom dplyr filter select
#' @importFrom rlang .data
#' @importFrom checkmate assert_tibble assert_directory_exists
#' @importFrom purrr map
#'
#'
#' @examples
#' \dontrun{
#' vitalctcae_datastandardization_b_bgb_b3227_101test(studyId =  studyIdVar,
#'                               tempLogger = CentralLogger,
#'                               da_crf_lst = da_crf_lst,
#'                                crf_config = crf_config)
#'}
#'
#'
#'

vitalctcae_datastandardization_b_bgb_b3227_101test <- function(studyId,
                                                           tempLogger,
                                                           crf_config,
                                                           da_crf_lst){
  
  calledFun <- "vitalctcae_datastandardization_b_bgb_b3227_101test"
  # Start Function --------------------------------------------------------------------
  log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))
  
  crf_name_lst <- purrr::map(.x = crf_config, .f = function(.x) return(.x$crf_name))
  
  checkmate::assert_true(all(crf_name_lst %in% names(da_crf_lst)))
  
  print_lst <- list()
  #assign crfs to current environment
  for (i in seq_along(crf_name_lst)) {
    crf_name <- crf_name_lst[[i]]
    r_crf_name <- names(crf_name_lst)[[i]]
    
    new_crf <- da_crf_lst[[crf_name]]
    #apply assertions
    #assert data frames have at least 2 rows
    #assert tibble
    checkmate::assert_data_frame(new_crf, min.rows = 0)
    
    #get necessary columns
    column_info_lst <- crf_config[[r_crf_name]]
    column_info_lst$crf_name <- NULL
    
    column_name_lst <- purrr::map(.x = column_info_lst, .f = function(.x) .x$crf_col_name)
    
    subset_crf <- new_crf %>%
      dplyr::select(column_name_lst %>% unlist() %>% unname())
    
    #check column type requirement
    for (j in seq_along(column_info_lst)) {
      r_col_name <- names(column_info_lst)[[j]]
      col_name <- column_info_lst[[j]]$crf_col_name
      
      #remove label so no warnings on join
      attr(subset_crf[[col_name]], which = "label") <- NULL
      
      column_type <- column_info_lst[[j]]$crf_col_type
      
      checkmate::assert_vector(column_type, max.len = 1, min.len = 1)
      
      checkmate::assert_true(column_type %in% c("character", "numeric", "date"))
      
      #valid types are "character", "numeric" and "date"
      check_lgl <- switch(column_type,
                          "character" = is.character(subset_crf[[col_name]]),
                          "numeric" = is.numeric(subset_crf[[col_name]]),
                          "date" = lubridate::is.POSIXct(subset_crf[[col_name]]))
      
      
      if (check_lgl == FALSE) {
        check_coercible_lgl <- switch(column_type,
                                      "character" = sum(is.na(subset_crf[[col_name]])) ==
                                        sum(is.na(suppressWarnings(as.character(subset_crf[[col_name]])))),
                                      "numeric" = sum(is.na(subset_crf[[col_name]])) ==
                                        sum(is.na(suppressWarnings(as.numeric(subset_crf[[col_name]])))),
                                      "date" = sum(is.na(subset_crf[[col_name]])) ==
                                        sum(is.na(suppressWarnings(lubridate::as_datetime(subset_crf[[col_name]])))))
        
        if (check_coercible_lgl == TRUE) {
          subset_crf[[col_name]] <- switch(column_type,
                                           "character" = suppressWarnings(as.character(subset_crf[[col_name]])),
                                           "numeric" = suppressWarnings(as.numeric(subset_crf[[col_name]])),
                                           "date" = suppressWarnings(lubridate::as_datetime(subset_crf[[col_name]])))
        }
      }
      #rename values to standard
      val_lst <- column_info_lst[[r_col_name]]$val_lst
      
      if (!is.null(val_lst)) {
        subset_crf <- subset_crf %>%
          dplyr::mutate(!!col_name := ifelse(is.na(.data[[col_name]]),
                                             NA,
                                             ifelse(.data[[col_name]] %in% names(val_lst),
                                                    val_lst[as.character(.data[[col_name]])],
                                                    .data[[col_name]])))
        
        #If "NA" is the value, change to actual NA
        if ("NA" %in% val_lst) {
          subset_crf <- subset_crf %>%
            dplyr::mutate(!!col_name := ifelse(is.na(.data[[col_name]]),
                                               NA,
                                               ifelse(.data[[col_name]] == "NA",
                                                      NA,
                                                      .data[[col_name]])))
        }
      }
    }
    
    #rename columns to standard
    subset_crf <- subset_crf %>%
      dplyr::select(column_name_lst %>% unlist()) #%>%
    #dplyr::distinct()
    
    checkmate::assert_data_frame(subset_crf, min.rows = 0, min.cols = 2)
    
    #assign parsed data frame to return list
    print_lst[[r_crf_name]] <- subset_crf
  }
  
  # Return ----------------
  standard_da_crf_lst <- print_lst
  assign("standard_da_crf_lst", print_lst, envir = parent.frame())
  return(standard_da_crf_lst)
  log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," standard_da_crf_lst returned"))
}



#' @title vitalctcae_grading_b_bgb_b3227_101test
#' @description the CTCAE grading and deviation for vital and ecg data
#' @param studyId This is a character field, studyId
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param vs_merged merged vital dataset
#' @param vitalPT vital lookup table
#' @param lb_toxgrades_tmp lb_toxgrades output
#' @param SubjectInfo_tmp SubjectInfo data table
#' @param ae The adverse events crf data frame
#'
#' @return vital
#' @return ae_to_vs_toxflag
#' @export vitalctcae_grading_b_bgb_b3227_101test
#' @importFrom dplyr select distinct mutate case_when filter group_by bind_rows left_join pull rowwise lead lag arrange first ungroup
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom tidyr pivot_wider
#' @importFrom tidyselect starts_with ends_with
#' @importFrom tibble add_column tribble as_tibble
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r info warn error
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#'        vitalctcae_grading_b_bgb_b3227_101test(studyId =  studyIdVar,
#'                                     tempLogger = CentralLogger,
#'                                     vs_merged = vs_merged,
#'                                     vitalPT = vitalPT,
#'                                     lb_toxgrades_tmp = lb_toxgrades,
#'                                     SubjectInfo_tmp = SubjectInfo,
#'                                     ae = ae)
#'}

vitalctcae_grading_b_bgb_b3227_101test <- function(studyId, tempLogger,
                                               vs_merged,
                                               vitalPT,
                                               lb_toxgrades_tmp,
                                               SubjectInfo_tmp,
                                               ae){
  
  calledFun <- "vitalctcae_grading_b_bgb_b3227_101test"
  # Start Function --------------------------------------------------------------------
  log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))
  
  # Assertions -----------------------------------------------------------------------
  # Assert vs_merged has min.rows and min.cols
  checkmate::assert_data_frame(vs_merged, min.rows = 0, min.cols = 2)
  log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm vs_merged has min.rows and min.cols."))
  
  VSToxGr <- NULL
  TESTDAT_INT <- NULL
  StdUnits <- NULL
  # Read AE-PT mapping list vitalPT config files
  vitalPT <- vitalPT %>%
    dplyr::mutate(PT = stringr::str_to_lower(.data$PT))
  
  # subset lb_anc_tmp for lab ANC tests------------------------------------------------------------------------------------------------------------------------------------
  lb_anc_tmp <- lb_toxgrades_tmp %>%
    dplyr::filter(.data$`Dictionary-Derived Lab Name` == "Neutrophils") %>%
    dplyr::mutate(lab_ANC = .data$StdValue,
                  lab_ANC_unit = ifelse(is.na(.data$StdUnits), "10^9/L", .data$StdUnits)) %>%
    dplyr::filter(StdUnits =="10^9/L") %>%
    #Reason suggested filtering to 10^9/L is the ctcae guideline suggests two SI units for Neutrophils, mm3 and 10^9/L
    dplyr::group_by(.data$Subject, .data$instanceId) %>%
    dplyr::filter(.data$lab_ANC == max(.data$lab_ANC, na.rm = T)) %>%
    #Add na.rm = T, if derive the max value when NA values are mixed in for a given subject
    dplyr::select(.data$Subject,.data$instanceId,.data$lab_ANC,.data$lab_ANC_unit) %>%
    dplyr::distinct(.data$Subject, .data$instanceId,.keep_all = T)
  
  # merge vital, weight, ECG all together------------------------------------------------------------------------------------------------------------------------------------
  vs_std <- vs_merged %>%
    dplyr::filter(!is.na(.data$stdvalue) & !is.na(.data$unit) & !is.na(.data$TESTDAT_INT)) %>%
    dplyr::left_join(lb_anc_tmp,by = c("Subject","instanceId")) %>%
    dplyr::mutate(temp_flag="No") %>%
    dplyr::arrange(.data$Subject,.data$RecordDate)
  
  for(i in 1:(nrow(vs_std)-1))
  {
    for (j in (i+1):nrow(vs_std))
    {
      if (vs_std[i,"Subject"] != vs_std[j,"Subject"])
      {
        break;
      }
      if (vs_std[i,"RecordDate"] == vs_std[j,"RecordDate"] & vs_std[i,"stdvalue"]>40 & vs_std[j,"stdvalue"]>40 & vs_std[i,"Test"]=="temp" & vs_std[j,"Test"]=="temp")
      {
        vs_std[i, "temp_flag"] <- "Yes"
        vs_std[j, "temp_flag"] <- "Yes"
        break;
      }
      break;
    }
  }
  
  vs_std  <-  GSDSUtilities::reApplyLabels(sourceDF = vs_merged, targetDF = vs_std)
  
  # calculalting baseline-----------------------------------------------------------------------------------------------------------------------------
  FirstDoseDate <- SubjectInfo_tmp %>% dplyr::select(Subject = .data$`Subject name or identifier`, .data$FirstDoseDate) %>% dplyr::distinct()
  attr( FirstDoseDate$Subject,"label") <- attr(vs_std$Subject,"label")
  
  vs_bl <- vs_std %>%
    dplyr::left_join(y = FirstDoseDate, by = c("Subject")) %>%
    dplyr::filter(as.Date(.data$TESTDAT_INT) <= .data$FirstDoseDate) %>%
    dplyr::group_by(.data$Subject, .data$Test) %>%
    dplyr::filter(.data$TESTDAT_INT == max(.data$TESTDAT_INT)) %>%
    dplyr::mutate(BaselineValue = mean(.data$stdvalue)) %>%
    dplyr::rename(BaselineDate = .data$TESTDAT_INT, BaselineVisit = .data$InstanceName) %>%
    dplyr::mutate(BaselineDate = as.Date(.data$BaselineDate)) %>%
    dplyr::filter(!is.na(.data$BaselineValue) & !is.na(.data$unit)) %>%
    dplyr::select(-.data$instanceId,-.data$Timepoint,-.data$stdvalue,-.data$lab_ANC,-.data$lab_ANC_unit,-.data$RecordDate,-.data$temp_flag,
                  -.data$LastChangeDate,-.data$WhatChanged) %>%
    dplyr::distinct(.data$Subject, .data$Test,.keep_all = T) %>%
    dplyr::ungroup()
  
  # Merge extracted baseline results into the vital dataset by subject, test and unit
  vs_bl_merged <- dplyr::left_join(vs_std,  vs_bl , by = c("Subject","Test","unit"))
  vs_bl_cal <- vs_bl_merged %>%
    dplyr::mutate(baseline_perc_change =
                    dplyr::case_when(
                      is.na(.data$stdvalue) |
                        is.na(.data$BaselineValue) ~
                        NA_real_,
                      .data$BaselineValue == 0 ~
                        NA_real_,
                      TRUE ~
                        (.data$stdvalue -
                           .data$BaselineValue)/.data$BaselineValue)*100,
                  baseline_abs_change =
                    dplyr::case_when(
                      is.na(.data$stdvalue) |
                        is.na(.data$BaselineValue) ~
                        NA_real_,
                      .data$BaselineValue == 0 ~
                        NA_real_,
                      TRUE ~
                        (.data$stdvalue -.data$BaselineValue)))
  
  
  #define %>=% and %<=% functions
  
  '%>=%' <- function(x, y) {
    dplyr::near(x, y) | x > y
  }
  
  '%<=%' <- function(x, y) {
    dplyr::near(x, y) | x < y
  }
  
  # Calculating CTCAE Grades---------------------------------------------------------------------
  vs_bl_cal <- vs_bl_cal %>%
    dplyr::mutate(Test =
                    ifelse(.data$Test == "temp" & .data$stdvalue >38 & .data$lab_ANC < 1 & !is.na(.data$lab_ANC),
                           "temp_lab", .data$Test))
  
  vs_ctcae_grade <- vs_bl_cal %>%
    dplyr::mutate(
      VSToxGr = dplyr::case_when(
        .data$Test == "temp" & .data$stdvalue > 38 & .data$stdvalue %<=% 39 ~ 1,
        .data$Test == "temp" & ((.data$stdvalue > 39 & .data$stdvalue %<=% 40 )|(.data$stdvalue > 32 & .data$stdvalue %<=% 35)) ~ 2,
        .data$Test == "temp" & ((.data$stdvalue > 40 & .data$temp_flag == "No")|(.data$stdvalue > 28 &.data$stdvalue %<=% 32)) ~ 3,
        .data$Test == "temp" & ((.data$stdvalue > 40 & .data$temp_flag == "Yes")|.data$stdvalue %<=% 28) ~ 4,
        
        .data$Test == "temp_lab" & .data$stdvalue > 38 & .data$lab_ANC < 1 & !is.na(.data$lab_ANC) ~ 3,
        #
        #
        .data$Test == "QTc" & (.data$stdvalue > 450 & .data$stdvalue %<=% 481) ~ 1,
        .data$Test == "QTc" & (.data$stdvalue %>=% 481 & .data$stdvalue %<=% 501) ~ 2,
        .data$Test == "QTc" & (.data$stdvalue %>=% 501 | .data$baseline_abs_change > 60) ~ 3,
        #
        #
        .data$Test == "weight" & ((.data$baseline_perc_change %>=% 5 & .data$baseline_perc_change < 10)|(.data$baseline_perc_change > -10 & .data$baseline_perc_change %<=% -5)) ~ 1,
        .data$Test == "weight" & ((.data$baseline_perc_change %>=% 10 & .data$baseline_perc_change < 20)|(.data$baseline_perc_change > -20 & .data$baseline_perc_change %<=% -10)) ~ 2,
        .data$Test == "weight" & (.data$baseline_perc_change %>=% 20|.data$baseline_perc_change %<=% -20) ~ 3,
        
        .data$Test == "SystolicBP" & .data$stdvalue > 120 & .data$stdvalue %<=% 140 ~ 1,
        .data$Test == "SystolicBP" & .data$stdvalue > 140 & .data$stdvalue %<=% 160 ~ 2,
        .data$Test == "SystolicBP" & .data$stdvalue %>=% 160 ~ 3,
        
        .data$Test == "DiastolicBP" & .data$stdvalue > 80 & .data$stdvalue %<=% 90 ~ 1,
        .data$Test == "DiastolicBP" & .data$stdvalue > 90 & .data$stdvalue %<=% 100 ~ 2,
        .data$Test == "DiastolicBP" & .data$stdvalue %>=% 100 ~ 3
        
      )) %>%
    dplyr::filter(!is.na(VSToxGr)) %>%
    dplyr::select(.data$Subject,.data$instanceId,.data$TESTDAT_INT,.data$Timepoint,.data$Test,.data$VSToxGr)
  
  vs_ctcae <- vs_bl_cal %>%
    dplyr::left_join(vs_ctcae_grade,by = c("Subject","instanceId","TESTDAT_INT","Timepoint","Test")) %>%
    dplyr::mutate(VSToxGr = dplyr::case_when(
      grepl("1", .data$VSToxGr ) ~ "Grade 1: Mild",
      grepl("2", .data$VSToxGr ) ~ "Grade 2: Moderate",
      grepl("3", .data$VSToxGr ) ~ "Grade 3: Severe",
      grepl("4", .data$VSToxGr ) ~ "Grade 4: Life-Threatening",
      .data$Test == "weight" & is.na(.data$baseline_perc_change) ~ "Baseline not available",
      TRUE ~ "Grade 0: Normal"
    )) %>%
    dplyr::select(-.data$temp_flag)
  
  
  # Derive ae_tox_flag, mapping ae to vital, if there is grade in vital, but no grade in ae than flag ------------------------------------------------------------------------
  death <- SubjectInfo_tmp %>% dplyr::select(Subject = .data$`Subject name or identifier`, .data$`Death Date`) %>% dplyr::distinct()
  attr(death$Subject,"label") <- attr(vs_ctcae$Subject,"label")
  
  vs_small_tmp <- vs_ctcae %>%
    dplyr::filter(grepl("[1-4]", .data$VSToxGr))
  
  ae_small_tmp <- ae %>%
    dplyr::mutate(PT= stringr::str_to_lower(.data$AETERM_PT)) %>%
    dplyr::filter(!is.na(.data$AESTDAT_INT)) %>%
    dplyr::filter(.data$PT %in% vitalPT$PT)%>%
    dplyr::left_join(death, by = "Subject") %>%
    dplyr::left_join(vitalPT,by = "PT") %>%
    dplyr::mutate(vs_window_start_date = .data$AESTDAT_INT - lubridate::days(9),
                  vs_window_end_date = dplyr::case_when(
                    !is.na(.data$AEENDAT_INT) ~ .data$AEENDAT_INT + lubridate::days(9),
                    is.na(.data$AEENDAT_INT) &
                      !is.na(.data$`Death Date`) ~ .data$`Death Date`,
                    is.na(.data$AEENDAT_INT) &
                      is.na(.data$`Death Date`) ~ lubridate::as_datetime(NA),
                    TRUE ~ lubridate::as_datetime(NA)
                  ),
                  ongoing = dplyr::if_else(is.na(.data$AEENDAT_INT) &
                                             is.na(.data$`Death Date`),
                                           "Y",
                                           NA_character_)
    ) %>%
    dplyr::select(.data$Subject,
                  .data$PT,
                  .data$Test,
                  .data$vs_window_start_date,
                  .data$vs_window_end_date,
                  .data$AETOXGR,
                  .data$ongoing)
  
  # create indicator columns ------------------------------------------------
  vs_ind_tb <- vs_small_tmp %>%
    dplyr::left_join(ae_small_tmp, by = c("Subject","Test")) %>%
    dplyr::mutate(vs_in_window =
                    dplyr::if_else(.data$RecordDate >= .data$vs_window_start_date &
                                     (.data$RecordDate <= .data$vs_window_end_date |
                                        .data$ongoing == "Y"), 1L, 0L),
                  vs_ae_match =
                    dplyr::case_when(
                      is.na(.data$AETOXGR) &
                        is.na(.data$VSToxGr) ~
                        1L,
                      is.na(.data$AETOXGR) &
                        !is.na(.data$VSToxGr) ~
                        0L,
                      !is.na(.data$AETOXGR) &
                        is.na(.data$VSToxGr) ~
                        0L,
                      .data$AETOXGR == .data$VSToxGr ~
                        1L,
                      TRUE ~ 0L)) %>%
    dplyr::filter(.data$vs_in_window == 1L) %>%
    dplyr::group_by(.data$Subject,.data$instanceId,.data$TESTDAT_INT,.data$Timepoint,.data$Test) %>%
    dplyr::mutate(all_ae_toxgrades =
                    ifelse(all(is.na(.data$AETOXGR)),
                           NA_character_,
                           glue::glue_collapse(.data$AETOXGR,
                                               sep = " "))) %>%
    dplyr::ungroup() %>%
    dplyr::select(.data$Subject,
                  .data$instanceId,
                  .data$Timepoint,
                  .data$TESTDAT_INT,
                  .data$Test,
                  .data$PT,
                  .data$AETOXGR,
                  .data$ongoing,
                  .data$vs_in_window,
                  .data$vs_ae_match,
                  .data$all_ae_toxgrades
    ) %>%
    dplyr::distinct()
  
  
  vs_tox_flag_tb <- vs_small_tmp %>%
    dplyr::left_join(vs_ind_tb, by = c("Subject","instanceId","TESTDAT_INT","Timepoint","Test")) %>%
    dplyr::group_by(.data$Subject,.data$instanceId,.data$TESTDAT_INT,.data$Timepoint,.data$Test) %>%
    dplyr::mutate(ae_tox_flag =
                    ifelse(all(is.na(.data$vs_ae_match)),
                           "No AE near the Vital RecordDate",
                           ifelse(any(.data$vs_ae_match == 1L),
                                  "Yes",
                                  paste0("Vital toxgr",
                                         .data$VSToxGr,
                                         " vs. AE toxgr:",
                                         .data$all_ae_toxgrades)))) %>%
    dplyr::ungroup() %>%
    dplyr::distinct() %>%
    dplyr::select(.data$Subject,.data$instanceId,.data$TESTDAT_INT,.data$Timepoint,.data$Test,
                  .data$PT,.data$AETOXGR,.data$all_ae_toxgrades,.data$ae_tox_flag)
  
  
  vital <- vs_ctcae %>%
    dplyr::left_join(vs_tox_flag_tb, by = c("Subject","instanceId","TESTDAT_INT","Timepoint","Test")) %>%
    dplyr::rename(`Date of assessment Interpolated`= .data$TESTDAT_INT,
                  `Subject name or identifier`= .data$Subject)
  
  
  
  # Derive vs_tox_flag, mapping vital to ae, if there is grade in ae, but there is grade in vital, then flag it---------------------------------------------------------------------
  ae_small_tmp_2 <- ae %>%
    dplyr::mutate(PT= stringr::str_to_lower(.data$AETERM_PT)) %>%
    dplyr::filter(!is.na(.data$AESTDAT_INT)) %>%
    dplyr::filter(.data$PT %in% vitalPT$PT) %>%
    dplyr::mutate(ae_window_start_date =
                    .data$AESTDAT_INT - lubridate::days(3),
                  ae_window_end_date = dplyr::if_else(
                    !is.na(.data$AEENDAT_INT),
                    .data$AEENDAT_INT + lubridate::days(3),
                    .data$AESTDAT_INT + lubridate::days(7))
    ) %>%
    dplyr::select(.data$Subject,
                  .data$PT,
                  .data$RecordId,
                  .data$ae_window_start_date,
                  .data$ae_window_end_date,
                  .data$AETOXGR)
  
  vs_small_tmp2 <- vs_ctcae
  
  vs_ae_map <- ae_small_tmp_2 %>%
    dplyr::left_join(vitalPT,by = "PT") %>%
    dplyr::left_join(vs_small_tmp2,
                     by = c("Subject", "Test")) %>%
    dplyr::mutate(vs_in_window =
                    ifelse(
                      is.na(.data$RecordDate) |
                        is.na(.data$ae_window_start_date) |
                        is.na(.data$ae_window_end_date),
                      0L,
                      ifelse(
                        .data$RecordDate >= .data$ae_window_start_date &
                          .data$RecordDate <= .data$ae_window_end_date,
                        1L,
                        0L)),
                  vs_ae_match =
                    ifelse(
                      is.na(.data$AETOXGR) &
                        is.na(.data$VSToxGr),
                      1L,
                      ifelse(
                        is.na(.data$AETOXGR) &
                          !is.na(.data$VSToxGr),
                        0L,
                        ifelse(
                          !is.na(.data$AETOXGR) &
                            is.na(.data$VSToxGr),
                          0L,
                          ifelse(
                            .data$AETOXGR == .data$VSToxGr,
                            1L,
                            0L))))) %>%
    dplyr::filter(.data$vs_in_window == 1L) %>%
    dplyr::group_by(.data$Subject,.data$RecordId) %>%
    dplyr::mutate(all_vs_toxgrades =
                    ifelse(
                      all(is.na(.data$VSToxGr)),
                      NA_character_,
                      glue::glue_collapse(unique(.data$VSToxGr),
                                          sep = " "))) %>%
    dplyr::ungroup() %>%
    dplyr::select(.data$Subject,
                  .data$RecordId,
                  .data$VSToxGr,
                  .data$vs_ae_match,
                  .data$all_vs_toxgrades
    ) %>%
    dplyr::distinct()
  
  ae_small_tmp_3 <- ae_small_tmp_2 %>%
    dplyr::left_join(vs_ae_map, by = c("Subject","RecordId")) %>%
    dplyr::group_by(.data$Subject,.data$RecordId) %>%
    dplyr::mutate(vs_tox_flag =
                    dplyr::case_when(
                      all(is.na(.data$VSToxGr)) ~
                        "No vital near the AE Dates",
                      any(.data$vs_ae_match == 1L) ~
                        "Yes",
                      TRUE ~
                        paste0("AE toxgr ",
                               .data$AETOXGR,
                               " vs. Vital toxgr: ",
                               .data$all_vs_toxgrades))) %>%
    dplyr::ungroup() %>%
    dplyr::select(.data$Subject,
                  .data$RecordId,
                  .data$ae_window_start_date,
                  .data$ae_window_end_date,
                  .data$all_vs_toxgrades,
                  .data$vs_tox_flag) %>%
    dplyr::distinct()
  
  
  ae_to_vs_toxflag <- ae_small_tmp_3 %>%
    dplyr::left_join(ae %>% dplyr::select(.data$Subject, .data$RecordId, .data$AETERM_PT),., by = c("Subject","RecordId")) %>%
    dplyr::mutate(vs_tox_flag =
                    dplyr::if_else(!( stringr::str_to_lower(.data$AETERM_PT)) %in% vitalPT$PT,
                                   "This AE is not converted by vitalPT Table",
                                   .data$vs_tox_flag)) %>%
    dplyr::select(-.data$AETERM_PT)
  
  
  # Assign vs_tox_flag_tb to calling envir ------------------------------------------
  assign("vital", vital, envir = parent.frame())
  log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","vital returned"))
  
  assign("ae_to_vs_toxflag", ae_to_vs_toxflag, envir = parent.frame())
  log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","ae_to_vs_toxflag returned"))
  
}




