library(readxl)
library(dplyr)
library(magrittr)
library(stringr)
library(tidyr)

ALS_field<-read_excel("BGB-B3227-101_V1.0_05Aug2024_1028.xlsx",sheet = "Fields") %>% 
  dplyr::select(.data$FormOID,.data$FieldOID,.data$SASLabel) %>% 
  dplyr::mutate(Form = tolower(.data$FormOID)) %>% 
  dplyr::select(-.data$FormOID) %>% 
  tidyr::drop_na()

ALS_form<- read_excel("BGB-B3227-101_V1.0_05Aug2024_1028.xlsx",sheet = "Forms") %>% 
  dplyr::select(.data$OID,.data$DraftFormName) %>% 
  dplyr::mutate(Form =tolower(.data$OID)) %>% 
  dplyr::select(-.data$OID) %>% 
  tidyr::drop_na()


td_info <- ALS_field %>% 
  dplyr::left_join(ALS_form, by = "Form") %>% 
  dplyr::filter(grepl("End of Treatment",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("last dose|primary reason|Other",.data$SASLabel)) %>% 
  dplyr::mutate(Treatment = sub(".*- \\s*(.*)", "\\1", .data$DraftFormName))