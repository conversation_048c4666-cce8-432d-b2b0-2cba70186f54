const { app, BrowserWindow, dialog, shell, Menu } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const log = require('electron-log');
const findFreePort = require('find-free-port');

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

class StreamlitElectronApp {
  constructor() {
    this.mainWindow = null;
    this.splashWindow = null;
    this.streamlitProcess = null;
    this.streamlitPort = 8501;
    this.isDev = process.argv.includes('--dev');
    this.isQuitting = false;
  }

  async initialize() {
    try {
      // Set up app event handlers
      this.setupAppEvents();
      
      // Create splash screen
      await this.createSplashWindow();
      
      // Find free port for Streamlit
      await this.findAvailablePort();
      
      // Start Streamlit server
      await this.startStreamlitServer();
      
      // Create main window
      await this.createMainWindow();
      
      // Close splash screen
      this.closeSplashWindow();
      
    } catch (error) {
      log.error('Failed to initialize app:', error);
      this.showErrorDialog('Initialization Error', error.message);
      app.quit();
    }
  }

  setupAppEvents() {
    app.whenReady().then(() => this.initialize());
    
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.cleanup();
        app.quit();
      }
    });

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.initialize();
      }
    });

    app.on('before-quit', () => {
      this.isQuitting = true;
      this.cleanup();
    });
  }

  async createSplashWindow() {
    this.splashWindow = new BrowserWindow({
      width: 400,
      height: 300,
      frame: false,
      alwaysOnTop: true,
      transparent: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    // Create splash screen HTML
    const splashHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <style>
          body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            color: white;
            text-align: center;
          }
          .container {
            padding: 20px;
          }
          .logo {
            font-size: 48px;
            margin-bottom: 20px;
          }
          .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
          }
          .subtitle {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 30px;
          }
          .spinner {
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
          }
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="logo">🧬</div>
          <div class="title">MDR R Package Generator</div>
          <div class="subtitle">Starting application...</div>
          <div class="spinner"></div>
        </div>
      </body>
      </html>
    `;

    this.splashWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(splashHTML)}`);
    this.splashWindow.center();
  }

  async findAvailablePort() {
    try {
      const ports = await findFreePort(8501, 8600);
      this.streamlitPort = ports[0];
      log.info(`Using port ${this.streamlitPort} for Streamlit`);
    } catch (error) {
      log.warn('Could not find free port, using default 8501');
      this.streamlitPort = 8501;
    }
  }

  async startStreamlitServer() {
    return new Promise((resolve, reject) => {
      const pythonPath = this.isDev ? 'python' : this.getBundledPythonPath();
      const scriptPath = this.isDev ? 'start.py' : path.join(process.resourcesPath, 'start.py');
      
      log.info(`Starting Streamlit with Python: ${pythonPath}`);
      log.info(`Script path: ${scriptPath}`);
      
      this.streamlitProcess = spawn(pythonPath, [scriptPath, this.streamlitPort.toString()], {
        cwd: this.isDev ? process.cwd() : process.resourcesPath,
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.streamlitProcess.stdout.on('data', (data) => {
        const output = data.toString();
        log.info('Streamlit stdout:', output);
        
        if (output.includes('You can now view your Streamlit app') || 
            output.includes('Network URL:') || 
            output.includes('Local URL:')) {
          setTimeout(resolve, 2000); // Give Streamlit time to fully start
        }
      });

      this.streamlitProcess.stderr.on('data', (data) => {
        const error = data.toString();
        log.error('Streamlit stderr:', error);
        
        if (error.includes('Address already in use')) {
          reject(new Error(`Port ${this.streamlitPort} is already in use`));
        }
      });

      this.streamlitProcess.on('error', (error) => {
        log.error('Failed to start Streamlit process:', error);
        reject(error);
      });

      this.streamlitProcess.on('close', (code) => {
        log.info(`Streamlit process exited with code ${code}`);
        if (code !== 0 && !this.isQuitting) {
          reject(new Error(`Streamlit process exited with code ${code}`));
        }
      });

      // Timeout after 30 seconds
      setTimeout(() => {
        reject(new Error('Streamlit server failed to start within 30 seconds'));
      }, 30000);
    });
  }

  getBundledPythonPath() {
    const platform = process.platform;
    const pythonDir = path.join(process.resourcesPath, 'python-env');
    
    if (platform === 'win32') {
      return path.join(pythonDir, 'Scripts', 'python.exe');
    } else {
      return path.join(pythonDir, 'bin', 'python');
    }
  }

  async createMainWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      minWidth: 1000,
      minHeight: 700,
      show: false,
      icon: this.getAppIcon(),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true
      }
    });

    // Set up window events
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      this.mainWindow.focus();
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // Load Streamlit app
    const streamlitUrl = `http://localhost:${this.streamlitPort}`;
    log.info(`Loading Streamlit app from: ${streamlitUrl}`);
    
    try {
      await this.mainWindow.loadURL(streamlitUrl);
    } catch (error) {
      log.error('Failed to load Streamlit URL:', error);
      throw new Error('Could not connect to Streamlit server');
    }

    // Set up menu
    this.createMenu();
  }

  getAppIcon() {
    const platform = process.platform;
    if (platform === 'win32') {
      return path.join(__dirname, 'build-resources', 'icon.ico');
    } else if (platform === 'darwin') {
      return path.join(__dirname, 'build-resources', 'icon.icns');
    } else {
      return path.join(__dirname, 'build-resources', 'icon.png');
    }
  }

  createMenu() {
    const template = [
      {
        label: 'File',
        submenu: [
          {
            label: 'Reload',
            accelerator: 'CmdOrCtrl+R',
            click: () => {
              if (this.mainWindow) {
                this.mainWindow.reload();
              }
            }
          },
          { type: 'separator' },
          {
            label: 'Exit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            }
          }
        ]
      },
      {
        label: 'View',
        submenu: [
          {
            label: 'Toggle Developer Tools',
            accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
            click: () => {
              if (this.mainWindow) {
                this.mainWindow.webContents.toggleDevTools();
              }
            }
          },
          { type: 'separator' },
          {
            label: 'Actual Size',
            accelerator: 'CmdOrCtrl+0',
            click: () => {
              if (this.mainWindow) {
                this.mainWindow.webContents.setZoomLevel(0);
              }
            }
          },
          {
            label: 'Zoom In',
            accelerator: 'CmdOrCtrl+Plus',
            click: () => {
              if (this.mainWindow) {
                const currentZoom = this.mainWindow.webContents.getZoomLevel();
                this.mainWindow.webContents.setZoomLevel(currentZoom + 1);
              }
            }
          },
          {
            label: 'Zoom Out',
            accelerator: 'CmdOrCtrl+-',
            click: () => {
              if (this.mainWindow) {
                const currentZoom = this.mainWindow.webContents.getZoomLevel();
                this.mainWindow.webContents.setZoomLevel(currentZoom - 1);
              }
            }
          }
        ]
      },
      {
        label: 'Help',
        submenu: [
          {
            label: 'About',
            click: () => {
              dialog.showMessageBox(this.mainWindow, {
                type: 'info',
                title: 'About MDR R Package Generator',
                message: 'MDR R Package Generator',
                detail: 'Version 1.0.0\n\nStreamline your clinical trial data analysis with automated R function generation.'
              });
            }
          }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  closeSplashWindow() {
    if (this.splashWindow) {
      this.splashWindow.close();
      this.splashWindow = null;
    }
  }

  cleanup() {
    log.info('Cleaning up application...');
    
    if (this.streamlitProcess) {
      log.info('Terminating Streamlit process...');
      this.streamlitProcess.kill('SIGTERM');
      
      // Force kill after 5 seconds if still running
      setTimeout(() => {
        if (this.streamlitProcess && !this.streamlitProcess.killed) {
          log.warn('Force killing Streamlit process...');
          this.streamlitProcess.kill('SIGKILL');
        }
      }, 5000);
    }
  }

  showErrorDialog(title, message) {
    dialog.showErrorBox(title, message);
  }
}

// Create and start the app
const electronApp = new StreamlitElectronApp();

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  log.error('Uncaught Exception:', error);
  electronApp.showErrorDialog('Application Error', error.message);
});

process.on('unhandledRejection', (reason, promise) => {
  log.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
