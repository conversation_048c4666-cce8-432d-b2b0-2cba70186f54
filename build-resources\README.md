# Build Resources

This directory contains resources needed for building the Electron application.

## Required Files

### Icons
You need to provide the following icon files:

- **icon.ico** - Windows icon (256x256 pixels recommended)
- **icon.icns** - macOS icon (512x512 pixels recommended)  
- **icon.png** - Linux icon (512x512 pixels recommended)

### Icon Requirements

#### Windows (.ico)
- Format: ICO
- Sizes: 16x16, 32x32, 48x48, 64x64, 128x128, 256x256
- Color depth: 32-bit with alpha channel

#### macOS (.icns)
- Format: ICNS
- Sizes: 16x16, 32x32, 128x128, 256x256, 512x512, 1024x1024
- Retina variants: @2x versions for each size

#### Linux (.png)
- Format: PNG
- Size: 512x512 pixels
- Color depth: 32-bit with alpha channel

## Creating Icons

### From PNG Source
If you have a high-resolution PNG (1024x1024 or larger):

1. **For Windows (.ico):**
   - Use online converters like favicon.io or convertio.co
   - Or use ImageMagick: `convert icon.png -define icon:auto-resize=256,128,64,48,32,16 icon.ico`

2. **For macOS (.icns):**
   - Use `iconutil` on macOS: 
     ```bash
     mkdir icon.iconset
     sips -z 16 16 icon.png --out icon.iconset/icon_16x16.png
     sips -z 32 32 icon.png --out icon.iconset/<EMAIL>
     sips -z 32 32 icon.png --out icon.iconset/icon_32x32.png
     sips -z 64 64 icon.png --out icon.iconset/<EMAIL>
     sips -z 128 128 icon.png --out icon.iconset/icon_128x128.png
     sips -z 256 256 icon.png --out icon.iconset/<EMAIL>
     sips -z 256 256 icon.png --out icon.iconset/icon_256x256.png
     sips -z 512 512 icon.png --out icon.iconset/<EMAIL>
     sips -z 512 512 icon.png --out icon.iconset/icon_512x512.png
     sips -z 1024 1024 icon.png --out icon.iconset/<EMAIL>
     iconutil -c icns icon.iconset
     ```

3. **For Linux (.png):**
   - Simply resize to 512x512: `sips -z 512 512 icon.png --out icon.png`

### Design Guidelines

#### Visual Design
- Use a simple, recognizable design
- Ensure good contrast at small sizes
- Avoid fine details that won't be visible at 16x16
- Consider the app's purpose (scientific/medical tool)

#### Suggested Design Elements
- DNA helix or molecular structure
- Data visualization elements (charts, graphs)
- R programming language symbol
- Medical/clinical research symbols
- Clean, professional appearance

#### Color Scheme
- Primary: Scientific blue (#007bff)
- Secondary: Professional gray (#6c757d)
- Accent: Success green (#28a745)
- Background: Clean white or transparent

## Placeholder Icons

Until you create custom icons, you can use placeholder icons:

1. Create simple colored squares with the app initials "MDR"
2. Use online icon generators
3. Use the default Electron icon temporarily

## Testing Icons

After creating icons, test them by:

1. Building the app: `npm run build`
2. Checking the generated installer
3. Installing and verifying the icon appears correctly in:
   - Application launcher
   - Taskbar/dock
   - Window title bar
   - System notifications

## Icon Tools

### Free Tools
- GIMP (cross-platform)
- Paint.NET (Windows)
- Preview (macOS)
- Online converters (favicon.io, convertio.co)

### Professional Tools
- Adobe Illustrator
- Sketch (macOS)
- Figma (web-based)
- Affinity Designer

## Notes

- Icons are embedded in the final application
- Larger file sizes will increase the app bundle size
- Vector-based source files (SVG, AI) are recommended for scaling
- Test icons on different backgrounds (light/dark themes)
- Consider accessibility (color blindness, contrast)

## Current Status

⚠️ **Icons needed**: Please add the required icon files to this directory before building for distribution.
