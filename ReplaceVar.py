import streamlit as st
import pandas as pd
import re
from pathlib import Path
import io
import zipfile

def load_r_file(uploaded_file):
    """Load R file content from uploaded file."""
    return uploaded_file.getvalue().decode('utf-8')

def save_r_file(file_path, content):
    """Save modified R file content."""
    with open(file_path, 'w') as f:
        f.write(content)

def load_config_file():
    """Load and validate the configuration file."""
    try:
        config_df = pd.read_excel("Config_MDR_var_replacement.xlsx")
        required_columns = ['Var_to_Replace', 'FieldPattern', 'FormPattern']
        
        # Check if required columns exist
        missing_cols = [col for col in required_columns if col not in config_df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns in config file: {', '.join(missing_cols)}")
            
        return config_df
    except Exception as e:
        raise Exception(f"Error loading config file: {str(e)}")

def find_matching_variables(user_df, config_df):
    """
    Find matching variables based on config patterns and user uploaded data.
    Returns a dictionary mapping old variables to new variables.
    """
    if 'FieldOID' not in user_df.columns or 'DraftFormName' not in user_df.columns or 'SASLabel' not in user_df.columns:
        raise ValueError("Uploaded file must contain 'FieldOID', 'SASLabel', and 'DraftFormName' columns")
    
    variable_mappings = {}
    replacements_info = []
    
    for _, config_row in config_df.iterrows():
        old_var = config_row['Var_to_Replace']
        field_pattern = config_row['FieldPattern']
        form_pattern = config_row['FormPattern']
        
        # Filter user data based on form pattern first
        matching_forms = user_df[user_df['DraftFormName'].str.contains(form_pattern, regex=True, na=False)]
        
        if not matching_forms.empty:
            # Then find matching field pattern within the filtered forms using SASLabel
            matching_vars = matching_forms[
                matching_forms['SASLabel'].str.contains(field_pattern, regex=True, na=False)
            ]
            
            if not matching_vars.empty:
                # Use the FieldOID as the new variable name
                new_var = matching_vars.iloc[0]['FieldOID']
                variable_mappings[old_var] = new_var
                replacements_info.append({
                    'old_var': old_var,
                    'new_var': new_var,
                    'form': matching_vars.iloc[0]['DraftFormName'],
                    'pattern_used': field_pattern,
                    'matched_saslabel': matching_vars.iloc[0]['SASLabel']  # Added for verification
                })
    
    return variable_mappings, replacements_info

def replace_variables(r_code, variable_mappings):
    """Replace variables in R code based on mapping dictionary."""
    modified_code = r_code
    replacements_made = []
    
    for old_var, new_var in variable_mappings.items():
        if pd.notna(old_var) and pd.notna(new_var):
            # Convert to string and strip whitespace
            old_var = str(old_var).strip()
            new_var = str(new_var).strip()
            
            if old_var and new_var:  # Check if non-empty after stripping
                # Create patterns for base variable and variables with _PT and _PROD suffixes
                base_pattern = r'(?<=[^\w])' + re.escape(old_var) + r'(?=[^\w])'
                pt_pattern = r'(?<=[^\w])' + re.escape(old_var + '_PT') + r'(?=[^\w])'
                soc_pattern = r'(?<=[^\w])' + re.escape(old_var + '_SOC') + r'(?=[^\w])'
                prod_pattern = r'(?<=[^\w])' + re.escape(old_var + '_PROD') + r'(?=[^\w])'
                Int_pattern = r'(?<=[^\w])' + re.escape(old_var + '_INT') + r'(?=[^\w])'
                
                # Replace base variable
                new_code = re.sub(base_pattern, new_var, modified_code)
                
                # Replace variables with _PT suffix
                if re.search(pt_pattern, modified_code):
                    new_code = re.sub(pt_pattern, new_var + '_PT', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_PT' → '{new_var}_PT'")
                
                # Replace variables with _PROD suffix
                if re.search(prod_pattern, modified_code):
                    new_code = re.sub(prod_pattern, new_var + '_PROD', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_PROD' → '{new_var}_PROD'")
                
                # Replace variables with _SOC suffix    
                if re.search(soc_pattern, modified_code):
                    new_code = re.sub(soc_pattern, new_var + '_SOC', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_SOC' → '{new_var}_SOC'")
                
                # Replace variables with _INT suffix
                if re.search(Int_pattern, modified_code):
                    new_code = re.sub(Int_pattern, new_var + '_INT', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_INT' → '{new_var}_INT'")
                
                # Check if any base variable replacements were made
                if new_code != modified_code:
                    replacements_made.append(f"'{old_var}' → '{new_var}'")
                    modified_code = new_code
    
    return modified_code, replacements_made

def create_zip_of_modified_files(modified_files):
    """Create a zip file containing all modified files."""
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        for filename, content in modified_files.items():
            zip_file.writestr(filename, content)
    return zip_buffer.getvalue()

def main():
    st.set_page_config(layout="wide")  # Make the page wide by default
    
    # Initialize session state for storing modified code and confirmation status
    if 'modified_files' not in st.session_state:
        st.session_state.modified_files = {}
    if 'confirmed_files' not in st.session_state:
        st.session_state.confirmed_files = {}
    if 'edited_files' not in st.session_state:
        st.session_state.edited_files = {}
    if 'confirmation_status' not in st.session_state:
        st.session_state.confirmation_status = {}
    
    # Add custom CSS to style the text area and code blocks
    st.markdown("""
        <style>
        /* Style for both code blocks and text areas */
        .stCode, .stTextArea textarea {
            font-family: monospace !important;
            font-size: 1em !important;
            line-height: 1.5 !important;
            padding: 10px !important;
            min-height: 500px !important;
            height: auto !important;
            background-color: #f0f2f6 !important;
            color: #000000 !important;
            border: 1px solid #cccccc !important;
            border-radius: 4px !important;
            overflow-y: auto !important;
            white-space: pre !important;
            tab-size: 4 !important;
        }
        
        /* Remove padding from text area container */
        .stTextArea div[data-baseweb="textarea"] {
            background-color: transparent !important;
            padding: 0 !important;
        }
        
        /* Ensure consistent width */
        .stTextArea, .stCode {
            width: 100% !important;
        }
        
        /* Hide scrollbar when not needed */
        .stTextArea textarea::-webkit-scrollbar, .stCode::-webkit-scrollbar {
            width: 8px !important;
        }
        
        .stTextArea textarea::-webkit-scrollbar-thumb, .stCode::-webkit-scrollbar-thumb {
            background-color: #999999 !important;
            border-radius: 4px !important;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def confirm_changes(file_name, edited_code):
        st.session_state.confirmed_files[file_name] = edited_code
        st.session_state.confirmation_status[file_name] = True
    
    st.title("R Variable Name Replacement Tool")
    st.write("""
    This tool helps you replace variable names in R files based on the configuration file and your uploaded data.
    
    Please upload:
    1. Your variable mapping Excel file containing:
       - FieldOID column: Contains the new variable names to be used
       - SASLabel column: Used for pattern matching
       - DraftFormName column: Contains the form names
    2. One or more R files to process
    
    The tool will automatically match the SASLabel patterns from the config file and use the corresponding FieldOID values as replacements.
    """)

    # Load config file first
    try:
        config_df = load_config_file()
        st.success("Configuration file loaded successfully")
        
        # Show config preview
        with st.expander("View Configuration"):
            st.dataframe(config_df)
    except Exception as e:
        st.error(f"Failed to load configuration file: {str(e)}")
        return

    # File uploader for variable mapping data
    mapping_file = st.file_uploader("Upload Variable Mapping Table (Excel)", type=['xlsx'])
    
    # File uploader for R files
    r_files = st.file_uploader("Upload R Files", type=['R'], accept_multiple_files=True)
    
    if mapping_file is not None and r_files:
        try:
            # Read mapping data
            user_df = pd.read_excel(mapping_file)
            
            # Validate required columns
            required_columns = ['FieldOID', 'SASLabel', 'DraftFormName']
            missing_cols = [col for col in required_columns if col not in user_df.columns]
            if missing_cols:
                st.error(f"Uploaded mapping file must contain these columns: {', '.join(missing_cols)}")
                st.write("Available columns in your file:", ", ".join(f"'{col}'" for col in user_df.columns))
                return
            
            # Find matching variables
            variable_mappings, replacements_info = find_matching_variables(user_df, config_df)
            
            if not variable_mappings:
                st.warning("No matching variables found based on the patterns in the config file.")
                return
            
            # Show matched variables
            st.write("Matched Variables:")
            matched_df = pd.DataFrame(replacements_info)
            st.dataframe(matched_df)

            # Create a multiselect for R files
            r_file_names = [file.name for file in r_files]
            selected_files = st.multiselect(
                "Select R files to process",
                r_file_names,
                default=r_file_names
            )
            
            if st.button("Replace Variables in Selected Files"):
                # Clear previous confirmations when generating new replacements
                st.session_state.confirmed_files = {}
                st.session_state.edited_files = {}
                st.session_state.confirmation_status = {}
                
                for r_file in r_files:
                    if r_file.name in selected_files:
                        # Read the R file content
                        r_code = load_r_file(r_file)
                        
                        # Perform replacement
                        modified_code, replacements = replace_variables(r_code, variable_mappings)
                        
                        if replacements:
                            # Store modified file
                            modified_filename = r_file.name  # Remove the _modified suffix
                            st.session_state.modified_files[r_file.name] = {
                                'original': r_code,
                                'modified': modified_code,
                                'replacements': replacements,
                                'modified_filename': modified_filename
                            }
            
            # Display the processed files
            for r_file in r_files:
                if r_file.name in selected_files and r_file.name in st.session_state.modified_files:
                    file_info = st.session_state.modified_files[r_file.name]
                    
                    # Create an expander for this file's results
                    with st.expander(f"Results for: {r_file.name}", expanded=True):
                        # Show replacements made for this file in a more compact way
                        st.write("**Replacements Made:**")
                        replacements_text = "\n".join([f"• {replacement}" for replacement in file_info['replacements']])
                        st.text(replacements_text)
                        
                        # Show side-by-side comparison
                        st.write("#### Code Comparison")
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            st.write("**Original Code**")
                            st.code(file_info['original'], language="r", line_numbers=True)
                            # Get the height of the original code block for reference
                            num_lines = len(file_info['original'].splitlines())
                            line_height = 20  # approximate height per line in pixels
                            code_height = max(500, num_lines * line_height)  # minimum 500px
                        
                        with col2:
                            st.write("**Modified Code (Editable)**")
                            # Create a unique key for each text area
                            edited_code = st.text_area(
                                "",  # Remove label as we already have the header
                                value=file_info['modified'] if r_file.name not in st.session_state.edited_files else st.session_state.edited_files[r_file.name],
                                height=code_height,  # Use the same height as original
                                key=f"edit_{r_file.name}",
                                label_visibility="collapsed"
                            )
                            st.session_state.edited_files[r_file.name] = edited_code
                            
                            # Add confirmation button and status for each file
                            col_confirm, col_status = st.columns([1, 2])
                            with col_confirm:
                                if st.button("Confirm Changes", key=f"confirm_{r_file.name}"):
                                    confirm_changes(r_file.name, edited_code)
                            with col_status:
                                if r_file.name in st.session_state.confirmation_status:
                                    st.success("✓ Changes confirmed")
                        
                        # Add a visual separator between files
                        st.markdown("---")
            
            # Add download button for confirmed modified files
            if st.session_state.confirmed_files:
                st.write("### Download Confirmed Files")
                confirmed_zip = {
                    name: content  # Remove the _modified suffix
                    for name, content in st.session_state.confirmed_files.items()
                }
                zip_data = create_zip_of_modified_files(confirmed_zip)
                
                col1, col2 = st.columns([1, 2])
                with col1:
                    st.download_button(
                        label="Download All Confirmed Modified Files",
                        data=zip_data,
                        file_name="modified_R_files.zip",
                        mime="application/zip",
                        key="download_all_modified"
                    )
                with col2:
                    st.write("Files ready for download:")
                    for filename in st.session_state.confirmed_files.keys():
                        st.write(f"- {filename} (confirmed)")

        except Exception as e:
            st.error(f"Error processing files: {str(e)}")
            st.write("Please ensure your files are properly formatted and contain the necessary content.")

if __name__ == "__main__":
    main() 