#!/usr/bin/env python3
"""
Test script to verify the updated diameter lesion logic in controller_generator.py

This script shows how the updated functionality works with different numbers of diameter lesions.
"""

import pandas as pd
import re

class MockControllerGenerator:
    """Mock class to test the diameter lesion logic without needing an actual ALS file."""

    def __init__(self, lesion_letters):
        """Initialize with a list of lesion letters (e.g., ['A', 'B', 'C', 'D', 'E'])."""
        self.diameter_lesion_fields = {
            'lesion_letters': lesion_letters,
            'diameter_fields': pd.DataFrame()  # Mock empty dataframe
        }

    def _generate_diameter_lesion_code(self):
        """Generate dynamic R code for diameter lesion processing."""
        lesion_letters = self.diameter_lesion_fields.get('lesion_letters', [])

        if len(lesion_letters) == 2:
            # If we have exactly 2 lesions, use simplified code
            return """  standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
    dplyr::mutate(tl_split_b_diam = dplyr::if_else(
        is.na(.data$tl_split_b_diam), NA_real_,
        dplyr::select(.,
                    c(.data$tl_split_b_diam
                      )) %>% rowSums(na.rm = TRUE))) %>%
    dplyr::select(-.data$tl_split_b_diam)"""
        elif len(lesion_letters) == 3:
            # If we have exactly 3 lesions, use the original code
            return """  standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
    dplyr::mutate(tl_split_b_diam = dplyr::if_else(
        is.na(.data$tl_split_b_diam) &
        is.na(.data$tl_split_c_diam) , NA_real_,
        dplyr::select(.,
                    c(.data$tl_split_b_diam,
                      .data$tl_split_c_diam
                      )) %>% rowSums(na.rm = TRUE))) %>%
    dplyr::select(-.data$tl_split_c_diam)"""
        elif len(lesion_letters) > 3:
            # Generate dynamic code for more than 3 lesions
            # Start with the base case from 3 lesions (A->tl_split_b_diam, B->tl_split_c_diam)
            # and add additional fields for lesions beyond C
            na_conditions = [
                "is.na(.data$tl_split_b_diam)",
                "is.na(.data$tl_split_c_diam)"
            ]
            select_columns = [".data$tl_split_b_diam", ".data$tl_split_c_diam"]
            select_columns_to_remove = [".data$tl_split_c_diam"]

            # Add additional fields for lesions beyond the first 3 (A, B, C)
            # Start from the 4th lesion (index 3) which corresponds to D
            for i in range(3, len(lesion_letters)):
                letter = lesion_letters[i]
                # Map D->d, E->e, F->f, etc.
                field_name = f"tl_split_{letter.lower()}_diam"
                na_conditions.append(f"is.na(.data${field_name})")
                select_columns.append(f".data${field_name}")
                select_columns_to_remove.append(f".data${field_name}")

            # Join conditions with &
            na_condition_str = " &\n        ".join(na_conditions)

            # Join select columns with comma and proper indentation
            select_columns_str = ",\n                      ".join(select_columns)

            # Join columns to remove (all except the first one which becomes the sum)
            if select_columns_to_remove:
                remove_columns_str = ", ".join(select_columns_to_remove)
                select_remove_str = f" %>%\n    dplyr::select(-{remove_columns_str})"
            else:
                select_remove_str = ""

            return f"""  standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
    dplyr::mutate(tl_split_b_diam = dplyr::if_else(
        {na_condition_str} , NA_real_,
        dplyr::select(.,
                    c({select_columns_str}
                      )) %>% rowSums(na.rm = TRUE))){select_remove_str}"""
        else:
            # Fallback for cases with fewer than 2 lesions or no lesions
            return """  standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
    dplyr::mutate(tl_split_b_diam = dplyr::if_else(
        is.na(.data$tl_split_b_diam), NA_real_,
        dplyr::select(.,
                    c(.data$tl_split_b_diam
                      )) %>% rowSums(na.rm = TRUE))) %>%
    dplyr::select(-.data$tl_split_b_diam)"""


def test_updated_diameter_lesion_scenarios():
    """Test different scenarios of diameter lesion configurations with updated logic."""

    print("=== Testing Updated Dynamic Diameter Lesion Logic ===\n")

    # Test Case 1: Only A lesion (should use fallback)
    print("Test Case 1: Only Lesion A (should use fallback)")
    print("=" * 50)
    generator1 = MockControllerGenerator(['A'])
    code1 = generator1._generate_diameter_lesion_code()
    print(code1)
    print("\n")

    # Test Case 2: A, B lesions (should use simplified code)
    print("Test Case 2: Lesions A, B (should use simplified code)")
    print("=" * 55)
    generator2 = MockControllerGenerator(['A', 'B'])
    code2 = generator2._generate_diameter_lesion_code()
    print(code2)
    print("\n")

    # Test Case 3: A, B, C lesions (should use original code)
    print("Test Case 3: Lesions A, B, C (should use original code)")
    print("=" * 55)
    generator3 = MockControllerGenerator(['A', 'B', 'C'])
    code3 = generator3._generate_diameter_lesion_code()
    print(code3)
    print("\n")

    # Test Case 4: A, B, C, D lesions (should use dynamic code)
    print("Test Case 4: Lesions A, B, C, D (should use dynamic code)")
    print("=" * 55)
    generator4 = MockControllerGenerator(['A', 'B', 'C', 'D'])
    code4 = generator4._generate_diameter_lesion_code()
    print(code4)
    print("\n")

    # Test Case 5: A, B, C, D, E lesions (should use dynamic code with more fields)
    print("Test Case 5: Lesions A, B, C, D, E (should use dynamic code with more fields)")
    print("=" * 75)
    generator5 = MockControllerGenerator(['A', 'B', 'C', 'D', 'E'])
    code5 = generator5._generate_diameter_lesion_code()
    print(code5)
    print("\n")

    # Test Case 6: No lesions (should use fallback)
    print("Test Case 6: No lesions (should use fallback)")
    print("=" * 45)
    generator6 = MockControllerGenerator([])
    code6 = generator6._generate_diameter_lesion_code()
    print(code6)
    print("\n")


if __name__ == "__main__":
    test_updated_diameter_lesion_scenarios()
