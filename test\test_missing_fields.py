import os
import sys
import pandas as pd
from patient_profile_generator import PatientProfileGenerator

def create_mock_als_file(file_path, include_dlt=False, include_clinical_interest=False):
    """Create a mock ALS file with or without DLT and Clinical interest fields."""
    # Create a writer to save the Excel file
    writer = pd.ExcelWriter(file_path, engine='openpyxl')
    
    # Create Forms sheet
    forms_data = {
        'OID': ['F.AE', 'F.CM', 'F.RS_R', 'F.PPROC'],
        'DraftFormName': [
            'Adverse Events', 
            'Prior/Concomitant Medications', 
            'Response Assessment', 
            'Prior/Concomitant Procedures/Surgeries'
        ],
        'Form': ['ae', 'cm', 'rs_r', 'pproc']
    }
    forms_df = pd.DataFrame(forms_data)
    forms_df.to_excel(writer, sheet_name='Forms', index=False)
    
    # Create Fields sheet with or without DLT and Clinical interest fields
    fields_data = {
        'FormOID': [
            'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE',
            'F.CM', 'F.CM', 'F.CM', 'F.CM', 'F.CM',
            'F.RS_R', 'F.RS_R',
            'F.PPROC', 'F.PPROC'
        ],
        'FieldOID': [
            'AESTDAT', 'AEENDAT', 'AETERM', 'AETERM_PT', 'AETERM_SOC', 'AETOXGR', 'AESDTH', 'AESER',
            'CMSTDAT', 'CMENDAT', 'CMTRT', 'CMTRT_PROD', 'CMONGO',
            'RSORRES', 'RSDAT',
            'PRSTDAT', 'PRTRT'
        ],
        'SASLabel': [
            'AE Start date', 'AE End date', 'Adverse event', 'AE Dictionary-Derived Term', 'AE SOC', 'Toxicity grade', 'Result in death', 'Was adverse event serious',
            'CM Start date', 'CM End date', 'Medication name', 'CM Preferred Term', 'Ongoing',
            'Overall response', 'Date of overall response',
            'Date of procedure/surgery', 'Type or name of procedure/surgery'
        ]
    }
    
    # Add DLT field if requested
    if include_dlt:
        fields_data['FormOID'].append('F.AE')
        fields_data['FieldOID'].append('AEDLT')
        fields_data['SASLabel'].append('DLT')
    
    # Add Clinical interest field if requested
    if include_clinical_interest:
        fields_data['FormOID'].append('F.AE')
        fields_data['FieldOID'].append('AECI')
        fields_data['SASLabel'].append('clinical interest')
    
    fields_df = pd.DataFrame(fields_data)
    fields_df.to_excel(writer, sheet_name='Fields', index=False)
    
    # Create DataDictionaryEntries sheet
    data_dic_data = {
        'DataDictionaryName': ['RSORRES'],
        'CodedData': ['CR', 'PR', 'SD', 'PD', 'NE'],
        'UserDataString': [
            'Complete response (CR)', 
            'Partial response (PR)', 
            'Stable disease (SD)', 
            'Progressive disease (PD)', 
            'Not evaluable (NE)'
        ]
    }
    data_dic_df = pd.DataFrame(data_dic_data)
    data_dic_df.to_excel(writer, sheet_name='DataDictionaryEntries', index=False)
    
    # Save the Excel file
    writer.close()
    
    return file_path

def main():
    # Test with no DLT and no Clinical interest fields
    mock_als_file1 = "mock_als_no_dlt_no_ci.xlsx"
    create_mock_als_file(mock_als_file1, include_dlt=False, include_clinical_interest=False)
    
    # Test with DLT but no Clinical interest fields
    mock_als_file2 = "mock_als_dlt_no_ci.xlsx"
    create_mock_als_file(mock_als_file2, include_dlt=True, include_clinical_interest=False)
    
    # Test with no DLT but with Clinical interest fields
    mock_als_file3 = "mock_als_no_dlt_ci.xlsx"
    create_mock_als_file(mock_als_file3, include_dlt=False, include_clinical_interest=True)
    
    # Test with both DLT and Clinical interest fields
    mock_als_file4 = "mock_als_dlt_ci.xlsx"
    create_mock_als_file(mock_als_file4, include_dlt=True, include_clinical_interest=True)
    
    # Generate R functions for each case
    for i, file_path in enumerate([mock_als_file1, mock_als_file2, mock_als_file3, mock_als_file4], 1):
        generator = PatientProfileGenerator(file_path)
        study_id = f"test_missing_fields_{i}"
        r_function = generator.generate_function(study_id)
        
        output_file = f"patientProfile_{study_id}_generated.R"
        with open(output_file, "w") as f:
            f.write(r_function)
        
        print(f"\nR function for case {i} generated and saved to '{output_file}'")
    
    # Clean up
    for file_path in [mock_als_file1, mock_als_file2, mock_als_file3, mock_als_file4]:
        os.remove(file_path)

if __name__ == "__main__":
    main()
