const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // File operations
  openFile: () => ipcRenderer.invoke('open-file'),
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  
  // System information
  getPlatform: () => process.platform,
  getArch: () => process.arch,
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // Notifications
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body),
  
  // Logging
  log: {
    info: (message) => ipc<PERSON>enderer.invoke('log', 'info', message),
    warn: (message) => ipcRenderer.invoke('log', 'warn', message),
    error: (message) => ipcRenderer.invoke('log', 'error', message)
  }
});

// Security: Remove node integration and enable context isolation
window.addEventListener('DOMContentLoaded', () => {
  // Add any DOM-ready initialization here
  console.log('MDR R Package Generator - Electron Preload Script Loaded');
});

// Prevent new window creation
window.addEventListener('click', (e) => {
  if (e.target.tagName === 'A' && e.target.href && e.target.target === '_blank') {
    e.preventDefault();
    // Let the main process handle external links
  }
});

// Handle keyboard shortcuts
window.addEventListener('keydown', (e) => {
  // Prevent certain shortcuts that might interfere with the app
  if (e.ctrlKey || e.metaKey) {
    switch (e.key) {
      case 'w':
        // Prevent Ctrl+W from closing the window in the web content
        if (!e.shiftKey) {
          e.preventDefault();
        }
        break;
      case 'n':
        // Prevent Ctrl+N from opening new window
        e.preventDefault();
        break;
      case 't':
        // Prevent Ctrl+T from opening new tab
        e.preventDefault();
        break;
    }
  }
});

// Add custom styling for better desktop app experience
const style = document.createElement('style');
style.textContent = `
  /* Hide Streamlit's default menu and toolbar */
  .stApp > header[data-testid="stHeader"] {
    display: none !important;
  }
  
  /* Adjust main content area */
  .stApp > div[data-testid="stAppViewContainer"] {
    padding-top: 0 !important;
  }
  
  /* Custom scrollbar for better desktop experience */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  
  /* Improve button and input styling for desktop */
  .stButton > button {
    transition: all 0.2s ease;
  }
  
  .stButton > button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  }
  
  /* Better file uploader styling */
  .stFileUploader {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    transition: border-color 0.2s ease;
  }
  
  .stFileUploader:hover {
    border-color: #007bff;
  }
  
  /* Improve sidebar styling */
  .css-1d391kg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  /* Better text input styling */
  .stTextInput > div > div > input {
    border-radius: 6px;
    border: 1px solid #ddd;
    transition: border-color 0.2s ease;
  }
  
  .stTextInput > div > div > input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
  }
`;

// Wait for DOM to be ready before adding styles
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    document.head.appendChild(style);
  });
} else {
  document.head.appendChild(style);
}

// Add a mutation observer to apply styles when Streamlit content loads
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.type === 'childList') {
      // Check if Streamlit app has loaded
      const stApp = document.querySelector('.stApp');
      if (stApp && !document.querySelector('#electron-custom-styles')) {
        style.id = 'electron-custom-styles';
        document.head.appendChild(style);
      }
    }
  });
});

// Start observing
observer.observe(document.body, {
  childList: true,
  subtree: true
});

// Clean up observer when page unloads
window.addEventListener('beforeunload', () => {
  observer.disconnect();
});
