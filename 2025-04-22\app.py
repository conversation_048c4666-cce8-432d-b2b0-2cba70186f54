import streamlit as st
import pandas as pd
import re
from pathlib import Path
import io
import zipfile
from dose_merge_generator import DoseMergeGenerator
from td_merge_generator import TDMergeGenerator
import openpyxl
from openpyxl import Workbook
import numpy as np
import warnings

# Suppress openpyxl Data Validation warning
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

def load_r_file(uploaded_file):
    """Load R file content from uploaded file."""
    return uploaded_file.getvalue().decode('utf-8')

def save_r_file(file_path, content):
    """Save modified R file content."""
    with open(file_path, 'w') as f:
        f.write(content)

def confirm_changes(file_name, edited_code):
    """Confirm changes made to a file."""
    st.session_state.confirmed_files[file_name] = edited_code
    st.session_state.confirmation_status[file_name] = True

def load_config_file():
    """Load and validate the configuration file."""
    try:
        config_df = pd.read_excel("Config_MDR_var_replacement.xlsx", engine='openpyxl')
        required_columns = ['Var_to_Replace', 'FieldPattern', 'FormPattern']
        
        # Check if required columns exist
        missing_cols = [col for col in required_columns if col not in config_df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns in config file: {', '.join(missing_cols)}")
            
        return config_df
    except Exception as e:
        raise Exception(f"Error loading config file: {str(e)}")

def find_matching_variables(var_label_value, config_df):
    """
    Find matching variables based on config patterns and var_label_value data.
    Returns a dictionary mapping old variables to new variables.
    """
    if 'FieldOID' not in var_label_value.columns or 'DraftFormName' not in var_label_value.columns or 'SASLabel' not in var_label_value.columns:
        raise ValueError("var_label_value must contain 'FieldOID', 'SASLabel', and 'DraftFormName' columns")
    
    variable_mappings = {}
    replacements_info = []
    
    for _, config_row in config_df.iterrows():
        old_var = config_row['Var_to_Replace']
        field_pattern = config_row['FieldPattern']
        form_pattern = config_row['FormPattern']
        
        # Filter data based on form pattern first
        matching_forms = var_label_value[var_label_value['DraftFormName'].str.contains(form_pattern, regex=True, na=False)]
        
        if not matching_forms.empty:
            # Then find matching field pattern within the filtered forms using SASLabel
            matching_vars = matching_forms[
                matching_forms['SASLabel'].str.contains(field_pattern, regex=True, na=False)
            ]
            
            if not matching_vars.empty:
                # Use the FieldOID as the new variable name
                new_var = matching_vars.iloc[0]['FieldOID']
                variable_mappings[old_var] = new_var
                replacements_info.append({
                    'old_var': old_var,
                    'new_var': new_var,
                    'form': matching_vars.iloc[0]['DraftFormName'],
                    'pattern_used': field_pattern,
                    'matched_saslabel': matching_vars.iloc[0]['SASLabel']  # Added for verification
                })
    
    return variable_mappings, replacements_info

def replace_variables(r_code, variable_mappings):
    """Replace variables in R code based on mapping dictionary."""
    modified_code = r_code
    replacements_made = []
    
    for old_var, new_var in variable_mappings.items():
        if pd.notna(old_var) and pd.notna(new_var):
            # Convert to string and strip whitespace
            old_var = str(old_var).strip()
            new_var = str(new_var).strip()
            
            if old_var and new_var:  # Check if non-empty after stripping
                # Create patterns for base variable and variables with _PT and _PROD suffixes
                base_pattern = r'(?<=[^\w])' + re.escape(old_var) + r'(?=[^\w])'
                pt_pattern = r'(?<=[^\w])' + re.escape(old_var + '_PT') + r'(?=[^\w])'
                soc_pattern = r'(?<=[^\w])' + re.escape(old_var + '_SOC') + r'(?=[^\w])'
                prod_pattern = r'(?<=[^\w])' + re.escape(old_var + '_PROD') + r'(?=[^\w])'
                Int_pattern = r'(?<=[^\w])' + re.escape(old_var + '_INT') + r'(?=[^\w])'
                
                # Replace base variable
                new_code = re.sub(base_pattern, new_var, modified_code)
                
                # Replace variables with _PT suffix
                if re.search(pt_pattern, modified_code):
                    new_code = re.sub(pt_pattern, new_var + '_PT', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_PT' → '{new_var}_PT'")
                
                # Replace variables with _PROD suffix
                if re.search(prod_pattern, modified_code):
                    new_code = re.sub(prod_pattern, new_var + '_PROD', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_PROD' → '{new_var}_PROD'")
                
                # Replace variables with _SOC suffix    
                if re.search(soc_pattern, modified_code):
                    new_code = re.sub(soc_pattern, new_var + '_SOC', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_SOC' → '{new_var}_SOC'")
                
                # Replace variables with _INT suffix
                if re.search(Int_pattern, modified_code):
                    new_code = re.sub(Int_pattern, new_var + '_INT', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_INT' → '{new_var}_INT'")
                
                # Check if any base variable replacements were made
                if new_code != modified_code:
                    replacements_made.append(f"'{old_var}' → '{new_var}'")
                    modified_code = new_code
    
    return modified_code, replacements_made

def create_zip_of_modified_files(modified_files):
    """Create a zip file containing all modified files."""
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        for filename, content in modified_files.items():
            zip_file.writestr(filename, content)
    return zip_buffer.getvalue()

def generate_config_combo(als_file, blind, lesion_metric, anchor_type, study_unit):
    """Generate config combo files from ALS file."""
    try:
        # Save the uploaded file temporarily
        with open("temp_als_file.xlsx", "wb") as f:
            f.write(als_file.getvalue())
        
        # Read the ALS file sheets with explicit engine
        als_field = pd.read_excel("temp_als_file.xlsx", sheet_name="Fields", engine='openpyxl')
        als_dataDic = pd.read_excel("temp_als_file.xlsx", sheet_name="DataDictionaryEntries", engine='openpyxl')
        als_form = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms", engine='openpyxl')
        als_project = pd.read_excel("temp_als_file.xlsx", sheet_name="CRFDraft", engine='openpyxl')
        als_folders = pd.read_excel("temp_als_file.xlsx", sheet_name="Folders", engine='openpyxl')

        # Clean up temporary file
        Path("temp_als_file.xlsx").unlink()

        # Get study name
        study_name = re.sub(r'[^a-zA-Z0-9_]', '_', f"{blind}_{als_project['ProjectName'].iloc[0].lower().replace('-', '_')}")

        # Process variable label table
        variable_label_tab = als_form[['OID', 'DraftFormName']].copy()
        variable_label_tab = variable_label_tab[variable_label_tab['OID'].notna()]
        variable_label_tab = variable_label_tab.merge(
            als_field[['FormOID', 'FieldOID', 'SASLabel', 'DataDictionaryName']],
            left_on='OID',
            right_on='FormOID'
        ).drop('FormOID', axis=1)
        variable_label_tab['OID'] = variable_label_tab['OID'].str.lower()

        # Process variable value table
        variable_value_tab = als_dataDic[['DataDictionaryName', 'UserDataString']].copy()
        variable_value_tab = variable_value_tab[variable_value_tab['DataDictionaryName'].notna()]

        # Map variable name, label and value
        var_label_value = variable_value_tab.merge(
            variable_label_tab,
            on='DataDictionaryName',
            how='outer'
        ).sort_values(['OID', 'FieldOID']).drop('DataDictionaryName', axis=1)

        # Store var_label_value in session state for variable replacement functionality
        st.session_state.var_label_value = var_label_value

        # Process DA_ALS
        da_als = var_label_value.copy()
        da_als['study_name'] = study_name
        da_als = da_als[da_als['DraftFormName'].str.contains('RECIST|Enrollment', case=False, na=False)].reset_index(drop=True)

        # Add r_name based on DraftFormName
        conditions = [
            da_als['DraftFormName'].str.contains('Time-point Response Assessment', case=False, na=False),
            da_als['DraftFormName'].str.contains('Non-Target Lesions', case=False, na=False),
            da_als['DraftFormName'].str.contains('Target Lesions', case=False, na=False),
            da_als['DraftFormName'].str.contains('New Lesions', case=False, na=False),
            da_als['DraftFormName'].str.contains('Enrollment', case=False, na=False)
        ]
        choices = ['rs_tb', 'ntl_tb', 'tl_tb', 'nl_tb', 'enr_tb']
        da_als['r_name'] = np.select(conditions, choices, default=None)
        da_als = da_als[da_als['r_name'].notna()].reset_index(drop=True)

        # Add r_col_name based on SASLabel
        conditions = [
            (da_als['SASLabel'].str.contains('Date', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('^Overall response', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('Evaluation of target lesions', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('Evaluation of non-target lesions', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('New lesion', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('Sum|Max', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Evaluated: Diameter measurement', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Diameter lesion', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Coalesced, lesion diameter', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Too small to measure', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Date of procedure', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Location', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Lesion status', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Target lesion number', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Lesion status', case=False, na=False)) & (da_als['r_name'] == 'ntl_tb'),
            (da_als['SASLabel'].str.contains('Date of procedure', case=False, na=False)) & (da_als['r_name'] == 'ntl_tb'),
            (da_als['SASLabel'].str.contains('Location', case=False, na=False)) & (da_als['r_name'] == 'ntl_tb'),
            (da_als['SASLabel'].str.contains('Were there new lesions to report', case=False, na=False)) & (da_als['r_name'] == 'nl_tb'),
            (da_als['SASLabel'].str.contains('Date of enrollment', case=False, na=False)) & (da_als['r_name'] == 'enr_tb')
        ]
        choices = [
            'rs_date', 'rs_overall_response', 'rs_tl_response', 'rs_ntl_response', 'rs_nl_response',
            'tl_diam_metric', 'tl_diam', 'tl_split_diam', 'tl_coalesce_diam', 'tl_too_small_diam',
            'tl_date', 'tl_location', 'tl_status', 'tl_id', 'ntl_response', 'ntl_date', 'ntl_location',
            'nl_yn', 'enr_date'
        ]
        da_als['r_col_name'] = np.select(conditions, choices, default=None)
        da_als = da_als[da_als['r_col_name'].notna()].reset_index(drop=True)

        # Add crf_col_type based on r_col_name
        da_als['crf_col_type'] = np.where(
            da_als['r_col_name'].str.contains('^tl(_.*_diam|_diam)|tl_id|tl_diam_metric', case=False, na=False),
            'numeric',
            np.where(
                da_als['SASLabel'].str.contains('Date', case=False, na=False),
                'date',
                'character'
            )
        )

        # Add r_col_val_name based on UserDataString
        conditions = [
            da_als['UserDataString'].str.contains('Absent', case=False, na=False),
            da_als['UserDataString'].str.contains('Coalesced', case=False, na=False),
            da_als['UserDataString'].str.contains('Complete', case=False, na=False),
            da_als['UserDataString'].str.contains('^Evaluated', case=False, na=False),
            da_als['UserDataString'].str.contains('No longer visible', case=False, na=False),
            da_als['UserDataString'].str.contains('Lymph', case=False, na=False),
            da_als['UserDataString'].str.contains('Not Applicable', case=False, na=False),
            da_als['UserDataString'].str.contains('Not.*Evalua|not done', case=False, na=False),
            (da_als['UserDataString'].str.contains('No', case=False, na=False)) & (da_als['r_col_name'].isin(['rs_nl_response', 'nl_yn'])),
            da_als['UserDataString'].str.contains('Yes', case=False, na=False),
            da_als['UserDataString'].str.contains('Partial', case=False, na=False),
            da_als['UserDataString'].str.contains('Stable', case=False, na=False),
            da_als['UserDataString'].str.contains('Non-CR', case=False, na=False),
            da_als['UserDataString'].str.contains('Unequivocal Progression', case=False, na=False),
            da_als['UserDataString'].str.contains('Progress', case=False, na=False),
            da_als['UserDataString'].str.contains('Present', case=False, na=False),
            da_als['UserDataString'].str.contains('Split', case=False, na=False),
            da_als['UserDataString'].str.contains('Unknown', case=False, na=False),
            da_als['UserDataString'].str.contains('Too small to measure', case=False, na=False)
        ]
        choices = [
            'Absent', 'cl', 'CR', 'el', 'invisible', 'lymph', 'NA', 'NE', 'No', 'Yes',
            'PR', 'SD', 'Non-CR/Non-PD', 'Unequivocal Progression', 'PD', 'Present',
            'sp', 'un', 'tstm'
        ]
        da_als['r_col_val_name'] = np.select(conditions, choices, default=None)

        # Add Flag_for_lymph and filter
        da_als['Flag_for_lymph'] = np.where(
            (da_als['SASLabel'].str.contains('Location', case=False, na=False)) & 
            (~da_als['UserDataString'].str.contains('Lymph', case=False, na=False)),
            1, 0
        )
        da_als = da_als[da_als['Flag_for_lymph'] != 1].reset_index(drop=True)

        # Rename columns
        da_als = da_als.rename(columns={
            'UserDataString': 'crf_col_val_name',
            'OID': 'crf_name',
            'FieldOID': 'crf_col_name',
            'SASLabel': 'crf_col_desc'
        })

        # Modify crf_col_name for date fields
        da_als['crf_col_name'] = np.where(
            (da_als['crf_col_desc'].str.contains('Date', case=False, na=False)) & 
            (da_als['r_name'] != 'subject_info'),
            da_als['crf_col_name'] + '_INT',
            da_als['crf_col_name']
        )

        # Select and arrange columns
        da_als = da_als[[
            'study_name', 'r_name', 'crf_name', 'r_col_name', 'crf_col_name',
            'crf_col_type', 'r_col_val_name', 'crf_col_val_name', 'crf_col_desc'
        ]].reset_index(drop=True)

        # Add system variables
        config_systemVar = ['subj_id', 'instance_id', 'instance_name']
        crf_col = ['Subject', 'instanceId', 'InstanceName']
        type_systemVar = ['character', 'numeric', 'character']
        col_des = [
            'Unique subject identifier',
            'Unique folder instance identifier',
            'Name of the folder instance'
        ]

        system_rows = []
        for r_name in da_als['r_name'].unique():
            for var, col, type_var, desc in zip(config_systemVar, crf_col, type_systemVar, col_des):
                system_rows.append({
                    'study_name': study_name,
                    'r_name': r_name,
                    'crf_name': da_als[da_als['r_name'] == r_name]['crf_name'].iloc[0],
                    'r_col_name': var,
                    'crf_col_name': col,
                    'crf_col_type': type_var,
                    'r_col_val_name': None,
                    'crf_col_val_name': None,
                    'crf_col_desc': desc
                })

        # Add subject info table
        active_treatment = re.split(r'-\d+$', als_project['ProjectName'].iloc[0])[0]
        subject_info_rows = pd.DataFrame({
            'study_name': study_name,
            'r_name': 'subject_info',
            'crf_name': 'SubjectInfo',
            'r_col_name': [
                'subj_id', 'first_dose_date', 'last_dose_date', 'eos_date',
                'eot_date', 'eot_reason', 'death_date', 'eos_reason', 'death_cause'
            ],
            'crf_col_name': [
                'Subject name or identifier', 'FirstDoseDate', 'LastDoseDate',
                'EOS Date', f'EOT Date -{active_treatment}',
                f'EOT Reason -{active_treatment}', 'Death Date', 'EOS Reason',
                'Death Cause'
            ],
            'crf_col_desc': [
                'Unique subject identifier', 'Calculated first dose date',
                'Calculated last dose date', 'End of Study Date',
                'End of Treatment Date', 'End of Treatment Reason',
                'Death date', 'End of Study Reason', 'cause of death'
            ],
            'crf_col_val_name': [None] * 9,
            'r_col_val_name': [None] * 9
        })
        subject_info_rows['crf_col_type'] = np.where(
            subject_info_rows['crf_col_name'].str.contains('Date', case=False, na=False),
            'date',
            'character'
        )

        # Combine all tables
        da_crf_config = pd.concat([
            da_als,
            pd.DataFrame(system_rows),
            subject_info_rows
        ], ignore_index=True)

        # Filter out enrollment table system variables
        da_crf_config = da_crf_config[
            ~((da_crf_config['r_name'] == 'enr_tb') & 
              (da_crf_config['r_col_name'].isin(['instance_id', 'instance_name'])))
        ].reset_index(drop=True)

        # Sort by r_name
        da_crf_config = da_crf_config.sort_values(by=['r_name', 'r_col_name']).reset_index(drop=True)

        # Process vs_crf_config
        vs_crf_config_ALS = var_label_value.copy()
        vs_crf_config_ALS = vs_crf_config_ALS[
            vs_crf_config_ALS['DraftFormName'].str.contains('Vital Sign.*|ECG|Weight', case=False, na=False)
        ]
        vs_crf_config_ALS = vs_crf_config_ALS[
            ~vs_crf_config_ALS['DraftFormName'].str.contains('NotinUse|Central', case=False, na=False)
        ]
        vs_crf_config_ALS['study_name'] = study_name

        # Add r_col_name for vs_crf_config
        conditions = [
            vs_crf_config_ALS['SASLabel'].str.contains('Were vital signs taken?', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Date', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Temperature', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('weight', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Systolic blood pressure', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Diastolic blood pressure', case=False, na=False),
            (vs_crf_config_ALS['SASLabel'].str.contains('Time point', case=False, na=False)) & 
            (vs_crf_config_ALS['DraftFormName'].str.contains('Vital Sign', case=False, na=False)),
            vs_crf_config_ALS['SASLabel'].str.contains('Was an ECG performed?', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('QTcF', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Was weight.* taken?', case=False, na=False)
        ]
        choices = [
            'VSYN', 'TESTDAT_INT', 'temp_stdvalue', 'weight_stdvalue',
            'SystolicBP_stdvalue', 'DiastolicBP_stdvalue', 'Timepoint',
            'EGPERF', 'stdvalue', 'WEYN'
        ]
        vs_crf_config_ALS['r_col_name'] = np.select(conditions, choices, default=None)
        vs_crf_config_ALS = vs_crf_config_ALS[vs_crf_config_ALS['r_col_name'].notna()]
        vs_crf_config_ALS = vs_crf_config_ALS[~vs_crf_config_ALS['DraftFormName'].str.contains('Weight', case=False, na=False)]
        vs_crf_config_ALS = vs_crf_config_ALS[~vs_crf_config_ALS['SASLabel'].str.contains('Date of weight', case=False, na=False)]
        vs_crf_config_ALS = vs_crf_config_ALS[~vs_crf_config_ALS['FieldOID'].str.contains('RSG', case=False, na=False)]
        vs_crf_config_ALS = vs_crf_config_ALS.drop('DraftFormName', axis=1)

        # Rename columns
        vs_crf_config_ALS = vs_crf_config_ALS.rename(columns={
            'OID': 'crf_name',
            'FieldOID': 'crf_col_name',
            'SASLabel': 'crf_col_desc',
            'UserDataString': 'crf_col_val_name'
        })

        # Update crf_col_val_name
        vs_crf_config_ALS['crf_col_val_name'] = np.where(
            vs_crf_config_ALS['crf_col_val_name'].notna() & 
            ~vs_crf_config_ALS['crf_col_val_name'].str.contains('^(Yes|No)', case=False, na=False),
            None,
            vs_crf_config_ALS['crf_col_val_name']
        )
        vs_crf_config_ALS = vs_crf_config_ALS.drop_duplicates()

        # Update crf_col_name based on conditions
        conditions = [
            vs_crf_config_ALS['crf_col_desc'].str.contains('Date', case=False, na=False),
            vs_crf_config_ALS['crf_col_desc'].str.contains('^(Was|Were)', case=False, na=False),
            vs_crf_config_ALS['crf_col_desc'].str.contains('Time Point', case=False, na=False),
            vs_crf_config_ALS['crf_col_desc'].str.contains('Temperature', case=False, na=False),
            vs_crf_config_ALS['crf_col_desc'].str.contains('Weight', case=False, na=False)
        ]
        suffixes = ['_INT', '_STD', '_STD', '_STD', '_STD']
        
        for condition, suffix in zip(conditions, suffixes):
            mask = condition
            vs_crf_config_ALS.loc[mask, 'crf_col_name'] = vs_crf_config_ALS.loc[mask, 'crf_col_name'] + suffix

        # Update r_col_val_name
        vs_crf_config_ALS['r_col_val_name'] = np.where(
            vs_crf_config_ALS['crf_col_val_name'].str.contains('^Yes', case=False, na=False),
            'Yes',
            np.where(
                vs_crf_config_ALS['crf_col_val_name'].str.contains('^No', case=False, na=False),
                'No',
                None
            )
        )

        # Add system variables
        config_systemVar_vs = ['Subject', 'instanceId', 'InstanceName', 'RecordDate', 'LastChangeDate', 'WhatChanged']
        col_des_vs = [
            'Unique subject identifier',
            'Unique folder instance identifier',
            'Name of the folder instance',
            'RecordDate',
            'LastChangeDate',
            'WhatChanged'
        ]

        system_rows = []
        for form in vs_crf_config_ALS['crf_name'].unique():
            for var, desc in zip(config_systemVar_vs, col_des_vs):
                system_rows.append({
                    'study_name': study_name,
                    'crf_name': form,
                    'r_col_name': var,
                    'crf_col_name': var,
                    'r_col_val_name': None,
                    'crf_col_val_name': None,
                    'crf_col_desc': desc
                })

        # Process forms with unit
        forms_with_unit = vs_crf_config_ALS[
            vs_crf_config_ALS['crf_name'].str.contains('^vs', case=False, na=False) &
            vs_crf_config_ALS['crf_col_desc'].str.contains('Temperature|Weight', case=False, na=False)
        ].copy()

        forms_with_unit['temp_unit'] = np.where(
            forms_with_unit['crf_col_desc'].str.contains('Temperature', case=False, na=False),
            forms_with_unit['crf_col_name'] + '_UN',
            None
        )
        forms_with_unit['weight_unit'] = np.where(
            forms_with_unit['crf_col_desc'].str.contains('Weight', case=False, na=False),
            forms_with_unit['crf_col_name'] + '_UN',
            None
        )

        forms_with_unit = forms_with_unit[['crf_name', 'temp_unit', 'weight_unit']]
        unit_rows = []
        
        for _, row in forms_with_unit.iterrows():
            if pd.notna(row['temp_unit']):
                unit_rows.append({
                    'study_name': study_name,
                    'crf_name': row['crf_name'],
                    'r_col_name': 'temp_unit',
                    'crf_col_name': row['temp_unit'],
                    'r_col_val_name': None,
                    'crf_col_val_name': None,
                    'crf_col_desc': 'Standard unit of temperature'
                })
            if pd.notna(row['weight_unit']):
                unit_rows.append({
                    'study_name': study_name,
                    'crf_name': row['crf_name'],
                    'r_col_name': 'weight_unit',
                    'crf_col_name': row['weight_unit'],
                    'r_col_val_name': None,
                    'crf_col_val_name': None,
                    'crf_col_desc': 'Standard unit of weight'
                })

        # Generate r_name for vs_config
        r_name_vs_gen = vs_crf_config_ALS[['crf_name']].drop_duplicates()
        r_name_vs_gen['category'] = np.where(r_name_vs_gen['crf_name'].str.contains('vs', case=False, na=False), 'vs', 'eg')
        r_name_vs_gen = r_name_vs_gen.sort_values('crf_name').reset_index(drop=True)
        r_name_vs_gen['index'] = r_name_vs_gen.groupby('category').cumcount() + 1
        r_name_vs_gen['r_name'] = r_name_vs_gen['category'] + r_name_vs_gen['index'].astype(str)

        # Combine all components
        vs_crf_config = pd.concat([
            pd.DataFrame(system_rows),
            vs_crf_config_ALS,
            pd.DataFrame(unit_rows)
        ], ignore_index=True)

        # Add crf_col_type
        vs_crf_config['crf_col_type'] = np.where(
            vs_crf_config['crf_col_desc'].str.contains('Date', case=False, na=False),
            'date',
            np.where(
                vs_crf_config['r_col_name'].str.contains('stdvalue|instanceId', case=False, na=False),
                'numeric',
                'character'
            )
        )

        # Sort and join with r_name
        vs_crf_config = vs_crf_config.sort_values(['crf_name', 'r_col_name']).reset_index(drop=True)
        vs_crf_config = vs_crf_config.merge(r_name_vs_gen[['crf_name', 'r_name']], on='crf_name', how='left')

        # Select final columns and filter
        vs_crf_config = vs_crf_config[[
            'study_name', 'r_name', 'crf_name', 'r_col_name', 'crf_col_name',
            'crf_col_type', 'r_col_val_name', 'crf_col_val_name', 'crf_col_desc'
        ]].drop_duplicates()

        vs_crf_config = vs_crf_config[
            vs_crf_config['r_col_name'].notna() &
            vs_crf_config['crf_col_name'].notna()
        ].reset_index(drop=True)

        # Process metadata extraction
        rs_folder_name = als_folders[
            als_folders['FolderName'].str.contains('Response Assessment', case=False, na=False)
        ]['FolderName'].tolist()

        # Extract unscheduled pattern
        unscheduled_patterns = [
            name for name in rs_folder_name 
            if re.search('unscheduled', name, re.IGNORECASE)
        ]
        unscheduled_grep = '|'.join(
            [re.search('(unscheduled.*?)(?=\\s|$)', name, re.IGNORECASE).group(1) 
             for name in unscheduled_patterns]
        ) if unscheduled_patterns else None

        # Extract EOT pattern
        eot_patterns = [
            name for name in rs_folder_name 
            if re.search('end of treatment|eot', name, re.IGNORECASE)
        ]
        if not eot_patterns:
            eot_patterns = [
                name for name in als_folders['FolderName'] 
                if re.search('end of treatment|eot', name, re.IGNORECASE)
            ]
        eot_grep = '|'.join(
            [re.search('(end of treatment|eot.*?)(?=\\s|$)', name, re.IGNORECASE).group(1) 
             for name in eot_patterns]
        ) if eot_patterns else None

        # Extract baseline visit pattern
        baseline_patterns = [
            name for name in als_folders['FolderName'] 
            if re.search('^screening', name, re.IGNORECASE)
        ]
        baseline_visit_name = '|'.join(
            [re.search('(^screening.*?)(?=\\s|$)', name, re.IGNORECASE).group(1) 
             for name in baseline_patterns]
        ) if baseline_patterns else None

        # Extract scheduled visit pattern
        def get_scheduled_pattern(folder_names):
            pattern = None
            if any(re.search(r'C\d+D\d+|cycle\d+day\d+|W\d+D\d+|week\d+day\d+', name, re.IGNORECASE) for name in folder_names):
                matches = []
                for name in folder_names:
                    match = re.search(r'(C\d+D\d+|cycle\d+day\d+|W\d+D\d+|week\d+day\d+)', name, re.IGNORECASE)
                    if match:
                        matches.append(match.group(1))
                pattern = '|'.join(set(matches))
            elif any(re.search(r'week(s)?|cycle', name, re.IGNORECASE) for name in folder_names):
                matches = []
                for name in folder_names:
                    match = re.search(r'(week(s)?|cycle)', name, re.IGNORECASE)
                    if match:
                        matches.append(match.group(1))
                pattern = '|'.join(set(matches))
            elif any(re.search(r'after', name, re.IGNORECASE) for name in folder_names):
                pattern = '(a|A)fter'
            return pattern

        scheduled_grep = get_scheduled_pattern(rs_folder_name)

        # Create da_meta_config
        da_meta_config = pd.DataFrame({
            'study_name': study_name,
            'config_name': [
                'lesion_metric', 'anchor_type', 'study_unit',
                'unscheduled_grep', 'eot_grep', 'scheduled_grep', 'baseline_visit_name'
            ],
            'config_value': [
                lesion_metric, anchor_type, study_unit,
                unscheduled_grep, eot_grep, scheduled_grep, baseline_visit_name
            ]
        })

        # Process CrfGlossary4Spotfire
        crf_glossary = var_label_value[['OID', 'DraftFormName']].copy()
        crf_glossary = crf_glossary[crf_glossary['OID'].notna()].reset_index(drop=True)
        crf_glossary.columns = ['AbbreviatedCRFName', 'CRFname']
        crf_glossary = crf_glossary[crf_glossary['AbbreviatedCRFName'].notna()].reset_index(drop=True)
        crf_glossary = crf_glossary.drop_duplicates().reset_index(drop=True)

        # Add additional rows to CrfGlossary4Spotfire
        additional_rows = pd.DataFrame([
            {'AbbreviatedCRFName': 'dose_merged', 'CRFname': 'Merged dosing forms'},
            {'AbbreviatedCRFName': 'subjectinfo', 'CRFname': 'Important info about subjects'},
            {'AbbreviatedCRFName': 'td_merged', 'CRFname': 'Merged EOT forms'},
            {'AbbreviatedCRFName': 'vs (merged)', 'CRFname': 'Merged vitals forms'},
            {'AbbreviatedCRFName': 'ecg (merged)', 'CRFname': 'Merged ecg forms'},
            {'AbbreviatedCRFName': 'lab', 'CRFname': 'Merged edc and central labs'},
            {'AbbreviatedCRFName': 'ae', 'CRFname': 'Adverse Event(Analysis)'}
        ])
        crf_glossary = pd.concat([crf_glossary, additional_rows], ignore_index=True)

        # Create config files
        wb = Workbook()
        
        # Add Variable mapping table
        ws = wb.active
        ws.title = "Variable mapping table"
        headers = ['UserDataString', 'OID', 'DraftFormName', 'FieldOID', 'SASLabel']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(var_label_value.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Add da_crf_config
        ws = wb.create_sheet("da_crf_config")
        headers = ['study_name', 'r_name', 'crf_name', 'r_col_name', 'crf_col_name', 'crf_col_type', 'r_col_val_name', 'crf_col_val_name', 'crf_col_desc']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(da_crf_config.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Add da_meta_config
        ws = wb.create_sheet("da_meta_config")
        headers = ['study_name', 'config_name', 'config_value']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(da_meta_config.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Add vs_crf_config
        ws = wb.create_sheet("vs_crf_config")
        headers = ['study_name', 'r_name', 'crf_name', 'r_col_name', 'crf_col_name', 'crf_col_type', 'r_col_val_name', 'crf_col_val_name', 'crf_col_desc']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(vs_crf_config.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Add CrfGlossary4Spotfire
        ws = wb.create_sheet("CrfGlossary4Spotfire")
        headers = ['AbbreviatedCRFName', 'CRFname']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(crf_glossary.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Format headers in all sheets
        for sheet in wb.sheetnames:
            ws = wb[sheet]
            for cell in ws[1]:
                cell.font = openpyxl.styles.Font(bold=True)
                cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = openpyxl.styles.Alignment(horizontal="center")
                cell.border = openpyxl.styles.Border(
                    left=openpyxl.styles.Side(style='thin'),
                    right=openpyxl.styles.Side(style='thin'),
                    top=openpyxl.styles.Side(style='thin'),
                    bottom=openpyxl.styles.Side(style='thin')
                )

        # Save the workbook
        output_path = f"config_combo_{study_name}.xlsx"
        wb.save(output_path)
        
        # Store study name in session state
        st.session_state.study_name = study_name
        
        return output_path

    except Exception as e:
        raise Exception(f"Error generating config combo: {str(e)}")

def generate_drug_merge_info(als_file):
    """Generate drug merge info from ALS file."""
    try:
        # Save the uploaded file temporarily
        with open("temp_als_file.xlsx", "wb") as f:
            f.write(als_file.getvalue())
        
        # Read ALS file sheets with explicit engine
        fields_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Fields", engine='openpyxl')
        forms_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms", engine='openpyxl')
        
        # Clean up temporary file
        Path("temp_als_file.xlsx").unlink()
        
        # Process fields
        fields_df = fields_df[['FormOID', 'FieldOID', 'SASLabel']].copy()
        fields_df['Form'] = fields_df['FormOID'].str.lower()
        fields_df = fields_df.drop('FormOID', axis=1).dropna()
        
        # Process forms
        forms_df = forms_df[['OID', 'DraftFormName']].copy()
        forms_df['Form'] = forms_df['OID'].str.lower()
        forms_df = forms_df.drop('OID', axis=1).dropna()
        
        # Join fields and forms
        var_label_form_drug = fields_df.merge(forms_df, on='Form')
        var_label_form_drug = var_label_form_drug[
            var_label_form_drug['DraftFormName'].str.contains('Study Drug Administration') &
            ~var_label_form_drug['FieldOID'].str.contains('RSG')
        ]
        
        # Required columns for dose_merged
        col_req = [
            "Subject name or identifier",
            "Dose Start Date (Interpolated)",
            "Dose End Date (Interpolated)",
            "Planned Dose",
            "Actual Dose",
            "Actual Frequency",
            "Planned Frequency",
            "Other acutual dose",
            "Other planned dose",
            "Other actual frequency",
            "Other planned frequency",
            "Cycle Number",
            "Was Study Drug administered?",
            "Reason for not administered",
            "Specify Other for Reason not administered",
            "Was Dose Modified?",
            "Reason Dose Modified",
            "Specify Other for Reason Dose Modified",
            "Was Dose Missed?",
            "Reason Dose Missed",
            "Specify Other for Reason Dose Missed",
            "Was Dose delayed since the last dose?",
            "Reason Dose delayed",
            "Specify Other for Reason Dose delayed",
            "Was Dose interrupted?",
            "Reason Dose interrupted",
            "Specify Other for Reason Dose interrupted",
            "Was Dose Discontinued?",
            "Reason Dose Discontinued",
            "Specify Other for Reason Dose Discontinued",
            "Was Dose Decreased?",
            "Reason Dose Decreased",
            "Specify Other for Reason Dose Decreased"
        ]
        
        # Create drug index
        drug_index = var_label_form_drug[['Form']].drop_duplicates()
        drug_index['index'] = range(1, len(drug_index) + 1)
        drug_index['drug_number'] = 'drug' + drug_index['index'].astype(str)
        drug_index['dose_number'] = 'dose' + drug_index['index'].astype(str)
        drug_index = drug_index.drop('index', axis=1)
        
        # Map fields to standardized labels
        drug_merged_info = drug_index.merge(var_label_form_drug, on='Form')
        drug_merged_info['Treatment'] = drug_merged_info['DraftFormName'].str.extract(r'.*-\s*(.*)')
        
        def map_label(row):
            sas_label = str(row['SASLabel']).lower()
            if 'start date' in sas_label:
                return "Dose Start Date (Interpolated)"
            elif any(x in sas_label for x in ['stop date', 'end date']):
                return "Dose End Date (Interpolated)"
            elif sas_label.startswith('actual dose'):
                return "Actual Dose"
            elif any(sas_label.startswith(x) for x in ['planned dose', 'prescribed dose']):
                return "Planned Dose"
            elif 'actual frequency' in sas_label:
                return "Actual Frequency"
            elif any(x in sas_label for x in ['planned frequency', 'prescribed frequency']):
                return "Planned Frequency"
            elif re.search(r'was .+ (administered|dosed)', sas_label):
                return "Was Study Drug administered?"
            elif re.search(r'reason for .* not (administered|dosed)', sas_label):
                return "Reason for not administered"
            elif 'other' in sas_label:
                return row['SASLabel']
            elif re.search(r'did subject report any dosing errors|was .+ missed', sas_label):
                return "Was Dose Missed?"
            elif re.search(r'reason for .* miss', sas_label):
                return "Reason Dose Missed"
            elif re.search(r'was .+ delayed', sas_label):
                return "Was Dose delayed since the last dose?"
            elif re.search(r'reason for .* delay', sas_label):
                return "Reason Dose delayed"
            elif re.search(r'was .+ interrupt', sas_label):
                return "Was Dose interrupted?"
            elif re.search(r'reason for interruption', sas_label):
                return "Reason Dose interrupted"
            elif re.search(r'has .+ been modified|was .+ modified', sas_label):
                return "Was Dose Modified?"
            elif re.search(r'reason for modification', sas_label):
                return "Reason Dose Modified"
            elif re.search(r'was .+ decrease', sas_label):
                return "Was Dose Decreased?"
            elif re.search(r'reason for .* decrease', sas_label):
                return "Reason Dose Decreased"
            elif re.search(r'was .+ discontinue', sas_label):
                return "Was Dose Discontinued?"
            elif re.search(r'reason for .* discontinue', sas_label):
                return "Reason Dose Discontinued"
            return None
        
        drug_merged_info['label_doseMerged'] = drug_merged_info.apply(map_label, axis=1)
        drug_merged_info = drug_merged_info.dropna(subset=['label_doseMerged'])
        drug_merged_info = drug_merged_info[~drug_merged_info['label_doseMerged'].str.contains('Unit', case=False)]
        
        # Handle "Other specify" labels
        drug_merged_info['pre_val'] = drug_merged_info['label_doseMerged'].shift(1)
        drug_merged_info['label_doseMerged'] = drug_merged_info.apply(
            lambda row: (
                f"Specify Other for {row['pre_val']}" if 'Other' in str(row['label_doseMerged']) and 'Reason' in str(row['pre_val'])
                else "Other acutual dose" if 'Other' in str(row['label_doseMerged']) and 'Actual Dose' in str(row['pre_val'])
                else "Other planned dose" if 'Other' in str(row['label_doseMerged']) and any(x in str(row['pre_val']) for x in ['Prescribed Dose', 'Planned Dose'])
                else "Other actual frequency" if 'Other' in str(row['label_doseMerged']) and 'Actual Frequency' in str(row['pre_val'])
                else "Other planned frequency" if 'Other' in str(row['label_doseMerged']) and 'Planned Frequency' in str(row['pre_val'])
                else row['label_doseMerged']
            ),
            axis=1
        )
        drug_merged_info = drug_merged_info.drop('pre_val', axis=1)
        drug_merged_info = drug_merged_info[~drug_merged_info['label_doseMerged'].str.contains('Other, specify', case=False)]
        
        # Create full table
        drug_full_tab = pd.DataFrame({
            'Form': np.repeat(drug_index['Form'], len(col_req)),
            'drug_number': np.repeat(drug_index['drug_number'], len(col_req)),
            'dose_number': np.repeat(drug_index['dose_number'], len(col_req)),
            'label_doseMerged': col_req * len(drug_index)
        })
        
        # Merge with drug_merged_info
        drug_full_tab = drug_full_tab.merge(
            drug_merged_info[['Form', 'FieldOID', 'label_doseMerged']],
            on=['Form', 'label_doseMerged'],
            how='left'
        )
        
        # Set default values
        drug_full_tab['FieldOID'] = drug_full_tab.apply(
            lambda row: (
                'Subject' if row['label_doseMerged'] == "Subject name or identifier"
                else 'Folder' if row['label_doseMerged'] == "Cycle Number"
                else row['FieldOID']
            ),
            axis=1
        )
        
        # Add Treatment
        drug_full_tab = drug_full_tab.merge(
            drug_merged_info[['Form', 'Treatment']].drop_duplicates(),
            on='Form',
            how='left'
        )
        
        # Set NA_character_ for missing values
        drug_full_tab['FieldOID'] = drug_full_tab.apply(
            lambda row: (
                row['Treatment'] if pd.isna(row['FieldOID']) and row['label_doseMerged'] == "Treatment"
                else "NA_character_" if pd.isna(row['FieldOID'])
                else row['FieldOID']
            ),
            axis=1
        )
        
        return drug_full_tab
        
    except Exception as e:
        st.error(f"Error generating drug merge info: {str(e)}")
        return None

def main():
    st.title("R Code Generator")
    
    # Initialize session state for ALS file if not exists
    if 'als_file' not in st.session_state:
        st.session_state.als_file = None
    
    # Add ALS file upload at the top level
    uploaded_als_file = st.file_uploader("Upload ALS Excel File", type=['xlsx'])
    if uploaded_als_file:
        st.session_state.als_file = uploaded_als_file
        st.success("ALS file uploaded successfully!")
    
    # Create tabs
    tab1, tab2, tab3, tab4 = st.tabs(["Config File Generator", "Variable Replacement", "Dose Merge Generator", "TD Merge Generator"])
    
    with tab1:
        st.title("Config File Generator")
        if st.session_state.als_file:
            # Generate drug merge info from ALS file
            drug_merge_info = generate_drug_merge_info(st.session_state.als_file)
            if drug_merge_info is not None:
                # Save drug merge info to a temporary file
                temp_file = "drug_merge_info.csv"
                drug_merge_info.to_csv(temp_file, index=False)
                st.session_state.drug_merge_info = drug_merge_info
                st.success("Drug merge info generated successfully!")
            
            # Get user inputs
            col1, col2 = st.columns(2)
            with col1:
                blind = st.selectbox("Blinded data?", ["Blinded", "Unblinded"])
                lesion_metric = st.selectbox("Lesion Metric", ["sum", "max"])
            with col2:
                anchor_type = st.selectbox("Anchor Type", ["first_dose", "rand"])
                study_unit = st.selectbox("Study Unit for lesion", ["mm", "cm"])
            
            if st.button("Generate Config Files", key="generate_config"):
                with st.spinner("Generating config files..."):
                    # Save the uploaded file temporarily
                    with open("temp_als_file.xlsx", "wb") as f:
                        f.write(st.session_state.als_file.getvalue())
                    
                    # Read the ALS file to get project name
                    als_project = pd.read_excel("temp_als_file.xlsx", sheet_name="CRFDraft", engine='openpyxl')
                    
                    # Generate study name
                    study_name = re.sub(r'[^a-zA-Z0-9_]', '_', f"{blind[0].lower()}_{als_project['ProjectName'].iloc[0].lower().replace('-', '_')}")
                    st.session_state.study_name = study_name
                    
                    # Clean up temporary file
                    Path("temp_als_file.xlsx").unlink()
                    
                    output_path = generate_config_combo(
                        st.session_state.als_file,
                        blind[0].lower(),  # Get first letter (b/u)
                        lesion_metric,
                        anchor_type,
                        study_unit
                    )
                    
                    # Read the generated file
                    with open(output_path, 'rb') as f:
                        file_data = f.read()
                    
                    # Offer download
                    st.download_button(
                        label="Download Generated Config Files",
                        data=file_data,
                        file_name=output_path,
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )
                    
                    # Clean up
                    Path(output_path).unlink()
                    
                    st.success("Config files generated successfully!")
    
    with tab2:
        st.title("Variable Replacement")
        if st.session_state.als_file:
            # Load config file first
            try:
                config_df = load_config_file()
                st.success("Configuration file loaded successfully")
                
                # Show config preview
                with st.expander("View Configuration"):
                    st.dataframe(config_df)
                
                # File uploader for R files
                r_files = st.file_uploader("Upload R Files", type=['R'], accept_multiple_files=True)
                
                if r_files:
                    # Find matching variables using the stored var_label_value
                    variable_mappings, replacements_info = find_matching_variables(st.session_state.var_label_value, config_df)
                    
                    if not variable_mappings:
                        st.warning("No matching variables found based on the patterns in the config file.")
                        return
                    
                    # Show matched variables
                    st.write("Matched Variables:")
                    matched_df = pd.DataFrame(replacements_info)
                    st.dataframe(matched_df)
                    
                    # Initialize session state for confirmed files if not exists
                    if 'confirmed_files' not in st.session_state:
                        st.session_state.confirmed_files = {}
                    if 'confirmation_status' not in st.session_state:
                        st.session_state.confirmation_status = {}
                    
                    # Process each R file
                    for r_file in r_files:
                        file_name = r_file.name
                        
                        # Create an expander for each file
                        with st.expander(f"Process {file_name}", expanded=True):
                            # Load and replace variables
                            original_code = load_r_file(r_file)
                            modified_code, replacements_made = replace_variables(original_code, variable_mappings)
                            
                            # Display modified content
                            st.text_area("Modified Code", modified_code, height=500, key=f"modified_{file_name}")
                            
                            # Show replacements made
                            if replacements_made:
                                st.write("Replacements made:")
                                for replacement in replacements_made:
                                    st.write(f"- {replacement}")
                            
                            # Add confirm button for each file
                            if st.button(f"Confirm Changes for {file_name}", key=f"confirm_{file_name}"):
                                confirm_changes(file_name, modified_code)
                                st.success(f"Changes confirmed for {file_name}")
                    
                    # Add helpful note about expander functionality
                    if r_files:
                        st.info("💡 Tip: You can collapse the sections you've reviewed by clicking on the expander headers.")
                    
                    # Add single download button for all confirmed files
                    if st.session_state.confirmed_files and len(st.session_state.confirmed_files) == len(r_files):
                        zip_data = create_zip_of_modified_files(st.session_state.confirmed_files)
                        st.download_button(
                            label="Download All Modified Files",
                            data=zip_data,
                            file_name="modified_files.zip",
                            mime="application/zip"
                        )
                    elif st.session_state.confirmed_files:
                        st.warning("Please confirm changes for all files to enable download.")
            
            except Exception as e:
                st.error(f"Failed to load configuration file: {str(e)}")
    
    with tab3:
        st.title("Dose Merge Generator")
        if st.session_state.als_file and 'drug_merge_info' in st.session_state:
            # Use the drug merge info directly from session state
            dose_merge_generator = DoseMergeGenerator(st.session_state.drug_merge_info)
            
            # Show configuration preview
            st.subheader("Configuration Preview")
            st.dataframe(dose_merge_generator.drug_info_df)
            
            # Get study ID from session state
            if 'study_name' in st.session_state:
                study_id = st.session_state.study_name
                
                if st.button("Generate Function", key="generate_dose_merge"):
                    try:
                        # Generate the function
                        generated_code = dose_merge_generator.generate_function(study_id)
                        
                        # Display the generated code in an editable text area
                        st.subheader("Generated R Function")
                        edited_code = st.text_area(
                            "Edit the generated R function if needed:",
                            value=generated_code,
                            height=500,
                            key="dose_merge_code"
                        )
                        
                        # Add download button for the edited code
                        st.download_button(
                            label="Download Generated Function",
                            data=edited_code,
                            file_name=f"doseMerge_{study_id}.R",
                            mime="text/plain"
                        )
                    except Exception as e:
                        st.error(f"Error generating function: {str(e)}")
            else:
                st.info("Please generate config files in the Config File Generator tab first to set the study ID.")
        else:
            st.info("Please upload an ALS file in the Config File Generator tab first.")
    
    with tab4:
        st.title("TD Merge Generator")
        if st.session_state.als_file and 'study_name' in st.session_state:
            # Generate td merge info from ALS file
            td_merge_generator = TDMergeGenerator(st.session_state.als_file)
            
            # Show configuration preview
            st.subheader("Configuration Preview")
            st.dataframe(td_merge_generator.td_info_df)
            
            # Get study ID from session state
            study_id = st.session_state.study_name
                
            if st.button("Generate Function", key="generate_td_merge"):
                try:
                    # Generate the function
                    generated_code = td_merge_generator.generate_function(study_id)
                    
                    # Display the generated code in an editable text area
                    st.subheader("Generated R Function")
                    edited_code = st.text_area(
                        "Edit the generated R function if needed:",
                        value=generated_code,
                        height=500,
                        key="td_merge_code"
                    )
                    
                    # Add download button for the edited code
                    st.download_button(
                        label="Download Generated Function",
                        data=edited_code,
                        file_name=f"tdMerge_{study_id}.R",
                        mime="text/plain"
                    )
                except Exception as e:
                    st.error(f"Error generating function: {str(e)}")
        else:
            st.info("Please upload an ALS file in the Config File Generator tab first.")

if __name__ == "__main__":
    main()