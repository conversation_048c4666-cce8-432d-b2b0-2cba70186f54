import os
import sys
import pandas as pd
from patient_profile_generator import PatientProfileGenerator

def create_mock_als_file(filename):
    """Create a mock ALS file for testing."""
    # Create Forms sheet
    forms_data = {
        'OID': ['AE', 'CM', 'PPROC', 'RS_R'],
        'DraftFormName': ['Adverse Events', 'Prior/Concomitant Medications', 'Prior/Concomitant Procedures/Surgeries', 'Time-point Response Assessment']
    }
    forms_df = pd.DataFrame(forms_data)

    # Create Fields sheet
    fields_data = {
        'FormOID': ['AE', 'AE', 'AE', 'AE', 'AE', 'AE', 'AE', 'AE', 'AE', 'AE',
                   'CM', 'CM', 'CM', 'CM',
                   'PPROC', 'PPROC',
                   'RS_R', 'RS_R'],
        'FieldOID': ['AESTDAT', 'AEENDAT', 'AETERM', 'AETOXGR', 'AEDEATH', 'AESER', 'AEOUT', 'AEDLT', 'AECI', 'AEIMM',
                    'CMSTDAT', 'CMENDAT', 'CMTRT', 'CMONGO',
                    'PRSTDAT', 'PRTRT',
                    'RSRESP', 'RSDAT'],
        'SASLabel': ['Start date', 'Stop date', 'Adverse event', 'Toxicity grade', 'Result in death', 'Was adverse event serious?', 'Outcome',
                    'Is this adverse event a dose limiting toxicity (DLT)?', 'Is this adverse event of clinical interest (AECI)?', 'Is this adverse event immune-related?',
                    'Start date', 'Stop date', 'Medication name', 'Ongoing',
                    'Date of procedure/surgery', 'Type or name of procedure/surgery',
                    'Overall response', 'Date of response']
    }
    fields_df = pd.DataFrame(fields_data)

    # Create DataDictionaryEntries sheet
    data_dic_data = {
        'DataDictionaryName': ['RSRESP', 'RSRESP', 'RSRESP', 'RSRESP'],
        'UserDataString': ['Complete response', 'Partial response', 'Stable disease', 'Progressive disease'],
        'CodedData': ['CR', 'PR', 'SD', 'PD']
    }
    data_dic_df = pd.DataFrame(data_dic_data)

    # Save to Excel file
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        forms_df.to_excel(writer, sheet_name='Forms', index=False)
        fields_df.to_excel(writer, sheet_name='Fields', index=False)
        data_dic_df.to_excel(writer, sheet_name='DataDictionaryEntries', index=False)

    return filename

def main():
    # Create a mock ALS file for testing
    mock_als_file = 'mock_als.xlsx'
    try:
        create_mock_als_file(mock_als_file)
        print(f"Created mock ALS file: {mock_als_file}")

        # Create a PatientProfileGenerator instance
        generator = PatientProfileGenerator(mock_als_file, tumor_type='Solid Tumor')
        print("PatientProfileGenerator instance created successfully")

        # Generate the R function for a specific study ID
        study_id = "b_bgb_16673_104"
        print(f"Generating R function for study ID: {study_id}")
        r_function = generator.generate_function(study_id)

        # Save the R function to a file
        output_file = f"patientProfile_{study_id}_generated.R"
        with open(output_file, "w") as f:
            f.write(r_function)

        print(f"R function generated and saved to '{output_file}'")

    except Exception as e:
        print(f"Error: {str(e)}")
    finally:
        # Clean up the mock file
        if os.path.exists(mock_als_file):
            os.remove(mock_als_file)
            print(f"Removed mock ALS file: {mock_als_file}")

if __name__ == "__main__":
    main()
