#' @title ae_3_SAEFlag_b_bgb_b3227_101test
#' @description Calculate a SAEFlag in the ae crf
#'
#' @param studyId This is a character field, studyId
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param ae This the ae dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return SAEFlag
#'
#' @export ae_3_SAEFlag_b_bgb_b3227_101test
#'
#' @importFrom dplyr case_when mutate select
#' @importFrom checkmate assert_data_frame
#' @importFrom log4r error info warn
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom magrittr %>%
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#'ae_3_SAEFlag_b_bgb_b3227_101test(studyId = studyIdVar, tempLogger = CentralLogger,
#'                            jobdatetime = jobdatetime, ae = ae, develop.f = develop.f,
#'                            vpath = vpath)
#' )
#' }
#'
#'
ae_3_SAEFlag_b_bgb_b3227_101test <- function(studyId, tempLogger, jobdatetime = jobdatetime, ae, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "ae_3_SAEFlag_b_bgb_b3227_101test"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))


      # Calculate SAEFlag ---------------------------------------------------------------------------------------------
      # UpdateVars ----
      SAEFlag <- ae %>%
        dplyr::select(.data$Subject,
                      .data$RecordId,
                      Serious = .data$AESER,
                      Death = .data$AESDTH,
                      LifeThreatening = .data$AESLIFE,
                      Hospitalization = .data$AESHOSP,
                      Disability = .data$AESDISAB,
                      BirthDefect = .data$AESCONG,
                      OtherEvent = .data$AESMIE) %>%
        dplyr::mutate(SAEFlag = dplyr::case_when(
          substr(.data$Serious, 1, 1) == "Y" &
            ( substr(.data$Death, 1, 1) != "Y" &
                substr(.data$LifeThreatening, 1, 1) != "Y" &
                substr(.data$Hospitalization, 1, 1) != "Y"  &
                substr(.data$Disability, 1, 1) != "Y" &
                substr(.data$BirthDefect, 1, 1)  != "Y" &
                substr(.data$OtherEvent, 1, 1)  != "Y" ) ~ "Missing SAE Condition",
          substr(.data$Serious, 1, 1) != "Y" &
            ( substr(.data$Death, 1, 1) == "Y" |
                substr(.data$LifeThreatening, 1, 1) == "Y" |
                substr(.data$Hospitalization, 1, 1) == "Y" |
                substr(.data$Disability, 1, 1) == "Y" |
                substr(.data$BirthDefect, 1, 1) == "Y" |
                substr(.data$OtherEvent, 1, 1)  == "Y" ) ~ "Potential SAE"

        )) %>%
        dplyr::select(
          .data$Subject,
          .data$RecordId,
          .data$SAEFlag
        )


      # Assign SAEFlag to calling envir ----------------------------------------------------
      assign("SAEFlag", SAEFlag, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," SAEFlag returned"))



      # End of aeCalc Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
