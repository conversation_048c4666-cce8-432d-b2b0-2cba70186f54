#' @title ae_5_AEWriteOut_b_bgb_b3227_101
#' @description Merge ae calculations into the original ae crf and write out to the analysis folder
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param ae This the ae dataframe
#' @param ae_to_lab_toxflag This is the ae_to_lab_toxflag dataframe
#' @param ae_to_vs_toxflag This is the ae_to_vs_toxflag dataframe
#' @param ae_CMList This is the ae_CMList dataframe
#' @param SAEFlag This is the SAEFlag dataframe
#' @param MHFlag This is the MHFlag dataframe
#' @param SubjectInfo This is the SubjectInfo dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return ae_calc
#' @return Treated
#'
#' @export ae_5_AEWriteOut_b_bgb_b3227_101
#'
#' @importFrom dplyr filter mutate select rowwise n_distinct distinct group_by summarise left_join rename
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r error info warn
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom magrittr %>%
#' @importFrom writexl write_xlsx
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#' ae_5_AEWriteOut_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
#'                                tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                                ae = ae, ae_to_lab_toxflag = ae_to_lab_toxflag,
#'                                ae_to_vs_toxflag = ae_to_vs_toxflag,
#'                                dose_merged = dose_merged, ae_CMList = ae_CMList,
#'                                SAEFlag = SAEFlag, MHFlag = MHFlag , SubjectInfo = SubjectInfo,
#'                                develop.f = develop.f, vpath = vpath)
#' }
#'
#'
ae_5_AEWriteOut_b_bgb_b3227_101 <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime,
                                           ae, ae_to_lab_toxflag, ae_to_vs_toxflag, ae_CMList, SAEFlag, MHFlag,
                                           SubjectInfo, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "ae_5_AEWriteOut_b_bgb_b3227_101"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))
      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))
      # Assert ae_to_lab_toxflag has min.rows and min.cols
      checkmate::assert_data_frame(ae_to_lab_toxflag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae_to_lab_toxflag has min.rows and min.cols."))
      # Assert ae_CMList has min.rows and min.cols
      checkmate::assert_data_frame(ae_CMList, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae_CMList has min.rows and min.cols."))
      # Assert SAEFlag has min.rows and min.cols
      checkmate::assert_data_frame(SAEFlag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm SAEFlag has min.rows and min.cols."))
      # Assert MHFlag has min.rows and min.cols
      checkmate::assert_data_frame(MHFlag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm MHFlag has min.rows and min.cols."))


      # ae_calc Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_calc dataframe"))

      #TreatedFlag & Total/Site Treated Calculations

      Treated <- SubjectInfo %>%
        dplyr::filter(!is.na(.data$FirstDoseDate)) %>%
        dplyr::select(.data$`Subject name or identifier`, .data$SiteNumber) %>%
        dplyr::mutate(TotalTreated = dplyr::n_distinct(.data$`Subject name or identifier`),
                      Treated = "Yes") %>%
        dplyr::distinct()


      SiteTreated <- Treated %>%
        dplyr::group_by(.data$SiteNumber) %>%
        dplyr::summarise(SiteTreated = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      #Create ae_calc dataframe
      ae_calc <- dplyr::left_join(ae, ae_to_lab_toxflag, by = c("Subject","RecordId"))  %>%
        dplyr::left_join(ae_CMList, by = c("RecordId")) %>%
        dplyr::left_join(ae_to_vs_toxflag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(SAEFlag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(MHFlag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(Treated %>% dplyr::select(-"SiteNumber"), by = c("Subject" = "Subject name or identifier")) %>%
        dplyr::left_join(SiteTreated, by = c("SiteNumber")) %>%
        #UpdateVars
        dplyr::mutate(`Total#Grade3-5Cases` = ae %>% dplyr::filter(grepl("[3-5]", .data$AETOXGR), !is.na(.data$AETERM)) %>%
                        nrow() ) %>%
        dplyr::rename(CMList = .data$CMList_Calc)

      #Add drug names for AE actions taken ----
      #UpdateVars
      attr(ae_calc$AEACNNAT, "label") <- "No action taken (Tislelizumab)"
      attr(ae_calc$AEACNDD, "label") <- "Drug withdrawn (Tislelizumab)"
      attr(ae_calc$AEACNDH, "label") <- "Drug interrupted (Tislelizumab)"
      attr(ae_calc$AEACNDDL, "label") <-  "Dose Delayed (Tislelizumab)"
      attr(ae_calc$AEACNID, "label") <-  "Infusion rate decreased (Tislelizumab)"
      attr(ae_calc$AEACNNA, "label") <- "Not Applicable (Tislelizumab)"

      attr(ae_calc$ACACNBGB, "label") <- "No action taken (A1217)"
      attr(ae_calc$AEAC2IRD, "label") <-  "Infusion rate decreased (A1217)"
      attr(ae_calc$ACTCPDR, "label") <- "Dose Reduced (A1217)"
      attr(ae_calc$ACTCPPD, "label") <- "Drug withdrawn (A1217)"
      attr(ae_calc$ACTCPDNC, "label") <- "Drug interrupted (A1217)"
      attr(ae_calc$ACTCPDD, "label") <- "Dose Delayed (A1217)"
      attr(ae_calc$ACTCPNA, "label") <- "Not Applicable (A1217)"



      attr(ae_calc$AEAC3NAT, "label") <- "No action taken (Palitaxel)"
      attr(ae_calc$AEAC3DR, "label") <- "Dose Reduced (Palitaxel)"
      attr(ae_calc$AEAC3DD, "label") <- "Drug withdrawn (Palitaxel)"
      attr(ae_calc$AEAC3DH, "label") <- "Drug interrupted (Palitaxel)"
      attr(ae_calc$AEAC3DDL, "label") <- "Dose Delayed (Palitaxel)"
      attr(ae_calc$AEAC3ID, "label") <-  "Infusion rate decreased (Palitaxel)"
      attr(ae_calc$AEAC3NA, "label") <- "Not Applicable (Palitaxel)"


      attr(ae_calc$AEAC4NAT, "label") <- "No action taken (Nab-paclitaxel)"
      attr(ae_calc$AEAC4DR, "label") <- "Dose Reduced (Nab-paclitaxel)"
      attr(ae_calc$AEAC4DD, "label") <- "Drug withdrawn (Nab-paclitaxel)"
      attr(ae_calc$AEAC4DH, "label") <- "Drug interrupted (Nab-paclitaxel)"
      attr(ae_calc$AEAC4DDL, "label") <- "Dose Delayed (Nab-paclitaxel)"
      attr(ae_calc$AEAC4ID, "label") <-  "Infusion rate decreased (Nab-paclitaxel)"
      attr(ae_calc$AEAC4NA, "label") <- "Not Applicable (Nab-paclitaxel)"



      attr(ae_calc$AEAC5NAT, "label") <- "No action taken (Pemetrexed)"
      attr(ae_calc$AEAC5DR, "label") <- "Dose Reduced (Pemetrexed)"
      attr(ae_calc$AEAC5DD, "label") <- "Drug withdrawn (Pemetrexed)"
      attr(ae_calc$AEAC5DH, "label") <- "Drug interrupted (Pemetrexed)"
      attr(ae_calc$AEAC5DDL, "label") <- "Dose Delayed (Pemetrexed)"
      attr(ae_calc$AEAC5ID, "label") <-  "Infusion rate decreased (Pemetrexed)"
      attr(ae_calc$AEAC5NA, "label") <- "Not Applicable (Pemetrexed)"


      attr(ae_calc$AEAC6NAT, "label") <- "No action taken (Carboplatin)"
      attr(ae_calc$AEAC6DR, "label") <- "Dose Reduced (Carboplatin)"
      attr(ae_calc$AEAC6DD, "label") <- "Drug withdrawn (Carboplatin)"
      attr(ae_calc$AEAC6DH, "label") <- "Drug interrupted (Carboplatin)"
      attr(ae_calc$AEAC6DDL, "label") <- "Dose Delayed (Carboplatin)"
      attr(ae_calc$AEAC6ID, "label") <-  "Infusion rate decreased (Carboplatin)"
      attr(ae_calc$AEAC6NA, "label") <- "Not Applicable (Carboplatin)"


      attr(ae_calc$AEAC7NAT, "label") <- "No action taken (Cisplatin)"
      attr(ae_calc$AEAC7DR, "label") <- "Dose Reduced (Cisplatin)"
      attr(ae_calc$AEAC7DD, "label") <- "Drug withdrawn (Cisplatin)"
      attr(ae_calc$AEAC7DH, "label") <- "Drug interrupted (Cisplatin)"
      attr(ae_calc$AEAC7DDL, "label") <- "Dose Delayed (Cisplatin)"
      attr(ae_calc$AEAC7ID, "label") <-  "Infusion rate decreased (Cisplatin)"
      attr(ae_calc$AEAC7NA, "label") <- "Not Applicable (Cisplatin)"


      attr(ae_calc$AEAC8NAT, "label") <- "No action taken (Etoposide)"
      attr(ae_calc$AEAC8DR, "label") <- "Dose Reduced (Etoposide)"
      attr(ae_calc$AEAC8DD, "label") <- "Drug withdrawn (Etoposide)"
      attr(ae_calc$AEAC8DH, "label") <- "Drug interrupted (Etoposide)"
      attr(ae_calc$AEAC8DDL, "label") <- "Dose Delayed (Etoposide)"
      attr(ae_calc$AEAC8ID, "label") <-  "Infusion rate decreased (Etoposide)"
      attr(ae_calc$AEAC8NA, "label") <- "Not Applicable (Etoposide)"

     # add analysis output to list before raiseLabels
      ae_list <- list("ae" = ae_calc)
      assign("ae_list", ae_list, envir = parent.frame())

     # raiseLabels on ae_calc Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","RaiseLabels on ae_calc dataframe"))
      ae_calc <- GSDSUtilities::raiseLabels(ae_calc, "label", isNullC = NA)

      # Assign ae_calc to calling envir ----------------------------------------------------
      assign("ae_calc", ae_calc, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," ae_calc returned"))

      # Assign Treated to calling envir ----------------------------------------------------
      assign("Treated", Treated , envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Treated returned"))

      # End of aeCalc Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
