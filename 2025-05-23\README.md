# MDR R Package Generator

A comprehensive Streamlit application that automates the generation of R functions for medical data review. This tool transforms ALS files into complete R packages with multiple specialized functions for medical data review(MDR).

## 🌟 Features

### **Automated R Function Generation**
- **12 Specialized Functions**: Automatically generates all R functions from a single ALS file
- **One-Click Generation**: Upload ALS file → Select tumor type → Generate complete package
- **Review & Confirm**: Review and edit each function before finalizing
- **Batch Download**: Download all confirmed functions and config files in one zip package

### **Supported Function Types**
1. **Dose Merge Generator** - Drug dosing data consolidation
2. **TD Merge Generator** - Treatment discontinuation data merging
3. **SubjectInfo Generator** - Patient demographic and baseline information
4. **PatientProfile Generator** - Comprehensive patient profiles
5. **AEWriteOut Generator** - Adverse events output formatting
6. **AE_CMlink Generator** - Adverse events and concomitant medications linking
7. **SAE Flag Generator** - Serious adverse events flagging
8. **MH Flag Generator** - Medical history flagging
9. **Vital CTCAE Generator** - Vital signs and CTCAE grading
10. **aeTox Flag Generator** - Toxicity flagging for adverse events
11. **labCalc Generator** - Laboratory calculations and transformations
12. **Controller Generator** - Main controller function for data processing

### **Enhanced User Interface**
- **Modern Design**: Professional gradient backgrounds with glass-morphism effects
- **Responsive Layout**: Optimized for desktop, laptop, and tablet use
- **Interactive Elements**: Smooth animations and hover effects
- **Status Tracking**: Visual indicators for completion status
- **Custom Typography**: Google Fonts integration for better readability

## 🚀 Quick Start

### **Prerequisites**
```bash
pip install -r requirements.txt
```

### **Launch Application**
```bash
streamlit run app.py
```

## 📋 Usage Guide

### **Step 1: Setup (Cover Page)**
1. **Upload ALS File**: Upload your Excel ALS file (`.xlsx` format)
2. **Select Tumor Type**: Choose between "Solid Tumor" or "Heme"
3. **Verify Settings**: Check the status cards to ensure both are configured

### **Step 2: Generate Config Files**
1. Navigate to **Config File Generator**
2. Configure study parameters:
   - **Blinded/Unblinded** data selection
   - **Lesion Metric** (sum/max)
   - **Anchor Type** (first_dose/rand)
   - **Study Unit** (mm/cm)
3. Click **Generate Config Files**
4. All R functions are automatically generated

### **Step 3: Review & Confirm Functions**
1. Navigate through individual function tabs
2. Review generated R code in the text editor
3. Make any necessary modifications
4. Click **✅ Confirm Function** for each function you want to include

### **Step 4: Download Complete Package**
1. Use the **📦 Download All Files** section in the sidebar
2. View the list of confirmed files
3. Click **🗂️ Download All Generated Files**
4. Receive a complete zip package with all files

## 🎯 Tumor Type Support

### **Solid Tumor Studies**
- Enhanced patient profile features
- Lesion measurement calculations
- Disease assessment functions
- Response evaluation criteria

### **Hematological Studies**
- Specialized laboratory calculations
- Heme-specific toxicity flagging
- Blood count transformations
- Hematological response criteria

## ⚠️ Important Notes

### **SubjectInfo Generator**
> **Note**: Please check onTreatment and MaxActualDose calculation. If the treatment used is not the primary treatment, please update to the primary treatment.

### **Data Requirements**
- ALS files must be in Excel format (`.xlsx`)
- Files should follow standard ALS structure with required sheets
- Variable mappings are automatically extracted from the ALS file

## 🔧 Technical Architecture

### **Core Components**
- **Frontend**: Streamlit with custom CSS styling
- **Backend**: Python-based R code generation
- **Data Processing**: Pandas for Excel file handling
- **Template System**: R template files for function generation
- **Configuration**: Dynamic config file generation

### **File Structure**
```
├── app.py                          # Main Streamlit application
├── requirements.txt                # Python dependencies
├── generators/                    # Python generator classes
│   ├── dose_merge_generator.py
│   ├── subject_info_generator.py
│   ├── patient_profile_generator.py
│   └── ...
└── README.md                      # This file
```

## 🎨 UI/UX Features

### **Visual Design**
- **Gradient Backgrounds**: Purple-blue gradient theme
- **Glass-morphism**: Semi-transparent content areas
- **Custom Fonts**: Inter and JetBrains Mono fonts
- **Responsive Design**: Optimized for various screen sizes

### **Interactive Elements**
- **Hover Effects**: Button animations and transitions
- **Status Badges**: Color-coded confirmation indicators
- **Progress Tracking**: Visual feedback for generation process
- **File Management**: Organized download system

### **Accessibility**
- **High Contrast**: Readable color combinations
- **Clear Typography**: Optimized font sizes and weights
- **Visual Hierarchy**: Structured layout with proper spacing
- **User Feedback**: Comprehensive status messages

## 🔄 Workflow Integration

### **Clinical Trial Workflow**
1. **Study Setup** → Upload ALS file
2. **Configuration** → Set study parameters
3. **Generation** → Automatic R function creation
4. **Review** → Quality check and customization
5. **Deployment** → Download complete package

### **Quality Assurance**
- **Template Validation**: Ensures R code syntax correctness
- **Variable Mapping**: Automatic extraction and validation
- **Error Handling**: Comprehensive error reporting
- **Code Review**: Manual review capability before confirmation

## 📊 Output Files

### **Generated Package Contents**
- **Config File**: `config_combo_{study_name}.xlsx`
- **R Functions**: Individual `.R` files for each function
- **Complete Package**: `{study_name}_complete_package.zip`

### **File Naming Convention**
- Study-specific naming with consistent prefixes
- Blinded/Unblinded indicators
- Function type identification
- Version control friendly naming

## 🛠️ Development

### **Adding New Functions**
1. Create generator class in `generators/` directory
2. Add R template in `simple_R_package/`
3. Update `generate_all_functions()` in `app.py`
4. Add navigation tab and confirmation UI

### **Customizing UI**
- Modify CSS in `apply_custom_css()` function
- Update color schemes and styling
- Add new interactive elements
- Enhance responsive design

## 📞 Support

For technical support or feature requests, please refer to the development team or create an issue in the project repository.

---

**MDR R Package Generator** - Streamlining clinical trial data analysis through automated R function generation.