library(readxl)
library(dplyr)
library(magrittr)
library(stringr)
library(tidyr)

ALS_field<-read_excel("BGB-B3227-101_V1.0_05Aug2024_1028.xlsx",sheet = "Fields") %>% 
  dplyr::select(.data$FormOID,.data$FieldOID,.data$SASLabel) %>% 
  dplyr::mutate(Form = tolower(.data$FormOID)) %>% 
  dplyr::select(-.data$FormOID) %>% 
  tidyr::drop_na()

ALS_form<- read_excel("BGB-B3227-101_V1.0_05Aug2024_1028.xlsx",sheet = "Forms") %>% 
  dplyr::select(.data$OID,.data$DraftFormName) %>% 
  dplyr::mutate(Form =tolower(.data$OID)) %>% 
  dplyr::select(-.data$OID) %>% 
  tidyr::drop_na()


var_label_form_drug <- ALS_field %>% 
  dplyr::left_join(ALS_form, by = "Form") %>% 
  dplyr::filter(grepl("Study Drug Administration",.data$DraftFormName)) %>% 
  dplyr::filter(!grepl("RSG",.data$FieldOID)) 

# columns required in dose_merged

col_req<-c(
  "Subject name or identifier",
  "Dose Start Date (Interpolated)",
  "Dose End Date (Interpolated)",
  "Planned Dose",
  "Actual Dose",
  "Actual Frequency",
  "Planned Frequency",
  "Other acutual dose",
  "Other planned dose",
  "Other actual frequency",
  "Other planned frequency",
  "Cycle Number",
  "Was Study Drug administered?",
  "Reason for not administered",
  "Specify Other for Reason not administered",
  "Was Dose Modified?",
  "Reason Dose Modified",
  "Specify Other for Reason Dose Modified",
  "Was Dose Missed?",
  "Reason Dose Missed",
  "Specify Other for Reason Dose Missed",
  "Was Dose delayed since the last dose?",
  "Reason Dose delayed",
  "Specify Other for Reason Dose delayed",
  "Was Dose interrupted?",
  "Reason Dose interrupted",
  "Specify Other for Reason Dose interrupted",
  "Was Dose Discontinued?",
  "Reason Dose Discontinued",
  "Specify Other for Reason Dose Discontinued"
)



drug_index<-var_label_form_drug %>% 
  dplyr::select(.data$Form) %>% 
  dplyr::distinct() %>% 
  dplyr::mutate(index = row_number(.data$Form)) %>% 
  dplyr::mutate(drug_number = paste0("drug",index)) %>% 
  dplyr::mutate(dose_number = paste0("dose",index)) %>% 
  dplyr::select(-.data$index)



drug_merged_info<-drug_index %>% 
  dplyr::left_join(var_label_form_drug,by = "Form") %>% 
  dplyr::mutate(Treatment = sub(".*- \\s*(.*)", "\\1", .data$DraftFormName)) %>% 
  dplyr::mutate(label_doseMerged = case_when(
    grepl("Start date",.data$SASLabel, ignore.case = T) ~ "Dose Start Date (Interpolated)",
    
    grepl("Stop date|End date", .data$SASLabel, ignore.case = T) ~"Dose End Date (Interpolated)",
    
    grepl("unit", .data$SASLabel, ignore.case = T) ~ .data$SASLabel,
    
    grepl("^Actual Dose",.data$SASLabel, ignore.case = T) ~"Actual Dose",
    
    grepl("^Planned dose|^Prescribed dose",.data$SASLabel, ignore.case =T) ~"Planned Dose",
    
    grepl("Actual frequency" ,.data$SASLabel, ignore.case = T) ~"Actual Frequency",
    
    grepl("(Planned|Prescribed) frequency" ,.data$SASLabel, ignore.case = T) ~"Planned Frequency",
    
    grepl("Was .+ (administered|dosed)",.data$SASLabel, ignore.case = T) ~"Was Study Drug administered?",
    
    grepl("Reason for .* not (administered|dosed)", .data$SASLabel, ignore.case= T) ~"Reason for not administered",
    
    grepl("Other",.data$SASLabel, ignore.case=T) ~ .data$SASLabel,
    
    grepl("Did subject report any dosing errors|was .+ missed",.data$SASLabel, ignore.case = T) ~"Was Dose Missed?",
    
    grepl("Reason for .* miss",.data$SASLabel, ignore.case=T) ~"Reason Dose Missed",
    
    grepl("Was .+ delayed", .data$SASLabel, ignore.case = T) ~"Was Dose delayed since the last dose?",
    
    grepl("Reason for .* delay" ,.data$SASLabel, ignore.case = T) ~"Reason Dose delayed",
    
    grepl("Was .+ interrupt" ,.data$SASLabel ,ignore.case =T) ~"Was Dose interrupted?",
    
    grepl("reason for interruption",.data$SASLabel, ignore.case =T) ~"Reason Dose interrupted",
    
    grepl("Has .+ been modified|Was .+ modified",.data$SASLabel, ignore.case =T) ~"Was Dose Modified?",
    
    grepl("reason for modification",.data$SASLabel, ignore.case = T) ~"Reason Dose Modified",
    
    grepl("was .+ decrease", .data$SASLabel, ignore.case = T) ~"Was Dose Decreased?",
    
    grepl("reason for .* decrease", .data$SASLabel, ignore.case = T) ~"Reason Dose Decreased",
    
    grepl("was .+ discontinue", .data$SASLabel, ignore.case = T) ~"Was Dose Discontinued?",
    
    grepl("reason for .* discontinue", .data$SASLabel, ignore.case =T) ~"Reason Dose Discontinued",
    
    TRUE ~NA_character_
    
  )) %>% 
  dplyr::filter(!is.na(.data$label_doseMerged)&!grepl("Unit",.data$label_doseMerged,ignore.case = T)) %>% 
  # deal with Other specify label, get previous value
  dplyr::mutate(pre_val = dplyr::lag(.data$label_doseMerged,n=1)) %>% 
  dplyr::mutate(label_doseMerged = case_when(grepl("Other",.data$label_doseMerged)&grepl("Reason",.data$pre_val)~ paste0("Specify Other for ",.data$pre_val),
                                             
                                             grepl("Other", .data$label_doseMerged)&grepl("Actual Dose",.data$pre_val) ~ "Other acutual dose",
                                             
                                             grepl("Other", .data$label_doseMerged)&grepl("Prescribed Dose|Planned Dose",.data$pre_val) ~ "Other planned dose",
                                             
                                             grepl("Other", .data$label_doseMerged)&grepl("Actual Frequency",.data$pre_val) ~ "Other actual frequency",
                                             
                                             grepl("other", .data$label_doseMerged)&grepl("Planned Frequency",.data$pre_val) ~"Other planned frequency",
                                             
                                             TRUE ~ .data$label_doseMerged)) %>% 
  dplyr::select(-.data$pre_val) %>% 
  dplyr::filter(!grepl("Other, specify",.data$label_doseMerged,ignore.case = T))


drug_full_tab<- data.frame(
  "Form" = rep(drug_index$Form, each = length(col_req)),
  "drug_number" = rep(drug_index$drug_number,each = length(col_req)),
  "dose_number" = rep(drug_index$dose_number, each = length(col_req)),
  "label_doseMerged" = rep(col_req, times= nrow(drug_index))
) %>% 
  dplyr::left_join(drug_merged_info %>% dplyr::select(.data$Form,.data$FieldOID,.data$label_doseMerged), by =c("Form", "label_doseMerged")) %>% 
  
  dplyr::mutate(FieldOID = case_when(
    
    .data$label_doseMerged == "Subject name or identifier" ~"Subejct",
    
    .data$label_doseMerged =="Cycle Number" ~"Folder",
  
    
    TRUE ~ .data$FieldOID)) %>% 
  dplyr::left_join(drug_merged_info %>% select(.data$Form, .data$Treatment) %>% distinct(), by = "Form") %>% 
  
  dplyr::mutate(FieldOID = case_when(is.na(.data$FieldOID)&.data$label_doseMerged =="Treatment" ~.data$Treatment, 
                                     
                                     is.na(.data$FieldOID) ~"NA_character_",
                                     
                                     TRUE ~ .data$FieldOID)) 














