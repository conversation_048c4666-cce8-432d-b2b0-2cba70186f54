@echo off
echo ========================================
echo  MDR R Package Generator - Development
echo ========================================
echo.

cd /d "%~dp0.."

echo Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org/
    pause
    exit /b 1
)

echo Checking if node_modules exists...
if not exist "node_modules" (
    echo Installing Node.js dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install Node.js dependencies
        pause
        exit /b 1
    )
)

echo Checking if python-env exists...
if not exist "python-env" (
    echo Setting up development environment...
    npm run setup-dev
    if errorlevel 1 (
        echo ERROR: Failed to setup development environment
        pause
        exit /b 1
    )
)

echo.
echo Starting MDR R Package Generator in development mode...
echo.
echo The application will open in a new window.
echo Close this window to stop the application.
echo.

npm run dev

echo.
echo Application stopped.
pause
