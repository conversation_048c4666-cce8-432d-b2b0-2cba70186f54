#' @title ae_5_AEWriteOut_b_bgb_b3227_101
#' @description Merge ae calculations into the original ae crf and write out to the analysis folder
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param ae This the ae dataframe
#' @param ae_to_lab_toxflag This is the ae_to_lab_toxflag dataframe
#' @param ae_to_vs_toxflag This is the ae_to_vs_toxflag dataframe
#' @param ae_CMList This is the ae_CMList dataframe
#' @param SAEFlag This is the SAEFlag dataframe
#' @param MHFlag This is the MHFlag dataframe
#' @param SubjectInfo This is the SubjectInfo dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return ae_calc
#' @return Treated
#'
#' @export ae_5_AEWriteOut_b_bgb_b3227_101
#'
#' @importFrom dplyr filter mutate select rowwise n_distinct distinct group_by summarise left_join rename
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r error info warn
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom magrittr %>%
#' @importFrom writexl write_xlsx
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#' ae_5_AEWriteOut_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
#'                                tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                                ae = ae, ae_to_lab_toxflag = ae_to_lab_toxflag,
#'                                ae_to_vs_toxflag = ae_to_vs_toxflag,
#'                                dose_merged = dose_merged, ae_CMList = ae_CMList,
#'                                SAEFlag = SAEFlag, MHFlag = MHFlag , SubjectInfo = SubjectInfo,
#'                                develop.f = develop.f, vpath = vpath)
#' }
#'
#'
ae_5_AEWriteOut_b_bgb_b3227_101 <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime,
                                           ae, ae_to_lab_toxflag, ae_to_vs_toxflag, ae_CMList, SAEFlag, MHFlag,
                                           SubjectInfo, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "ae_5_AEWriteOut_b_bgb_b3227_101"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))
      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))
      # Assert ae_to_lab_toxflag has min.rows and min.cols
      checkmate::assert_data_frame(ae_to_lab_toxflag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae_to_lab_toxflag has min.rows and min.cols."))
      # Assert ae_CMList has min.rows and min.cols
      checkmate::assert_data_frame(ae_CMList, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae_CMList has min.rows and min.cols."))
      # Assert SAEFlag has min.rows and min.cols
      checkmate::assert_data_frame(SAEFlag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm SAEFlag has min.rows and min.cols."))
      # Assert MHFlag has min.rows and min.cols
      checkmate::assert_data_frame(MHFlag, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm MHFlag has min.rows and min.cols."))


      # ae_calc Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_calc dataframe"))

      #TreatedFlag & Total/Site Treated Calculations

      Treated <- SubjectInfo %>%
        dplyr::filter(!is.na(.data$FirstDoseDate)) %>%
        dplyr::select(.data$`Subject name or identifier`, .data$SiteNumber) %>%
        dplyr::mutate(TotalTreated = dplyr::n_distinct(.data$`Subject name or identifier`),
                      Treated = "Yes") %>%
        dplyr::distinct()


      SiteTreated <- Treated %>%
        dplyr::group_by(.data$SiteNumber) %>%
        dplyr::summarise(SiteTreated = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()


      # Merge in CCQ list and create a CCQ Flag and add imAE Category
      CCQ_lst <- readxl::read_xlsx("/usrfiles/spotfire/IntegratedSafety/Dictionary/CCQ MedDRA27.0.xlsx", sheet = "CCQ v27.0") %>%
        dplyr::filter(.data$Type=="imAE") %>%
        dplyr::select(.data$`CCQ Category`, AEPT = tidyselect::contains("PT"), .data$Scope) %>%
        dplyr::distinct() %>%
        dplyr::mutate(CCQ_flag = "Y")

      #Create ae_calc dataframe
      ae_calc <- dplyr::left_join(ae, ae_to_lab_toxflag, by = c("Subject","RecordId"))  %>%
        dplyr::left_join(ae_CMList, by = c("RecordId")) %>%
        dplyr::left_join(ae_to_vs_toxflag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(SAEFlag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(MHFlag, by = c("Subject","RecordId")) %>%
        dplyr::left_join(Treated %>% dplyr::select(-"SiteNumber"), by = c("Subject" = "Subject name or identifier")) %>%
        dplyr::left_join(SiteTreated, by = c("SiteNumber")) %>%
        #UpdateVars
        dplyr::mutate(`Total#Grade3-5Cases` = ae %>% dplyr::filter(grepl("[3-5]", .data$AETOXGR1), !is.na(.data$AETERM1)) %>%
                        nrow() ) %>%
        dplyr::rename(CMList = .data$CMList_Calc)

      ae_calc <- dplyr::left_join(ae_calc,CCQ_lst, by = c("AETERM1_PT" = "AEPT")) %>%
        dplyr::mutate(CCQ_flag = dplyr::if_else(is.na(.data$CCQ_flag), "N", .data$CCQ_flag))

      #Add drug names for AE actions taken ----
      #UpdateVars
      attr(ae_calc$AEACN_NO1, "label") <- "Dose not changed (BGB-B3227)"
      attr(ae_calc$AEACN_DR1, "label") <- "Dose reduced (BGB-B3227)"
      attr(ae_calc$AEACN_DW1, "label") <- "Drug withdrawn (BGB-B3227)"
      attr(ae_calc$AEACN_DI1, "label") <-  "Drug interrupted (BGB-B3227)"
      attr(ae_calc$AEACN_RD1, "label") <-  "Dose rate reduced (BGB-B3227)"
      attr(ae_calc$AEACN_NA1, "label") <- "Not applicable (BGB-B3227)"

      attr(ae_calc$AEACN_NO2, "label") <- "Dose not changed (Tislelizumab)"
      attr(ae_calc$AEACN_DW2, "label") <-  "Drug withdrawn (Tislelizumab)"
      attr(ae_calc$AEACN_DI2, "label") <- "Drug interrupted (Tislelizumab)"
      attr(ae_calc$AEACN_RD2, "label") <- "Dose rate reduced (Tislelizumab)"
      attr(ae_calc$AEACN_NA2, "label") <- "Not applicable (Tislelizumab)"


      attr(ae_calc$AEACN_NO3, "label") <- "Dose not changed (Oxaliplatin)"
      attr(ae_calc$AEACN_DR3, "label") <- "Dose reduced (Oxaliplatin)"
      attr(ae_calc$AEACN_DW3, "label") <- "Drug withdrawn (Oxaliplatin)"
      attr(ae_calc$AEACN_DI3, "label") <- "Drug interrupted (Oxaliplatin)"
      attr(ae_calc$AEACN_RD3, "label") <- "Dose rate reduced (Oxaliplatin)"
      attr(ae_calc$AEACN_NA3, "label") <-  "Not applicable (Oxaliplatin)"


      attr(ae_calc$AEACN_NO4, "label") <- "Dose not changed (Capecitabine)"
      attr(ae_calc$AEACN_DR4, "label") <- "Dose reduced (Capecitabine)"
      attr(ae_calc$AEACN_DW4, "label") <- "Drug withdrawn (Capecitabine)"
      attr(ae_calc$AEACN_DI4, "label") <- "Drug interrupted (Capecitabine)"
      attr(ae_calc$AEACN_NA4, "label") <- "Not applicable (Capecitabine)"



     # add analysis output to list before raiseLabels
      ae_list <- list("ae" = ae_calc)
      assign("ae_list", ae_list, envir = parent.frame())

     # raiseLabels on ae_calc Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","RaiseLabels on ae_calc dataframe"))
      ae_calc <- GSDSUtilities::raiseLabels(ae_calc, "label", isNullC = NA)

      # Assign ae_calc to calling envir ----------------------------------------------------
      assign("ae_calc", ae_calc, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," ae_calc returned"))

      # Assign Treated to calling envir ----------------------------------------------------
      assign("Treated", Treated , envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Treated returned"))

      # End of aeCalc Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
