import pandas as pd
import re
from pathlib import Path
import io

class ControllerGenerator:
    def __init__(self, als_file, study_id=None, tumor_type=None):
        """Initialize the ControllerGenerator with ALS file, optional study_id, and tumor_type."""
        self.als_file = als_file
        self.provided_study_id = study_id
        self.tumor_type = tumor_type
        self.study_info = self._extract_study_info()
        self.table_mappings = self._generate_table_mappings()
        self.patient_profile_table_mappings = self._generate_patient_profile_table_mappings()
        self.diameter_lesion_fields = self._extract_diameter_lesion_fields()

    def _extract_study_info(self):
        """Extract study information from ALS file."""
        try:
            # Save the uploaded file temporarily
            with open("temp_als_file.xlsx", "wb") as f:
                f.write(self.als_file.getvalue())

            # Read the ALS file sheets
            als_project = pd.read_excel("temp_als_file.xlsx", sheet_name="CRFDraft", engine='openpyxl')
            als_form = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms", engine='openpyxl')
            als_form = als_form[~als_form['DraftFormName'].str.contains('_INACTIVE', na=False)]

            # Clean up temporary file
            Path("temp_als_file.xlsx").unlink()

            # Extract study name and ID
            project_name = als_project['ProjectName'].iloc[0] if 'ProjectName' in als_project.columns else "unknown_project"

            # Use provided study_id if available, otherwise extract from ALS file
            if self.provided_study_id:
                full_study_id = self.provided_study_id
            else:
                study_id = re.sub(r'[^a-zA-Z0-9_]', '_', project_name.lower().replace('-', '_'))
                # Get blind prefix (b_ or u_)
                blind_prefix = "b_"  # Default to blinded
                # Combine to get full study ID
                full_study_id = f"{blind_prefix}{study_id}"

            # Extract forms information
            forms = als_form['DraftFormName'].tolist()
            form_oids = als_form['OID'].tolist() if 'OID' in als_form.columns else []

            # Create a mapping of form names to their OIDs
            form_mapping = {}
            for i in range(len(forms)):
                if i < len(form_oids) and isinstance(forms[i], str):
                    form_mapping[forms[i]] = form_oids[i].lower() if isinstance(form_oids[i], str) else ""

            # Extract treatment forms
            treatment_forms = [form for form in forms if isinstance(form, str) and 'Study Drug Administration' in form]
            treatments = []
            for form in treatment_forms:
                if '-' in form:
                    treatment = form.split('-')[-1].strip()
                    treatments.append(treatment)

            # Extract EOT forms
            eot_forms = [form for form in forms if isinstance(form, str) and 'End of Treatment' in form]
            eot_treatments = []
            for form in eot_forms:
                if '-' in form:
                    treatment = form.split('-')[-1].strip()
                    eot_treatments.append(treatment)

            # Extract CRF forms based on patterns
            crf_patterns = [
                "^Adverse Events$", "^Adverse Events \\{Mixed form\\}$", "^Prior/Concomitant Medications$",
                "Medical History", "Enrollment", "^Prior/Concomitant Procedures/Surgeries$",
                "End of Study", "Death Details", "End of Treatment", "Demographics", "^Subject$",
                "Study Drug Administration", "Disease History", "Time-point Response Assessment",
                "Disease Assessment", "^Target Lesions", "^Non-Target Lesions", "^New Lesion",
                "Vital Signs", "ECG Local"
            ]

            # Add TLS Assessment and Chemistry - Local patterns for Heme studies
            if self.tumor_type == "Heme":
                crf_patterns.append("TLS Assessment")
                crf_patterns.append("Chemistry - Local")

            # Create a dictionary to store form types and their OIDs
            crf_forms = {}
            for pattern in crf_patterns:
                matching_forms = [form for form in forms if isinstance(form, str) and re.search(pattern, form)]
                for form in matching_forms:
                    form_type = pattern.replace("^", "").replace("$", "").replace("\\", "")
                    if form in form_mapping:
                        if form_type not in crf_forms:
                            crf_forms[form_type] = []
                        crf_forms[form_type].append(form_mapping[form])

            return {
                'project_name': project_name,
                'study_id': full_study_id,
                'treatments': treatments,
                'eot_treatments': eot_treatments,
                'crf_forms': crf_forms
            }

        except Exception as e:
            raise Exception(f"Error extracting study information: {str(e)}")

    def _generate_table_mappings(self):
        """Generate mappings between standard table names and actual table names from ALS."""
        try:
            # Save the uploaded file temporarily
            with open("temp_als_file.xlsx", "wb") as f:
                f.write(self.als_file.getvalue())

            # Read ALS file sheets
            forms_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms", engine='openpyxl')

            # Clean up temporary file
            Path("temp_als_file.xlsx").unlink()

            # Process forms to get table mappings
            forms_df = forms_df[['OID', 'DraftFormName']].copy()

            # For rs_r and mh_dx, we need to get all matching tables, not just the first one
            rs_r_tables = forms_df[forms_df['DraftFormName'].str.contains('Disease Assessment|Time-point Response Assessment', case=False, na=False)]['OID'].tolist()
            rs_r_tables = [oid.lower() for oid in rs_r_tables] if rs_r_tables else ['rs_r']

            mh_dx_tables = forms_df[forms_df['DraftFormName'].str.contains('Disease History', case=False, na=False)]['OID'].tolist()
            mh_dx_tables = [oid.lower() for oid in mh_dx_tables] if mh_dx_tables else ['mh_dx']

            # Get the default (first) table for each type
            rs_r_default = rs_r_tables[0] if rs_r_tables else 'rs_r'
            mh_dx_default = mh_dx_tables[0] if mh_dx_tables else 'mh_dx'

            # Map standard names to actual form names and convert to lowercase
            table_mappings = {
                'ds_enr': forms_df[forms_df['DraftFormName'].str.contains('Enrollment', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Enrollment', case=False, na=False)].empty else 'ds_enr',
                'subject': forms_df[forms_df['DraftFormName'].str.contains('Subject', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Subject', case=False, na=False)].empty else 'subject',
                'sd': forms_df[forms_df['DraftFormName'].str.contains('End of Study', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('End of Study', case=False, na=False)].empty else 'sd',
                'dd': forms_df[forms_df['DraftFormName'].str.contains('Death', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Death', case=False, na=False)].empty else 'dd',
                'dm': forms_df[forms_df['DraftFormName'].str.contains('Demographics', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Demographics', case=False, na=False)].empty else 'dm',
                'rs_r': rs_r_default,
                'mh_dx': mh_dx_default,
                # Store all tables for rs_r and mh_dx
                'rs_r_tables': rs_r_tables,
                'mh_dx_tables': mh_dx_tables
            }

            return table_mappings

        except Exception as e:
            raise Exception(f"Error generating table mappings: {str(e)}")

    def _generate_patient_profile_table_mappings(self):
        """Generate table mappings for PatientProfile function parameters."""
        try:
            # Save the uploaded file temporarily
            with open("temp_als_file.xlsx", "wb") as f:
                f.write(self.als_file.getvalue())

            # Read ALS file sheets
            forms_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms", engine='openpyxl')

            # Clean up temporary file
            Path("temp_als_file.xlsx").unlink()

            # Process forms to get table mappings
            forms_df = forms_df[['OID', 'DraftFormName']].copy()
            # Filter out rows where DraftFormName contains "_INACTIVE"
            forms_df = forms_df[~forms_df['DraftFormName'].str.contains('_INACTIVE', na=False)]
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df.drop('OID', axis=1).dropna()

            # Basic table mappings
            table_mappings = {
                'ae': forms_df[forms_df['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \\{Mixed form\\}$', case=False, na=False)]['Form'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \\{Mixed form\\}$', case=False, na=False)].empty else 'ae',
                'cm': forms_df[forms_df['DraftFormName'].str.contains('Prior/Concomitant Medications$', case=False, na=False)]['Form'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Prior/Concomitant Medications$', case=False, na=False)].empty else 'cm',
                'pproc': forms_df[forms_df['DraftFormName'].str.contains('Prior/Concomitant Procedures/Surgeries$', case=False, na=False)]['Form'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Prior/Concomitant Procedures/Surgeries$', case=False, na=False)].empty else 'pproc',
            }

            # Find all response assessment forms (rs_*)
            rs_forms = forms_df[forms_df['DraftFormName'].str.contains('Time-point Response Assessment|Disease Assessment', case=False, na=False)]

            # Add each rs form to the mappings
            for idx, row in rs_forms.iterrows():
                form_name = row['Form'].lower()

                # Check if this is a generic response form (rs_r)
                if form_name == 'rs_r':
                    table_mappings['rs_r'] = form_name
                else:
                    # Extract a short name for the form (e.g., rs_cll, rs_nhl, rs_wm)
                    try:
                        # Try to extract a meaningful suffix from the form name
                        if '-' in row['DraftFormName']:
                            suffix = re.sub(r'[^a-zA-Z0-9]', '', row['DraftFormName'].split('-')[-1].lower())[:3]
                        else:
                            # If no hyphen, use the last 3 characters of the form name
                            suffix = re.sub(r'[^a-zA-Z0-9]', '', row['DraftFormName'].lower())[-3:]

                        short_name = 'rs_' + suffix
                    except:
                        # Fallback to a generic name with an index
                        short_name = f'rs_{idx+1}'

                    table_mappings[short_name] = form_name

            return table_mappings

        except Exception as e:
            raise Exception(f"Error generating patient profile table mappings: {str(e)}")

    def _extract_diameter_lesion_fields(self):
        """Extract diameter lesion fields from ALS file."""
        try:
            # Save the uploaded file temporarily
            with open("temp_als_file.xlsx", "wb") as f:
                f.write(self.als_file.getvalue())

            # Read Fields sheet from ALS file
            fields_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Fields", engine='openpyxl')
            forms_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms", engine='openpyxl')

            # Clean up temporary file
            Path("temp_als_file.xlsx").unlink()

            # Process fields
            fields_df = fields_df[['FormOID', 'FieldOID', 'SASLabel']].copy()
            fields_df['Form'] = fields_df['FormOID'].str.lower()
            fields_df = fields_df.dropna(subset=['SASLabel'])

            # Process forms
            forms_df = forms_df[['OID', 'DraftFormName']].copy()
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df.dropna()

            # Join fields and forms
            merged_df = fields_df.merge(forms_df, on='Form', how='left')

            # Filter for Target Lesions forms and diameter lesion fields
            target_lesion_forms = merged_df[
                merged_df['DraftFormName'].str.contains('^Target Lesions', case=False, na=False)
            ]

            # Extract diameter lesion fields
            diameter_fields = target_lesion_forms[
                target_lesion_forms['SASLabel'].str.contains('^Diameter lesion', case=False, na=False)
            ]

            # Extract the lesion letters (A, B, C, D, E, etc.)
            lesion_letters = []
            for _, row in diameter_fields.iterrows():
                sas_label = row['SASLabel']
                # Extract the letter after "Diameter lesion "
                match = re.search(r'Diameter lesion\s+([A-Z])', sas_label, re.IGNORECASE)
                if match:
                    letter = match.group(1).upper()
                    if letter not in lesion_letters:
                        lesion_letters.append(letter)

            # Sort the letters to ensure consistent order
            lesion_letters.sort()

            return {
                'lesion_letters': lesion_letters,
                'diameter_fields': diameter_fields
            }

        except Exception as e:
            # Return empty result if there's an error
            return {
                'lesion_letters': [],
                'diameter_fields': pd.DataFrame()
            }

    def _get_eg_vs_table_counts(self):
        """Get the number of ECG and Vital Signs tables from the study."""
        try:
            crf_forms = self.study_info.get('crf_forms', {})

            eg_count = 0
            vs_count = 0

            # Count ECG forms
            if 'ECG Local' in crf_forms:
                eg_count = len(crf_forms['ECG Local'])

            # Count Vital Signs forms
            if 'Vital Signs' in crf_forms:
                vs_count = len(crf_forms['Vital Signs'])

            return eg_count, vs_count

        except Exception as e:
            # Default to common numbers if there's an error
            return 0, 0  # Default: 0 ECG forms, 0 VS forms

    def _generate_diameter_lesion_code(self):
        """Generate dynamic R code for diameter lesion processing."""
        lesion_letters = self.diameter_lesion_fields.get('lesion_letters', [])

        if len(lesion_letters) == 2:
            # If we have exactly 2 lesions, use simplified code
            return """  standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
    dplyr::mutate(tl_split_b_diam = dplyr::if_else(
        is.na(.data$tl_split_b_diam), NA_real_,
        dplyr::select(.,
                    c(.data$tl_split_b_diam
                      )) %>% rowSums(na.rm = TRUE))) %>%
    dplyr::select(-.data$tl_split_b_diam)"""
        elif len(lesion_letters) == 3:
            # If we have exactly 3 lesions, use the original code
            return """  standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
    dplyr::mutate(tl_split_b_diam = dplyr::if_else(
        is.na(.data$tl_split_b_diam) &
        is.na(.data$tl_split_c_diam) , NA_real_,
        dplyr::select(.,
                    c(.data$tl_split_b_diam,
                      .data$tl_split_c_diam
                      )) %>% rowSums(na.rm = TRUE))) %>%
    dplyr::select(-.data$tl_split_c_diam)"""
        elif len(lesion_letters) > 3:
            # Generate dynamic code for more than 3 lesions
            # Start with the base case from 3 lesions (A->tl_split_b_diam, B->tl_split_c_diam)
            # and add additional fields for lesions beyond C
            na_conditions = [
                "is.na(.data$tl_split_b_diam)",
                "is.na(.data$tl_split_c_diam)"
            ]
            select_columns = [".data$tl_split_b_diam", ".data$tl_split_c_diam"]
            select_columns_to_remove = [".data$tl_split_c_diam"]

            # Add additional fields for lesions beyond the first 3 (A, B, C)
            # Start from the 4th lesion (index 3) which corresponds to D
            for i in range(3, len(lesion_letters)):
                letter = lesion_letters[i]
                # Map D->d, E->e, F->f, etc.
                field_name = f"tl_split_{letter.lower()}_diam"
                na_conditions.append(f"is.na(.data${field_name})")
                select_columns.append(f".data${field_name}")
                select_columns_to_remove.append(f".data${field_name}")

            # Join conditions with &
            na_condition_str = " &\n        ".join(na_conditions)

            # Join select columns with comma and proper indentation
            select_columns_str = ",\n                      ".join(select_columns)

            # Join columns to remove (all except the first one which becomes the sum)
            if select_columns_to_remove:
                remove_columns_str = ", ".join(select_columns_to_remove)
                select_remove_str = f" %>%\n    dplyr::select(-{remove_columns_str})"
            else:
                select_remove_str = ""

            return f"""  standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
    dplyr::mutate(tl_split_b_diam = dplyr::if_else(
        {na_condition_str} , NA_real_,
        dplyr::select(.,
                    c({select_columns_str}
                      )) %>% rowSums(na.rm = TRUE))){select_remove_str}"""
        else:
            # Fallback for cases with fewer than 2 lesions or no lesions
            return """  standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
    dplyr::mutate(tl_split_b_diam = dplyr::if_else(
        is.na(.data$tl_split_b_diam), NA_real_,
        dplyr::select(.,
                    c(.data$tl_split_b_diam
                      )) %>% rowSums(na.rm = TRUE))) %>%
    dplyr::select(-.data$tl_split_b_diam)"""

    def _generate_remaining_code(self):
        """Generate the remaining code for the controller."""
        # This method is not used anymore as we've integrated all the code in generate_controller
        pass

    def _get_path_components(self, study_id):
        """Extract path components from study_id for proper directory structure."""
        # Determine if blinded or unblinded
        if study_id.startswith('b_'):
            blind_type = 'blinded'
            base_study = study_id[2:]  # Remove 'b_' prefix
        elif study_id.startswith('u_'):
            blind_type = 'unblinded'
            base_study = study_id[2:]  # Remove 'u_' prefix
        else:
            blind_type = 'blinded'  # Default to blinded
            base_study = study_id

        # Extract the study name without the last part (e.g., bgb_16673 from bgb_16673_101)
        parts = base_study.split('_')
        if len(parts) >= 3:
            # For studies like bgb_16673_101, take bgb_16673
            study_name = '_'.join(parts[:-1])
        else:
            # For shorter names, use the whole base_study
            study_name = base_study
        outfolder_study = study_id.replace('_', '.')
        return blind_type, study_name, base_study, outfolder_study

    def generate_controller(self):
        """Generate controller R code."""
        study_id = self.study_info['study_id']

        # Generate function header
        function_name = f"controllerMDR_{study_id}"

        # Conditional imports and parameters based on tumor type
        if self.tumor_type == "Heme":
            # For Heme studies, exclude disease assessment imports and parameters
            imports_section = """#' @importFrom dplyr filter select pull mutate_at mutate_if distinct bind_rows
#' @importFrom readr read_csv
#' @importFrom readxl read_xlsx
#' @importFrom stringr str_replace_all str_to_lower
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_directory_exists assert_file_exists assert_data_frame
#' @importFrom log4r info warn error file_appender
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data :=
#' @importFrom mdr.labCTCAE labCTCAE
#' @importFrom stats na.omit
#' @importFrom utils write.table
#' @importFrom fs dir_delete"""

            examples_section = f"""#' @examples
#' \\dontrun{{
#' {function_name}(EDC_input_File = {study_id}_datestamp.txt,
#'                              testing_outFolder = NULL,
#'                              develop.f = NULL, vpath = ".vault")
#' }}"""

            function_params = f"""{function_name} <- function(EDC_input_File,
                                        testing_outFolder = NULL,
                                        develop.f = NULL,
                                        vpath) {{"""

            # For Heme studies, exclude disease assessment related variables
            variable_definitions = """      # Define Variables ------------------------------------------------------------------
      alldatasetsList <- NULL
      dose_merged <- NULL
      td_merged <- NULL
      ae_to_lab_toxflag <- NULL
      ae_CMList <- NULL
      SAEFlag <- NULL
      MHFlag <- NULL
      SubjectInfo <- NULL
      lb_calc <- NULL
      Treated <- NULL
      PPandCorevars <- NULL
      aePatientProfile <- NULL
      lb_toxgrades <- NULL
      c_lab <- NULL
      PatientProfile <- NULL
      ae_list<-NULL
      ae_cm_link <- NULL
      cm_list <-NULL
      da_crf_lst <- NULL
      crf_config <- NULL
      standard_da_crf_lst <- NULL
      ae_to_vs_toxflag <- NULL
      TESTDAT_INT <- NULL
      chem_tls_timeImputed <- NULL
      lab_add_ALB <- NULL
      TLS_final <- NULL
      final1<-NULL
      final2<-NULL
      current_date <- Sys.Date()"""
        else:
            # For Solid Tumor studies, include all imports and parameters
            imports_section = """#' @importFrom dplyr filter select pull mutate_at mutate_if distinct bind_rows
#' @importFrom readr read_csv
#' @importFrom readxl read_xlsx
#' @importFrom stringr str_replace_all str_to_lower
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_directory_exists assert_file_exists assert_data_frame
#' @importFrom log4r info warn error file_appender
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data :=
#' @importFrom mdr.simplifiedDiseaseAssessment recist_1_1_fn new_get_da_config_info_fn standardize_crfs_fn
#' @importFrom mdr.labCTCAE labCTCAE
#' @importFrom stats na.omit
#' @importFrom utils write.table
#' @importFrom fs dir_delete"""

            examples_section = f"""#' @examples
#' \\dontrun{{
#' {function_name}(EDC_input_File = {study_id}_datestamp.txt,
#'                              testing_outFolder = NULL,
#'                              develop.f = NULL, vpath = ".vault",
#'                              da_config_full_path =
#'                              "/usrfiles/spotfire/MDR_config/diseaseAssessment/")
#' }}"""

            function_params = f"""{function_name} <- function(EDC_input_File,
                                        testing_outFolder = NULL,
                                        develop.f = NULL,
                                        vpath,
                                        da_config_full_path = "/usrfiles/spotfire/MDR_config/diseaseAssessment/") {{"""

            # For Solid Tumor studies, include all variables
            variable_definitions = """      # Define Variables ------------------------------------------------------------------
      alldatasetsList <- NULL
      dose_merged <- NULL
      td_merged <- NULL
      ae_to_lab_toxflag <- NULL
      ae_CMList <- NULL
      SAEFlag <- NULL
      MHFlag <- NULL
      SubjectInfo <- NULL
      lb_calc <- NULL
      Treated <- NULL
      PPandCorevars <- NULL
      aePatientProfile <- NULL
      lb_toxgrades <- NULL
      c_lab <- NULL
      PatientProfile <- NULL
      ae_list<-NULL
      ae_cm_link <- NULL
      cm_list <-NULL
      rs_tb<-NULL
      tl_tb <-NULL
      waterfall_spider_tb<-NULL
      da_crf_lst <- NULL
      crf_config <- NULL
      standard_da_crf_lst <- NULL
      ae_to_vs_toxflag <- NULL
      TESTDAT_INT <- NULL
      current_date <- Sys.Date()"""

        # Conditional parameter documentation based on tumor type
        if self.tumor_type == "Heme":
            param_docs = """#' @param EDC_input_File EDC DateTime data frame 1 by 1 tibble
#' @param testing_outFolder path to a testing directory
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault"""
        else:
            param_docs = """#' @param EDC_input_File EDC DateTime data frame 1 by 1 tibble
#' @param testing_outFolder path to a testing directory
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#' @param da_config_full_path The folder name where the diseaseAssessment
#' config files are stored. The default is set to the production version
#' of the config file."""

        # Start building the controller code
        controller_code = f"""#' @title {function_name}
#' @description Controller to run all the {study_id} MDR Scripts
#'
{param_docs}
#'
#' @return DF_{study_id}
#'
#' @export {function_name}
#'
{imports_section}
#'
{examples_section}
#'
#'
{function_params}
  withCallingHandlers(
    expr = {{
{variable_definitions}


      # Create studyIdVar -----------------------------------------------------------------
      # Define studyIdVar
      studyIdVar <- "{study_id}"
      # Create Controller Called Fun
      calledFun <- "{function_name}"
"""

        # Add EDC extract date section
        # Get path components for proper directory structure
        blind_type, study_name, base_study, outfolder_study= self._get_path_components(study_id)

        controller_code += f"""
      # Define EDC extract Date -----------------------------------------------------------------
      EDC_File <- readr::read_delim("/mnt/usrfiles/bgcrh/cp/{blind_type}/{study_name}/{base_study}/prod/crts/sptfrvis/sas2rds/{study_id}_datestamp.txt", ",")
      EDCextractDate <- EDC_File$EDCextractDate

      # Setup TZ and digits ---------------------------------------------------------------
      Sys.setenv(TZ = "UTC")
      options(digits.secs = 6)
      jobdatetime <- format(Sys.time(),"%Y_%m_%d_%H_%M_%OS3")

      # Set up archive and logger -----------------------------------------------------------
      #in case of copy/paste error in hard-coded path
      #for advantig studies, will need to hard code path_study_id
      # to "advantig_###"
      path_study_id <- stringr::str_replace(studyIdVar,
                                            pattern = "^(u|b)_",
                                            replacement = "")

      # Create outfolder_study by replacing underscores with dots
      outfolder_study <- stringr::str_replace_all(studyIdVar, "_", ".")

      # Check if testing_outFolder is not null
      OutFolder <- paste0("/mnt/usrfiles/bgcrh/cp/{blind_type}/{study_name}/{base_study}/prod/crts/sptfrvis/mdr.{outfolder_study}/current/analysis/")
      # if called from Compare_outputs, testing_outFolder is not null
      OutFolder <- dplyr::if_else(!is.null(testing_outFolder),
                     paste0(stringr::str_replace(stringr::str_replace(OutFolder, "/current/analysis", ""), "prod", "dev"),current_date,"/current/analysis/"),
                     OutFolder)

      # create archive folder ---------------
      archive_root <- stringr::str_replace(OutFolder,
                                           pattern = "analysis/",
                                           replacement = "") %>%
        stringr::str_replace(.,
                             pattern = "current",
                             replacement = "archive")

      archive_folder <-  archive_root %>%
        paste0(., "archive_", jobdatetime, "/")

      if (!dir.exists(archive_folder)) {{
        dir.create(archive_folder, recursive = TRUE)

      }}

      # Create a logger ---------------------------------------------------------------
      # Create Log layout
      file_layout <- function(level, ...) {{
        paste0(format(Sys.time(), "%d/%b/%Y %H:%M:%OS3 %z"),"|", "[",level,"]","|", ...,"|",
               collapse = "",gsub("@beigenecorp.net", "", as.character(as.data.frame(Sys.info())[("user"),1])),"\\r")
      }}
      # Create Log file (empty)
      file.create(paste0(archive_folder,"MDRLogging_",jobdatetime,".log"))
      # Write Header for log file
      writeLines("DateTime|LogLevel|Type|FunctionName|Message|UserName",
                 paste0(archive_folder,"MDRLogging_",jobdatetime,".log"))
      # Create logger object
      CentralLogger <- log4r::logger(threshold = "DEBUG",
                                     appenders = c(log4r::file_appender(paste0(archive_folder,
                                                                               "MDRLogging_",
                                                                               jobdatetime,
                                                                               ".log"),
                                                                        append = TRUE, file_layout)
                                     )
      )
      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Start of ",calledFun))
"""

        # Add email notification section
        controller_code += """
      # Email started ------------------------------------------------------
      #SCL 2020_03_30 - EMAIL Started
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("Start of MDR Transformation ", calledFun, " at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>", calledFun, " Started at: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
"""

        # Add folder creation and archive sections
        controller_code += f"""
      checkmate::assert_true(stringr::str_detect(OutFolder,
                                                 pattern = path_study_id))

      archive_xlsx_folder <- paste0(archive_folder, "xlsx/")

      if (!dir.exists(archive_xlsx_folder)) {{
        dir.create(archive_xlsx_folder, recursive = TRUE)

      }}

      checkmate::assert_directory_exists(archive_xlsx_folder)

      archive_analysis_folder <- paste0(archive_folder, "analysis/")

      if (!dir.exists(archive_analysis_folder)) {{
        dir.create(archive_analysis_folder, recursive = TRUE)

      }}

      checkmate::assert_directory_exists(archive_analysis_folder)

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","archive xlsx and analysis folders created."))

      #CREATE OUTFOLDER - DO NOT DELETE -----------------------------------------

      if (!dir.exists(OutFolder)) {{
        dir.create(OutFolder, recursive = TRUE)

      }}

      checkmate::assert_directory_exists(OutFolder)
      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","OutFolder created."))

      #create xlsx folder --------
      xlsx_folder <- stringr::str_replace(OutFolder,
                                          pattern = "analysis",
                                          replacement = "xlsx")

      checkmate::assert_true(stringr::str_detect(xlsx_folder,
                                                 pattern = path_study_id))

      if (!dir.exists(xlsx_folder)) {{
        dir.create(xlsx_folder, recursive = TRUE)

      }}

      checkmate::assert_directory_exists(xlsx_folder)

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","xlsx and analysis folders."))
"""

        # Add archive files section
        controller_code += """
      #Archive xlsx and analysis files --------------------
      all_xlsx_files <- list.files(xlsx_folder)

      purrr::walk2(.x = paste0(xlsx_folder, all_xlsx_files),
                   .y = paste0(archive_folder, "xlsx/", all_xlsx_files),
                   .f = function(.x, .y) file.copy(from = .x, to = .y))

      #will delete empty xlsx directory without conditional
      if (length(all_xlsx_files) > 0) {
        purrr::walk(paste0(xlsx_folder, all_xlsx_files),
                    file.remove)

      }

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","xlsx files archived."))

      all_analysis_files <- list.files(OutFolder)

      purrr::walk2(.x = paste0(OutFolder, all_analysis_files),
                   .y = paste0(archive_folder, "analysis/", all_analysis_files),
                   .f = function(.x, .y) file.copy(from = .x, to = .y))

      #will delete empty analysis directory without conditional
      if (length(all_analysis_files) > 0) {
        purrr::walk(paste0(OutFolder, all_analysis_files),
                    file.remove)

      }

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","analysis files archived."))
"""

        # Add delete old archives section
        controller_code += """
      # delete archives > 30 days out -------------------------
      archive_dirs <- list.dirs(archive_root,
                                full.names = FALSE,
                                recursive = FALSE)

      archive_dates <- archive_dirs %>%
        stringr::str_replace(.,
                             pattern = "archive_",
                             replacement = "") %>%
        purrr::map(.x = .,
                   .f = function(.x) paste0(stringr::str_split(.x,
                                                               pattern = "_",
                                                               simplify = TRUE)[c(1, 2, 3)],
                                            collapse = "-")) %>%
        purrr::map(., as.Date)

      delete_lgl_lst <- purrr::map(.x = archive_dates,
                                   .f = function(.x) difftime(Sys.Date(), .x, units = "days") > 30)

      dirs_to_delete <- purrr::map2(.x = archive_dirs,
                                    .y = delete_lgl_lst,
                                    .f = function(.x, .y) ifelse(.y == TRUE, .x, NA)) %>%
        unlist() %>%
        stats::na.omit()

      if (length(dirs_to_delete) > 0) {
        dirs_to_delete <- dirs_to_delete %>%
          paste0(archive_root, ., "/")
        purrr::walk(dirs_to_delete, fs::dir_delete)

      }
"""

        # Add EDC input file section
        controller_code += f"""
      #print EDC input file to analysis location ----------------
      edc_input_folder <- stringr::str_replace(OutFolder,
                                               pattern = "analysis/",
                                               replacement = "")

      utils::write.table(EDC_input_File,
                         file = paste0(edc_input_folder, "{study_id}_datestamp.txt"),
                         sep = "\\t",
                         row.names = FALSE,
                         na = "")

      # Load Study alldatasetsList objects ------------------------------------------------------------
      # Extract the specified `sourceLocation`
      crf_sourceLocation <- "/mnt/usrfiles/bgcrh/cp/{blind_type}/{study_name}/{base_study}/prod/crts/sptfrvis/sas2rds/"
      fst_sourceLocation <- "/mnt/usrfiles/bgcrh/cp/{blind_type}/{study_name}/{base_study}/prod/crts/sptfrvis/diffTrackR/output/"

      # in case of copy/paste error with paths
      checkmate::assert_true(stringr::str_detect(crf_sourceLocation,
                                                 pattern = path_study_id))

      checkmate::assert_true(stringr::str_detect(fst_sourceLocation,
                                                 pattern = path_study_id))

      alldatasetsList <- mdrAddNewDataFlag(diff_track_path = fst_sourceLocation,
                                           crf_path = crf_sourceLocation,
                                           dev_ind = develop.f,
                                           vault_path = vpath,
                                           temp_logger = CentralLogger,
                                           job_date_time = jobdatetime,
                                           study_id = studyIdVar)

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","All alldatasetsList created"))
"""

        # Generate CRF extraction section dynamically
        crf_forms = self.study_info.get('crf_forms', {})

        # Extract form names from ALS file
        form_type_mapping = {}

        # Get the forms from the ALS file
        for form_type, oids in crf_forms.items():
            if not oids:
                continue

            # Get the first OID for this form type
            oid = oids[0].lower() if oids else ""

            # Special cases for certain form types with flexible pattern matching
            if re.search(r"End\s+of\s+Treatment", form_type, re.IGNORECASE):
                form_type_mapping[form_type] = "ds_eot"
            else:
                # For other forms, use the OID as the variable name
                form_type_mapping[form_type] = oid

        # Create a list of CRF variables to extract
        crf_vars = []
        crf_assignments = []

        # Add standard CRFs that should always be included
        standard_crfs = ["lab"]
        for crf in standard_crfs:
            if crf not in crf_vars:
                crf_vars.append(crf)

        # Add standard CRF assignments
        if "lab" not in [assignment.split(" <- ")[0] for assignment in crf_assignments]:
            crf_assignments.append("lab <- alldatasetsList$lab")

        # Track which forms are mapped to td variables to avoid duplicates
        td_mapped_forms = []

        # Track which forms are mapped to ec_inf variables to avoid duplicates
        ec_inf_mapped_forms = []

        # Track whether we've already handled the ae assignment to avoid duplicates
        ae_assignment_added = False

        # Add CRFs from the ALS file
        for form_type, oids in crf_forms.items():
            var_name = form_type_mapping.get(form_type)
            # Skip End of Treatment forms as they will be handled separately
            if var_name == "ds_eot":
                td_mapped_forms.append(form_type)
                continue
            # Skip Study Drug Administration forms as they will be handled separately
            if var_name == "ec_inf":
                ec_inf_mapped_forms.append(form_type)
                continue

            # Skip End of Treatment forms as they will be handled separately
            if form_type in td_mapped_forms:
                continue
            # Skip Study Drug Administration forms as they will be handled separately
            if form_type in ec_inf_mapped_forms:
                continue

            # Special case for Adverse Events forms - only one ae table should exist
            if var_name == "ae" or var_name == "ae_mix":
                if not ae_assignment_added:
                    # Use the first OID found for ae, there should be only one ae table
                    first_oid = oids[0]
                    crf_assignments.append(f"ae <- alldatasetsList${first_oid}")
                    if "ae" not in crf_vars:
                        crf_vars.append("ae")
                    ae_assignment_added = True
                # Ensure ae_mix is never added to crf_vars
                continue  # Skip further processing for ae/ae_mix forms
            elif len(oids) > 1:
                # Handle multiple instances of the same form type (excluding ae)
                for oid in oids:
                    # For forms with multiple instances, use the original form name directly
                    crf_assignments.append(f"{oid} <- alldatasetsList${oid}")
                    if oid not in crf_vars:
                        crf_vars.append(oid)
            elif len(oids) == 1:
                # Handle single instance forms (excluding ae which is handled above)
                oid = oids[0]
                crf_assignments.append(f"{oid} <- alldatasetsList${oid}")
                if oid not in crf_vars:
                    crf_vars.append(oid)

            # Add var_name to crf_vars for non-ae forms only
            if var_name and var_name not in crf_vars and var_name != "ae_mix":
                crf_vars.append(var_name)

        # Count Study Drug Administration forms
        ec_inf_forms = []
        for form_type, oids in crf_forms.items():
            # Check if this form type is mapped to ec_inf
            if form_type in ec_inf_mapped_forms or (form_type in form_type_mapping and form_type_mapping[form_type] == "ec_inf"):
                ec_inf_forms.extend(oids)

        # Add special cases for Study Drug Administration forms
        if ec_inf_forms:
            # Use the original form names directly
            for oid in ec_inf_forms:
                if oid not in crf_vars:
                    crf_vars.append(oid)

                # Use the actual OID from the ALS file
                # Check if this assignment already exists to avoid duplicates
                assignment = f"{oid} <- alldatasetsList${oid}"
                if assignment not in crf_assignments:
                    crf_assignments.append(assignment)

        # Count End of Treatment forms
        eot_forms = []
        for form_type, oids in crf_forms.items():
            # Check if this form type is mapped to ds_eot
            if form_type in form_type_mapping and form_type_mapping[form_type] == "ds_eot":
                eot_forms.extend(oids)

        # Add special cases for End of Treatment forms
        if eot_forms:
            # Map to td1, td2, td3, etc.
            for i in range(1, len(eot_forms) + 1):
                var_name = f"td{i}"
                if var_name not in crf_vars:
                    crf_vars.append(var_name)

                if i-1 < len(eot_forms):
                    # Use the actual OID from the ALS file
                    # Check if this assignment already exists to avoid duplicates
                    assignment = f"{var_name} <- alldatasetsList${eot_forms[i-1]}"
                    if assignment not in crf_assignments:
                        crf_assignments.append(assignment)

        # Add table mappings for SubjectInfo function
        # Extract table mappings
        ds_enr = self.table_mappings['ds_enr']
        subject = self.table_mappings['subject']
        sd = self.table_mappings['sd']
        dd = self.table_mappings['dd']
        dm = self.table_mappings['dm']
        rs_r = self.table_mappings['rs_r']
        mh_dx = self.table_mappings['mh_dx']

        # Get all rs_r and mh_dx tables
        rs_r_tables = self.table_mappings['rs_r_tables']
        mh_dx_tables = self.table_mappings['mh_dx_tables']

        # Add assignments for table mappings if they don't already exist
        table_assignments = [
            f"{ds_enr} <- alldatasetsList${ds_enr}",
            f"{subject} <- alldatasetsList${subject}",
            f"{sd} <- alldatasetsList${sd}",
            f"{dd} <- alldatasetsList${dd}",
            f"{dm} <- alldatasetsList${dm}",
            f"{rs_r} <- alldatasetsList${rs_r}",
            f"{mh_dx} <- alldatasetsList${mh_dx}"
        ]

        # Add additional rs_r and mh_dx tables
        for table in rs_r_tables[1:]:  # Skip the first one as it's already included
            table_assignments.append(f"{table} <- alldatasetsList${table}")
        for table in mh_dx_tables[1:]:  # Skip the first one as it's already included
            table_assignments.append(f"{table} <- alldatasetsList${table}")

        # Add table assignments to crf_assignments if they don't already exist
        for assignment in table_assignments:
            if assignment not in crf_assignments:
                crf_assignments.append(assignment)
                var_name = assignment.split(" <- ")[0]
                if var_name not in crf_vars:
                    crf_vars.append(var_name)

        # Add patient profile table mappings for PatientProfile function
        ae_pp = self.patient_profile_table_mappings['ae']
        cm_pp = self.patient_profile_table_mappings['cm']
        pproc_pp = self.patient_profile_table_mappings['pproc']

        # Get all rs tables for patient profile
        rs_tables_pp = []
        for key, value in self.patient_profile_table_mappings.items():
            if key.startswith('rs_'):
                rs_tables_pp.append((key, value))

        # Add patient profile table assignments
        pp_table_assignments = [
            f"{cm_pp} <- alldatasetsList${cm_pp}",
            f"{pproc_pp} <- alldatasetsList${pproc_pp}"
        ]

        # Special handling for ae_pp - if it's ae_mix, don't add it as a separate assignment
        # since we already have ae <- alldatasetsList$ae_mix
        if ae_pp != "ae_mix":
            pp_table_assignments.insert(0, f"{ae_pp} <- alldatasetsList${ae_pp}")

        # Add rs table assignments for patient profile
        for rs_key, rs_value in rs_tables_pp:
            pp_table_assignments.append(f"{rs_value} <- alldatasetsList${rs_value}")

        # Add patient profile table assignments to crf_assignments if they don't already exist
        for assignment in pp_table_assignments:
            if assignment not in crf_assignments:
                crf_assignments.append(assignment)
                var_name = assignment.split(" <- ")[0]
                if var_name not in crf_vars and var_name != "ae_mix":
                    crf_vars.append(var_name)

        # Add TLS Assessment and Chemistry - Local forms for Heme studies
        if self.tumor_type == "Heme":
            tls_forms = []
            chemistry_local_forms = []
            for form_type, oids in crf_forms.items():
                # Check if this form type contains "TLS Assessment"
                if "TLS Assessment" in form_type:
                    tls_forms.extend(oids)
                # Check if this form type contains "Chemistry - Local"
                if "Chemistry - Local" in form_type:
                    chemistry_local_forms.extend(oids)

            # Add TLS Assessment forms to CRF extraction
            if tls_forms:
                for oid in tls_forms:
                    if oid not in crf_vars:
                        crf_vars.append(oid)

                    # Add assignment for each TLS Assessment form
                    assignment = f"{oid} <- alldatasetsList${oid}"
                    if assignment not in crf_assignments:
                        crf_assignments.append(assignment)

            # Add Chemistry - Local forms to CRF extraction
            if chemistry_local_forms:
                for oid in chemistry_local_forms:
                    if oid not in crf_vars:
                        crf_vars.append(oid)

                    # Add assignment for each Chemistry - Local form
                    assignment = f"{oid} <- alldatasetsList${oid}"
                    if assignment not in crf_assignments:
                        crf_assignments.append(assignment)

        # Final cleanup: ensure ae_mix is completely removed from both lists
        crf_vars = [var for var in crf_vars if var != "ae_mix"]
        crf_assignments = [assignment for assignment in crf_assignments if not assignment.startswith("ae_mix <-")]

        # Generate the CRF extraction code
        crf_extraction_code = """
  #UpdateVars
  crfs_to_extract <- c("{}")
  # Extract required CRFs
  {}

  #assert dataframes have atleast 2 rows
  for (i_crfs in seq_along(crfs_to_extract)) {{
    checkmate::assert_data_frame(get(crfs_to_extract[i_crfs]), min.rows = 0)
  }}
  rm(i_crfs)
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Extracted required CRFs"))

  #Directories -----------------------------------------------------------------------
  ctcaelookupFolder <-  "/usrfiles/spotfire/MDR_config/labCTCAE/"
""".format('", "'.join(crf_vars), '\n  '.join(crf_assignments))

        controller_code += crf_extraction_code

        # Add directories section
        controller_code += """
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if ctcaelookupFolder exists", ctcaelookupFolder))
  checkmate::assert_directory_exists(ctcaelookupFolder)

  ctcvslookupFolder <-  "/usrfiles/spotfire/MDR_config/vsCTCAE/"
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if ctcvslookupFolder exists", ctcvslookupFolder))
  checkmate::assert_directory_exists(ctcvslookupFolder)

  # Define Study Parameters/Variables -------------------------------------------------

  # Central Lab (if available set to TRUE)
  central_lab.f <- FALSE
  if (central_lab.f) {
    # Check Central Lab File exists and load
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check Central Lab files"))
    c_lab_path <- "replace_me"
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if c_lab_path file exists"))
    checkmate::assert_file_exists(c_lab_path)
    c_lab <- readRDS(c_lab_path)
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Created c_lab dataframe"))
    #UpdateVars: Map central lab variables to standardized names
    c_lab <- c_lab %>%
      dplyr::mutate(`Subject` =  dplyr::if_else(!is.na(.data$SUBJID) & !.data$SUBJID == "", .data$SUBJID, .data$SCRNNUM)) %>%
      dplyr::select(
        Subject = .data$Subject,
        LBTEST = .data$LBTEST ,
        `SiteNumber` = .data$SITEID ,
        Folder = .data$VISIT,
        project = .data$STUDYID,
        `RecordDate` = .data$LBDTM ,
        NumericValue = .data$RPTRESN,
        LabLow = .data$RPTNRLO,
        LabHigh =  .data$RPTNRHI,
        LabUnits = .data$RPTU,
        StdValue = .data$SIRESN,
        StdLow  =  .data$SINRLO,
        StdHigh = .data$SINRHI,
        StdUnits = .data$SIU,
        LabName = .data$LBNAM,
        LabFlag = .data$ALRTFL,
        AnalyteValue = .data$RPTRESC,
        FormName = .data$BATTRNAM
      )


  }

  # AnalytePT file
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if AnalytePT file exists"))
  checkmate::assert_file_exists(paste0(ctcaelookupFolder,"AnalytePT.xlsx"))
  AnalytePT <- readxl::read_xlsx(paste0(ctcaelookupFolder,"AnalytePT.xlsx"))

  # vitalPT file
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if vitalPT file exists"))
  checkmate::assert_file_exists(paste0(ctcvslookupFolder,"vitalPT.xlsx"))
  vitalPT <- readxl::read_xlsx(paste0(ctcvslookupFolder,"vitalPT.xlsx"))

  # create a llst to store analysis output------------------------------------------------------
  analysis_list <- list()
"""

        # Generate function calls for each module
        study_id = self.study_info['study_id']

        # Add ae_1_linkAECMColumn function call
        controller_code += f"""
  # Call ae_1_linkAECMColumn_{study_id} Function ------------------------------------------------------
  ae_1_linkAECMColumn_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
                    tempLogger = CentralLogger, jobdatetime = jobdatetime, ae_tmp = ae, cm_tmp = cm,
                    develop.f = develop.f, vpath = vpath)
        # add analysis output to list
        ae_cm_list <- list("ae_cm_link" = ae_cm_link)
        analysis_list<-append(analysis_list, ae_cm_list)
        analysis_list<-append(analysis_list, cm_list)
"""

        # Add doseMerge function call with dynamic parameters
        # Collect all drug-related forms dynamically
        all_drug_forms = []

        # Add Study Drug Administration forms (ec_inf_forms)
        all_drug_forms.extend(ec_inf_forms)

        # Look for other drug-related forms in crf_forms
        # These could be any forms that contain drug-related keywords
        drug_keywords = ['drug', 'dose', 'administration', 'infusion', 'oral', 'treatment']
        for form_type, oids in crf_forms.items():
            # Skip forms already included in ec_inf_forms
            if any(oid in ec_inf_forms for oid in oids):
                continue
            # Skip End of Treatment forms
            if form_type in form_type_mapping and form_type_mapping[form_type] == "ds_eot":
                continue
            # Check if this form type contains drug-related keywords
            if any(keyword in form_type.lower() for keyword in drug_keywords):
                all_drug_forms.extend(oids)

        # Generate the parameter list based on all drug forms found
        dose_merge_params = []
        for i, oid in enumerate(all_drug_forms):
            param_name = f"drug{i+1}"
            dose_merge_params.append(f"{param_name} = {oid}")

        # Join the parameters
        dose_merge_param_str = ", ".join(dose_merge_params)

        controller_code += f"""
  # Call doseMerge_{study_id} Function ------------------------------------------------------
  doseMerge_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
                   tempLogger = CentralLogger, jobdatetime = jobdatetime,
                   {dose_merge_param_str},
                   develop.f = develop.f, vpath = vpath)
        # add analysis output to list
        dose_list <- list("dose_merged" = dose_merged)
        analysis_list<-append(analysis_list, dose_list)
"""

        # Add tdMerge function call with dynamic parameters
        # Generate the parameter list based on the number of forms
        td_merge_params = []

        # Add End of Treatment forms as parameters - use all available forms
        for i in range(1, len(eot_forms) + 1):  # Use all available td forms
            td_merge_params.append(f"td_{i} = td{i}")

        # Join the parameters
        td_merge_param_str = ", ".join(td_merge_params)

        controller_code += f"""
  # Call tdMerge_{study_id} Function ------------------------------------------------------
  tdMerge_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
                 tempLogger = CentralLogger, jobdatetime = jobdatetime,
                 {td_merge_param_str}, develop.f = develop.f, vpath = vpath)
       # add analysis output to list
        tdmerge_list <- list("td_merged" = td_merged)
        analysis_list<-append(analysis_list, tdmerge_list)
"""

        # Add subjectInfo function call with dynamic parameters
        # Build parameter list for SubjectInfo function call
        subject_info_params = [
            "studyId = studyIdVar",
            "sourceLocation = OutFolder",
            "tempLogger = CentralLogger",
            "jobdatetime = jobdatetime",
            "dose_merged = dose_merged",
            f"{ds_enr} = {ds_enr}",
            f"{subject} = {subject}",
            f"{sd} = {sd}",
            f"{dd} = {dd}",
            "td_merged = td_merged",
            f"{dm} = {dm}",
            f"{rs_r} = {rs_r}"
        ]

        # Add additional rs_r tables if they exist
        for table in rs_r_tables[1:]:  # Skip the first one as it's already included
            subject_info_params.append(f"{table} = {table}")

        subject_info_params.append(f"{mh_dx} = {mh_dx}")

        # Add additional mh_dx tables if they exist
        for table in mh_dx_tables[1:]:  # Skip the first one as it's already included
            subject_info_params.append(f"{table} = {table}")

        subject_info_params.extend([
            "develop.f = develop.f",
            "vpath = vpath"
        ])

        # Join parameters with proper formatting
        subject_info_param_str = ",\n                 ".join(subject_info_params)

        controller_code += f"""
  # Call subjectInfo_{study_id} Function ------------------------------------------------------
  subjectInfo_{study_id}({subject_info_param_str})
       # add analysis output to list
        subject_list <- list("SubjectInfo" = SubjectInfo)
        analysis_list<-append(analysis_list, subject_list)
"""

        # Add disease assessment section (only for Solid Tumor studies)
        if self.tumor_type != "Heme":
            # Generate dynamic diameter lesion code
            diameter_lesion_code = self._generate_diameter_lesion_code()

            controller_code += f"""
  # Call diseaseAssessment Functions ------------------------------------------------------------
  #get study-specific configuration information
  da_config <-mdr.simplifiedDiseaseAssessment::new_get_da_config_info_fn(study_name = studyIdVar,
                                                                config_path = da_config_full_path)
  #create list of used CRFs for the study
  da_crf_name_lst <- purrr::map(.x = da_config$crf_config,
                                .f = function(.x) .x$crf_name)
  da_crf_name_lst$subject_info <- NULL

  da_crf_lst <- sapply(da_crf_name_lst, get, envir = sys.frame(sys.parent(0)), simplify = FALSE)
  names(da_crf_lst) <- da_crf_name_lst
  da_crf_lst$SubjectInfo <- SubjectInfo

  standard_da_crf_lst <-mdr.simplifiedDiseaseAssessment::standardize_crfs_fn(da_crf_lst,
                                                                    da_config$crf_config,
                                                                    studyIdVar,
                                                                    CentralLogger)

  # SEE WORKING INSTRUCTIONS FOR DISEASE ASSESSMENT
  # controller updates here may be necessary
  standard_da_crf_lst$clean$nl_tb <- standard_da_crf_lst$clean$nl_tb %>%
    dplyr::filter(.data$nl_yn == "Yes")

{diameter_lesion_code}

  # get results of disease assessment analysis
  da_results <-mdr.simplifiedDiseaseAssessment::recist_1_1_fn(study_name = studyIdVar,
                                                     study_data_lst = standard_da_crf_lst,
                                                     temp_logger = CentralLogger,
                                                     job_date_time = jobdatetime,
                                                     develop.f = develop.f,
                                                     vpath = vpath,
                                                     config_info = da_config,
                                                     spotfire_ind = 1)

  # add analysis output to list
  waterfall_spider_list <- list("waterfall_spider_tb" = waterfall_spider_tb)
  analysis_list<-append(analysis_list, waterfall_spider_list)

  swimmers_list <- list("swimmers_tb" = da_results$swimmers_tb)
  analysis_list<-append(analysis_list, swimmers_list)

  tl_list <- list("tl_tb" = tl_tb)
  analysis_list<-append(analysis_list, tl_list)

  rs_list <- list("rs_tb" = rs_tb)
  analysis_list<-append(analysis_list, rs_list)

  da_error_list <- list("da_error_tb" = da_results$error_tb)
  analysis_list<-append(analysis_list, da_error_list)

  waterfall_spider_removal_list <- list("waterfall_spider_removal_tb" = da_results$waterfall_spider_removal_tb)
  analysis_list<-append(analysis_list, waterfall_spider_removal_list)

  rs_mismatch_pp_list <- list("rs_mismatch_pp" = da_results$rs_mismatch_pp)
  analysis_list <- append(analysis_list, rs_mismatch_pp_list)
"""

        # Add labCTCAE function call
        controller_code += """
  # Call labCTCAE function -------------------------------------------------------------------------
  lb_toxgrades <- mdr.labCTCAE::labCTCAE(lab = lab,
                                         c_lab = c_lab,
                                         central_lab.f = central_lab.f,
                                         SubjectInfo = SubjectInfo,
                                         version = 5,
                                         Hallek = "N",
                                         studyIdVar = studyIdVar,
                                         OutFolder = OutFolder,
                                         CentralLogger = CentralLogger,
                                         jobdatetime = jobdatetime,
                                         develop.f = develop.f, vpath = vpath)
"""

        # Add remaining function calls
        study_id = self.study_info['study_id']

        # Add ae_2_ToxFlag function call
        controller_code += f"""
  # Call ae_2_ToxFlag_{study_id} Function ------------------------------------------------------
  ae_2_ToxFlag_{study_id}(studyId = studyIdVar,
                        tempLogger = CentralLogger,
                        jobdatetime = jobdatetime,
                        ae = ae,
                        lb_toxgrades = lb_toxgrades,
                        AnalytePT = AnalytePT,
                        develop.f = develop.f, vpath = vpath)
"""

        # Add ae_3_SAEFlag function call
        controller_code += f"""
  # Call ae_3_SAEFlag_{study_id} Function ----------------------------------------------

  ae_3_SAEFlag_{study_id}(studyId = studyIdVar, tempLogger = CentralLogger,
                        jobdatetime = jobdatetime, ae = ae,
                        develop.f = develop.f, vpath = vpath)
"""

        # Add ae_4_MHFlag function call
        controller_code += f"""
  # Call ae_4_MHFlag_{study_id} Function ----------------------------------------------

  ae_4_MHFlag_{study_id}(studyId = studyIdVar, tempLogger = CentralLogger,
                       jobdatetime = jobdatetime,
                       ae = ae, mh = mh,
                       develop.f = develop.f, vpath = vpath)
"""

        # Add vitalctcae functions with dynamic eg and vs table counts
        eg_count, vs_count = self._get_eg_vs_table_counts()

        # Generate dynamic bind_rows for ECG tables
        if eg_count > 0:
            eg_tables = [f"standard_da_crf_lst$eg{i+1}" for i in range(eg_count)]
            eg_bind_rows = ",".join(eg_tables)
        else:
            # If no ECG tables, create an empty tibble
            eg_bind_rows = "tibble::tibble()"

        # Generate dynamic bind_rows for VS tables
        if vs_count > 0:
            vs_tables = [f"standard_da_crf_lst$vs{i+1}" for i in range(vs_count)]
            vs_bind_rows = ",".join(vs_tables)
        else:
            # If no VS tables, create an empty tibble
            vs_bind_rows = "tibble::tibble()"

        controller_code += f"""
      # Call vitalctcae_grading_{study_id} Function---------------------------------------------------------
      config_path <- paste0(ctcvslookupFolder,"{study_id}_vs_crf_config.csv")
      vitalctcae_config_{study_id}(studyId =  studyIdVar, tempLogger = CentralLogger, config_path = config_path)

      # create list of used CRFs for the study
      da_crf_name_lst <- purrr::map(.x = crf_config, .f = function(.x) .x$crf_name)
      da_crf_lst <- sapply(da_crf_name_lst, get, envir = sys.frame(sys.parent(0)), simplify = FALSE)
      names(da_crf_lst) <- da_crf_name_lst


      vitalctcae_datastandardization_{study_id}(studyId =  studyIdVar, tempLogger = CentralLogger,da_crf_lst = da_crf_lst, crf_config = crf_config)

      # merge all study specific Vital/ECG CRFs

      # if weight data is collected as a seperated CRF
      # weight_stand <- standard_da_crf_lst$weight %>%
      #   dplyr::filter(.data$WEYN =="Yes" ) %>%
      #   dplyr::mutate(Test = "weight",
      #                 Timepoint = NA_character_) %>%
      #   dplyr::select(-.data$WEYN)


      # Dynamic ECG CRFs binding - {eg_count} ECG forms found
      eg_stand <- dplyr::bind_rows({eg_bind_rows}) %>%
        dplyr::filter(.data$EGPERF =="Yes") %>%
        dplyr::mutate(Test = "QTc",
                      unit = "ms") %>%
        dplyr::select(-.data$EGPERF) %>%
        dplyr::group_by(.data$Subject, .data$InstanceName,.data$TESTDAT_INT) %>%
        dplyr::mutate(stdvalue = mean(.data$stdvalue, na.rm = TRUE)) %>%
        dplyr::distinct(.data$Subject, .data$instanceId,.keep_all = T)


      # Dynamic VS CRFs binding - {vs_count} VS forms found
      vs_merged <-  dplyr::bind_rows({vs_bind_rows}) %>%
        dplyr::filter(.data$VSYN == "Yes") %>%
        dplyr::mutate(SystolicBP_unit ="mmHg",
                      DiastolicBP_unit = "mmHg")  %>%
        dplyr::select(-.data$VSYN) %>%
        tidyr::pivot_longer(cols = c(.data$weight_stdvalue,.data$temp_stdvalue,.data$SystolicBP_stdvalue,.data$DiastolicBP_stdvalue,
                                     .data$weight_unit,.data$SystolicBP_unit,.data$DiastolicBP_unit),
                            names_to = c("Test",".value"),
                            names_pattern = "(.*)_(.*)") %>%
        dplyr::bind_rows(eg_stand)

      vitalctcae_grading_{study_id}(studyId =  studyIdVar,
                         tempLogger = CentralLogger,
                         vs_merged = vs_merged,
                         vitalPT = vitalPT,
                         lb_toxgrades_tmp = lb_toxgrades,
                         SubjectInfo_tmp = SubjectInfo,
                         ae = ae)


  # add analysis output to list
  vital <- list("vital" = vital)
  analysis_list <- append(analysis_list, vital)
"""

        # Add ae_5_AEWriteOut function call
        controller_code += f"""
  # Call ae_5_AEWriteOut_{study_id} Function ----------------------------------------------

  ae_5_AEWriteOut_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
                           tempLogger = CentralLogger, jobdatetime = jobdatetime,
                           ae = ae, ae_to_lab_toxflag = ae_to_lab_toxflag,
                           ae_to_vs_toxflag = ae_to_vs_toxflag,
                           ae_CMList = ae_CMList, SAEFlag = SAEFlag, MHFlag = MHFlag , SubjectInfo = SubjectInfo,
                           develop.f = develop.f, vpath = vpath)
        # add analysis output to list
        analysis_list<-append(analysis_list, ae_list)
"""

        # Add TLS functions for Heme studies
        if self.tumor_type == "Heme":
            controller_code += f"""
  # Call TLS Functions for Heme Studies ----------------------------------------------
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Creating TLS.xlsx"))
  # we need to process tls related tables before we bind them together, and bind all the tables together
  chem_tls <-  dplyr::bind_rows(lb_tls, lb_tls1, lb_tls2....)

  TLS_1_dataProcessing_{study_id}(studyId = studyIdVar,
                                             sourceLocation = OutFolder,
                                             tempLogger = CentralLogger,
                                             jobdatetime = jobdatetime,
                                             lab = lab, chem_local = lb_chem,
                                             chem_tls = chem_tls)


  TLS_2_abnormal_fl_{study_id}(studyId = studyIdVar,
                                        sourceLocation = OutFolder,
                                        tempLogger = CentralLogger,
                                        jobdatetime = jobdatetime,
                                        chem_tls_timeImputed=chem_tls_timeImputed,
                                        lab_add_ALB = lab_add_ALB,
                                        SubjectInfo = SubjectInfo,
                                        testing.f = F)



      # TLS_3_dose_fl_{study_id} Function ----------------------------------------------

  # TLS_3_dose_fl_{study_id}(studyId = studyIdVar,
  #                                   tempLogger = CentralLogger,
  #                                   jobdatetime = jobdatetime,
  #                                   TLS_final=TLS_final,
  #                                   ex = ex3,
  #                                   addDose = T)


      # Call TLS_4_clinical_fl_{study_id} Function ----------------------------------------------
  TLS_4_clinical_fl_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
                                        tempLogger = CentralLogger, jobdatetime = jobdatetime,
                                        ae = ae,lb_toxgrades = lb_toxgrades,
                                        TLS_final = TLS_final,  develop.f = develop.f,
                                        vpath = vpath)


		# add analysis output to list
			  tls_list <- list("TLS" = final1)
			  analysis_list<-append(analysis_list, tls_list)
			  tlsd_list <- list("TLS_details" = final2)
			  analysis_list<-append(analysis_list, tlsd_list)
"""

        # Add labCalc function call
        controller_code += f"""
  # Call labCalc_{study_id} Function ------------------------------------------------------
  labCalc_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
                 tempLogger = CentralLogger, jobdatetime = jobdatetime,
                 ae = ae, lab = lab, lb_toxgrades = lb_toxgrades,
                 AnalytePT = AnalytePT, SubjectInfo_tmp = SubjectInfo,
                 develop.f = develop.f, vpath = vpath)
        # add analysis output to list
        lb_list <- list("lab" = lb_calc)
        analysis_list<-append(analysis_list, lb_list)
"""

        # Add patientProfile function call with dynamic parameters
        # Build parameter list for PatientProfile function call
        ae_pp = self.patient_profile_table_mappings['ae']
        cm_pp = self.patient_profile_table_mappings['cm']
        pproc_pp = self.patient_profile_table_mappings['pproc']

        # Get all rs tables for patient profile
        rs_tables_pp = []
        for key, value in self.patient_profile_table_mappings.items():
            if key.startswith('rs_'):
                rs_tables_pp.append((key, value))

        # Build parameter list for PatientProfile function call
        patient_profile_params = [
            "studyId = studyIdVar",
            "sourceLocation = OutFolder",
            "tempLogger = CentralLogger",
            "jobdatetime = jobdatetime",
            "EDCextractDate = EDCextractDate",
            "SubjectInfo = SubjectInfo",
            f"ae = {ae_pp}",
            f"cm = {cm_pp}"
        ]

        # Add rs table parameters
        for rs_key, rs_value in rs_tables_pp:
            patient_profile_params.append(f"{rs_key} = {rs_value}")

        patient_profile_params.extend([
            "lb_calc = lb_calc",
            f"{pproc_pp} = {pproc_pp}",
            "td_merged = td_merged",
            "dose_merged = dose_merged",
            "Treated = Treated",
            "develop.f = develop.f",
            "vpath = vpath"
        ])

        # Join parameters with proper formatting
        patient_profile_param_str = ",\n                          ".join(patient_profile_params)

        controller_code += f"""
  # Call patientProfile_{study_id} Function ------------------------------------------------------
  patientProfile_{study_id}({patient_profile_param_str})
       # add analysis output to list
        patient_list <- list("PatientProfile" = PatientProfile)
        analysis_list<-append(analysis_list, patient_list)
        aepatient_list <- list("aePatientProfile" = aePatientProfile)
        analysis_list<-append(analysis_list, aepatient_list)
"""

        # Add study day/cohort section
        controller_code += """
   #add studyday/studyweek, cohort/enrolled/treated related, column cleaning -------------------------
    #UpdateVars
    # cohort/arm is study specific-remember to update
      appendCol<- c("Cohort","Phase","Part","Dose level",
            "FirstDoseDate","OnTreatment",
            "Enrolled","TotalEnrolled", "TotalEnrolled by Cohort/Arm",
            "Treated","TotalTreated" , "TotalTreated by Cohort/Arm")

    #UpdateVars
    # set TRUE for studies that need standardized output
    standardcol<- TRUE

    if (standardcol== TRUE) {
      checkmate::assert_true(all(appendCol %in% names(SubjectInfo)))
      called_fun <- "mdrJoinCohortStudyDay"
        StudyDay_Cohort_added_tb <- mdrJoinCohortStudyDay(studyId = studyIdVar,alldatasetsList,
                                  appendCol=appendCol, SubjectInfo, sourcetype="crf",  tempLogger = CentralLogger)
        log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","StudyDay_Cohort_added_tb created for crf."))
    }
    else {
      StudyDay_Cohort_added_tb <-alldatasetsList
    }
    # #Print xlsx -------------------------
    # #raise labels
    print_new_data_flag_lst <- purrr::map(StudyDay_Cohort_added_tb,
                        GSDSUtilities::raiseLabels,
                        attrC = "label",
                        isNullC = NA)

    purrr::walk2(.x =  print_new_data_flag_lst,
           .y = names( print_new_data_flag_lst),
           new_data_out_path = xlsx_folder,
           .f = function(.x,
                 .y,
                 new_data_out_path) writexl::write_xlsx(list("Sheet 1" = .x),
                                    path = paste0(new_data_out_path,
                                          .y,
                                          ".xlsx")))
    #Print analysis output list to rds------------------------
     saveRDS(analysis_list, file = paste0(OutFolder,"analysis_list.rds"))
     log4r::info(CentralLogger,paste0(studyIdVar,"|",calledFun,"|","Print analysis output list to rds."))

    #Add/Clean columns for analysis files and print------------------------
    if (standardcol== TRUE) {
      checkmate::assert_true(all(appendCol %in% names(SubjectInfo)))
      called_fun <- "mdrJoinCohortStudyDay"
      print_new_analysis <- mdrJoinCohortStudyDay(studyId = studyIdVar, analysis_list,
                                  appendCol=appendCol, SubjectInfo, sourcetype="analysis", tempLogger = CentralLogger)
      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","StudyDay_Cohort_added_tb created for analysis."))

    }
    else {
      #ae,cm etc not gone through raiseLabels yet
      for (n in c("ae","waterfall_spider_tb","tl_tb","rs_tb")){
        analysis_list[[n]]<-GSDSUtilities::raiseLabels(analysis_list[[n]], "label", isNullC = NA)
      }
      print_new_analysis <-analysis_list
    }

    files_name<- names(print_new_analysis)

    purrr::walk2(.x =  print_new_analysis,
              .y = files_name,
              new_data_out_path = OutFolder,
              .f = function(.x,
                            .y,
                            new_data_out_path) if ((.y == 'PatientProfile') | (.y == 'lab')) { readr::write_delim(
                              x = .x,
                              path =  paste0(new_data_out_path,.y,".csv"),
                              delim  = "|",
                              quote_escape = "double",
                              na = "")
                            }
                            else writexl::write_xlsx(list("Sheet 1" = .x),
                                                     path = paste0(new_data_out_path,
                                                                   .y,
                                                                   ".xlsx"))
                )

    # Remove alldatasetsList list
    rm(alldatasetsList)
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove alldatasetsList list"))
    rm(print_new_data_flag_lst)
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove print_new_data_flag_lst list"))
    rm(StudyDay_Cohort_added_tb)
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove StudyDay_Cohort_added_tb list"))
    rm(analysis_list)
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove analysis_list list"))
    rm(print_new_analysis)
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove print_new_analysis list"))
"""

        # Add end of controller section
        controller_code += """
  # End of Controller -----------------------------------------------------------------
  log4r::info(CentralLogger,paste0(studyIdVar,"|",calledFun,"|",calledFun,"- All done, quitting."))
  # Email finished ------------------------------------------------------
  #SCL 2029_03_30 - EMAIL finished
  if (is.null(develop.f)) {
    mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                     to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                     subject = paste0("Completion of MDR Transformation ", calledFun," jobdatetime ",jobdatetime),
                     body = paste0("<html><body><p>", calledFun, " Finished at: ", format(Sys.time(), "%Y-%m-%d %X %Z"),"</p></body></html>"),
                     html = TRUE,
                     smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                     authenticate = TRUE,
                     send = TRUE)
  }
  # Return EDC_input_File dataframe for drake ------
  return(EDC_input_File)
 },

 # Error Functions -------------------------------------------------------------------
 error = function(e){
   #Log that error was caught, print error, log detail error condition, and stop
   log4r::error(CentralLogger,paste0(studyIdVar,"|",calledFun,"|",calledFun,": Caught an error!"))
   log4r::error(CentralLogger, paste0(studyIdVar,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
   log4r::info(CentralLogger,paste0(studyIdVar,"|",calledFun,"|",calledFun,"- All done, quitting."))
   #SCL 2029_03_30 - EMAIL on Error
   if (is.null(develop.f)) {
     mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                      to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                      subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                      body = paste0("<html><body><p>StudyId: ", studyIdVar, " </p><p>Error DateTime: ",
                                    format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                      html = TRUE,
                      smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                      authenticate = TRUE,
                      send = TRUE)
   }
   stop(paste0("Failure in function :", calledFun))
 },

 # Warning Functions -----------------------------------------------------------------
 warning = function(w){
   # Log that warning was caught, print warning, log detail warning condition
   log4r::warn(CentralLogger,paste0(studyIdVar,"|",calledFun,"|",calledFun,": Caught a warning!"))
   log4r::warn(CentralLogger, paste0(studyIdVar,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
   invokeRestart("muffleWarning")
  }
 )
}
"""

        # Add mdrJoinCohortStudyDay function
        controller_code += """
# Add StudyDay, Cohort, Column Cleaning Function ------------------------------------------------------
#' @title mdrJoinCohortStudyDay
#' @description To each output, add cohort related, enrolled/treated related, study day/week for dates, remove not-needed columns for each output per config file
#'
#' @param studyId This is a character field, studyId
#' @param alldatasetsList A list of tibbles of crfs extracted from sas2rds
#' @param appendCol A list of columns to be append to each output
#' @param SubjectInfo This is the SubjectInfo dataframe
#' @param sourcetype Categories used for filter in config file
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#'
#' @return A list of tibbles of crfs and analysis output with cohort related, study day added, with columns cleaned
#'
#' @export mdrJoinCohortStudyDay
#'
#' @importFrom dplyr filter select distinct pull
#' @importFrom tibble tibble
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_tibble
#' @importFrom rlang .data
#'
#' @examples
#' \\dontrun{
#' mdrJoinCohortStudyDay(studyId=studyId,alldatasetsList = alldatasetsList,
#' appendCol=appendCol,SubjectInfo = SubjectInfo,sourcetype=sourcetype,
#' tempLogger=tempLogger)
#' }
mdrJoinCohortStudyDay = function(studyId,alldatasetsList,appendCol,SubjectInfo,sourcetype,tempLogger){

  calledFun <- "mdrJoinCohortStudyDay"
  crf_col_exclusion_config <- readr::read_csv("/usrfiles/spotfire/mdr_config/mdr_column_cleaning_config.csv")
  checkmate::assert_tibble(crf_col_exclusion_config)
  checkmate::assert_tibble(SubjectInfo, min.rows = 1, min.cols = 2)
  checkmate::assert_list(alldatasetsList)

  exact_exclusions <- crf_col_exclusion_config %>%
    dplyr::filter(.data$edc == "Rave" & .data$match_type == "exact" & .data$category != "date") %>%
    dplyr::pull(.data$match_value)

  grep_exclusions <- crf_col_exclusion_config %>%
    dplyr::filter(.data$edc == "Rave" & .data$match_type == "grep"& .data$category != "date") %>%
    dplyr::pull(.data$match_value)

  dates_studyday <- crf_col_exclusion_config %>%
    dplyr::filter(.data$edc == "Rave" & .data$match_type == "grep"& .data$category == "date") %>%
    dplyr::pull(.data$match_value)

  StudyDay_Cohort_added_tb <- purrr::map(.x = seq_along(alldatasetsList), .f = ~{
    if (length(alldatasetsList[.x][[1]])!=0) {#avoid null tibble
      crfname<- names(alldatasetsList[.x])
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",crfname," - process started."))
      #exclude columns that already in the table, for example cohort is already in enr, Treated already in ae
      #comparison is on label not variable name
      col_label <- GSDSUtilities::raiseLabels(alldatasetsList[[.x]], "label", isNullC = NA)
      col_to_add <- setdiff(appendCol, names(col_label))
      FirstDose_Cohort_tb <- SubjectInfo %>%
        dplyr::select("Subject name or identifier", col_to_add)
      if (crfname!="SubjectInfo") {
        # SubjectInfo already has col_to_add
        if ("Subject name or identifier" %in% colnames(alldatasetsList[[.x]])){
          # for joining tibble after raiselabel
          FirstDose_Cohort_df <- alldatasetsList[[.x]] %>%
            dplyr::left_join(FirstDose_Cohort_tb, by =c("Subject name or identifier")) %>%
            dplyr::mutate_if(purrr::is_character, list(~dplyr::na_if(., "")))
        }
        else if ("Subject Name or Identifier" %in% colnames(alldatasetsList[[.x]])) {
          # for subject label variation, for example, in ae_1_linkAECMColumn file
          FirstDose_Cohort_df <- alldatasetsList[[.x]] %>%
            dplyr::left_join(FirstDose_Cohort_tb, by =c("Subject Name or Identifier"="Subject name or identifier")) %>%
            dplyr::mutate_if(purrr::is_character, list(~dplyr::na_if(., "")))
        }
        else {
          # for joining tibble before raiselabel
          FirstDose_Cohort_df <- alldatasetsList[[.x]] %>%
            dplyr::left_join(FirstDose_Cohort_tb, by =c("Subject"="Subject name or identifier")) %>%
            dplyr::mutate_if(purrr::is_character, list(~dplyr::na_if(., "")))
        }
      }
      else {

        FirstDose_Cohort_df <- alldatasetsList[[.x]]
      }
      return_tb <- FirstDose_Cohort_df
      for(i in 1:ncol(FirstDose_Cohort_df))
      {
        if (((any(dates_studyday %>% purrr::map(function(x) grepl(x, colnames(FirstDose_Cohort_df)[i]))==TRUE)) &
             ((sapply(FirstDose_Cohort_df[i],class)[1]== "Date"
               |sapply(FirstDose_Cohort_df[i],class)[1]== "POSIXct")))
            |(crfname=="lab"  & (colnames(FirstDose_Cohort_df)[i]=="RecordDate"
                                 |colnames(FirstDose_Cohort_df)[i]=="Clinical date of record (ex: visit date)")))
        {

          if (is.null(attr(FirstDose_Cohort_df[[i]], "label"))) {
            studyday_column <- paste0(colnames(FirstDose_Cohort_df)[i],"_StudyDay")
          } else {
            studyday_column <- paste0(attr(FirstDose_Cohort_df[[i]], "label"),"_StudyDay")
          }
          studyweek_column<-stringr::str_replace(studyday_column, "_StudyDay", "_StudyWeek")
          return_tb <- return_tb %>%
            dplyr::mutate(!!studyday_column :=
                            ifelse(
                              !is.na(.data[[colnames(FirstDose_Cohort_df)[i]]]) &
                                !is.na(.data$FirstDoseDate),
                              as.integer(difftime(.data[[colnames(FirstDose_Cohort_df)[i]]],
                                                  .data$FirstDoseDate,
                                                  units = "days")),
                              NA_integer_)) %>%
            dplyr::mutate(!!studyweek_column :=
                            ifelse(
                              !is.na(.data[[colnames(FirstDose_Cohort_df)[i]]]) &
                                !is.na(.data$FirstDoseDate),
                              as.integer(difftime(.data[[colnames(FirstDose_Cohort_df)[i]]],
                                                  .data$FirstDoseDate,
                                                  units = "weeks")),
                              NA_integer_))
        }
        else if ( colnames(FirstDose_Cohort_df)[i] %in%  exact_exclusions)
        {
          return_tb[colnames(FirstDose_Cohort_df)[i]]<- NULL
        }
        else if (any(grep_exclusions %>% purrr::map(function(x) grepl(x, colnames(FirstDose_Cohort_df)[i]))==TRUE))
        {
          if ( !grepl("\\\\sDay$", colnames(FirstDose_Cohort_df)[i]) & crfname !="PatientProfile") # not remove 'EOT Day' and 'EOS Day'
          {
            if ( !(grepl("_STD", colnames(FirstDose_Cohort_df)[i]) & grepl("^vs[1-9]*", crfname))) # not remove '_STD' from Vitals
            {
              return_tb[colnames(FirstDose_Cohort_df)[i]]<- NULL
            }
          }
        }
      }

      if(crfname!="SubjectInfo")
      {
        return_tb <-return_tb %>%  dplyr::select(-.data$FirstDoseDate)
        log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",crfname," - process ended."))
      }

      if (sourcetype=="analysis" & crfname %in% c("ae","cm","waterfall_spider_tb","tl_tb","rs_tb")){
        #ae,cm etc not gone through raiseLabels yet
        return_tb <- GSDSUtilities::raiseLabels(return_tb, "label", isNullC = NA)
      }
      else {
        return_tb<-return_tb
      }

    }
    else
    {
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"| ", names(alldatasetsList[.x]),"- has no data"))
    }
  }) %>% purrr::set_names(nm = names(alldatasetsList))
  # exclude null tibble from the output
  StudyDay_Cohort_added_tb <- StudyDay_Cohort_added_tb[which(!sapply(StudyDay_Cohort_added_tb, is.null))]
  return(StudyDay_Cohort_added_tb)
}
"""

        return controller_code
