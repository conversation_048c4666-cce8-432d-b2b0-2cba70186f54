#' @title doseMerge_b_bgb_b3227_101
#' @description Merge all dosing crfs together
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param drug1 This dosing crf #1 for BGB-B3227
#' @param drug2 This dosing crf #2 for Tislelizumab
#' @param drug3 This dosing crf #3 for Oxaliplatin
#' @param drug4 This dosing crf #4 for Capecitabine
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return dose_merged
#'
#' @export doseMerge_b_bgb_b3227_101
#'
#' @importFrom dplyr mutate select bind_rows case_when
#' @importFrom tidyr unite
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#'doseMerge_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
#'                         tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                         drug1 = ec_inf , drug2 = ec_inf2, drug3 = ec_inf3, drug4 = ec_or,
#'                         develop.f = develop.f, vpath = vpath)
#' }
#'
#'
#'
doseMerge_b_bgb_b3227_101 <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime,
                                      drug1, drug2, drug3,drug4, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "doseMerge_b_bgb_b3227_101"
    # Start Function --------------------------------------------------------------------
    log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

    # Assertions -----------------------------------------------------------------------
    # Assert sourceLocation directory exists
    checkmate::assert_directory_exists(sourceLocation)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))
    # Assert drug1 has min.rows and min.cols
    checkmate::assert_data_frame(drug1, min.rows = 0, min.cols = 2)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm drug1 has min.rows and min.cols."))
    # Assert drug2 has min.rows and min.cols
    checkmate::assert_data_frame(drug2, min.rows = 0, min.cols = 2)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm drug2 has min.rows and min.cols."))
    # Assert drug3 has min.rows and min.cols
    checkmate::assert_data_frame(drug3, min.rows = 0, min.cols = 2)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm drug3 has min.rows and min.cols."))
    # Assert drug4 has min.rows and min.cols
    checkmate::assert_data_frame(drug4, min.rows = 0, min.cols = 2)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm drug4 has min.rows and min.cols."))

    # Dose Data Table #1 ----
    # Add CRF Origin and Treatment names
    # UpdateVars
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dose1 dataframe"))
    dose1 <- drug1 %>%
      dplyr::mutate(Treatment = "BGB-B3227", `CRF Origin` = "ec_inf", `Dose Units` = "mg") %>%
      dplyr::mutate(PERF_ECDOSE = as.character(.data$PERF_ECDOSE),
                    `Planned Dose` = dplyr::if_else(.data$SCHE_ECDOSE=="Other",as.character(.data$SCHE_ECDOSEO),as.character(.data$SCHE_ECDOSE))) %>%
      dplyr::select(`Subject name or identifier` = .data$Subject,
                    `Dose Start Date (Interpolated)` = .data$ECSTDAT_INT,
                    `Dose End Date (Interpolated)` = .data$ECENDAT_INT,
                    .data$`Planned Dose`,
                    `Actual Dose` = .data$PERF_ECDOSE,
                    `Cycle Number` = .data$Folder,
                    .data$Treatment,
                    .data$`CRF Origin`,
                    .data$`Dose Units`,
                    `Was Study Drug administered?` = .data$ECYN_STD, #Make this yes if acutal dose >0 &
                    #`Reason for not administered` = .data$SDA12NRE,
                    #`Specify Other for Reason not administered`= .data$SDA12NOT,
                     `Was Dose Modified?` = .data$ECPMOD_STD,
                     `Reason Dose Modified` = .data$ECADJ,
                    `Specify Other for Reason Dose Modified` = .data$ECADJOTH,
                    # `Was Dose Missed?` = .data$EXREAS1_STD,
                     `Reason Dose Missed` = .data$SDADMRES,
                     `Specify Other for Reason Dose Missed`=.data$SDADMROT,
                    `Was Dose delayed since the last dose?`= .data$SDADODEL_STD,
                    `Reason Dose delayed`= 	.data$SDADDRES,
                    `Specify Other for Reason Dose delayed` = .data$SDADDOT,
                    #`Was Dose interrupted?`= .data$SDA12INR_STD,
                    #`Reason Dose interrupted`= .data$SDA12IRE,
                    #`Specify Other for Reason Dose interrupted` = .data$SDA12IOS,
                    #`Was Dose Discontinued?`= .data$SDA12DIS_STD,
                    #`Reason Dose Discontinued`= .data$SDA12DIR,
                    #`Specify Other for Reason Dose Discontinued` = .data$SDA12DIO,
                    .data$RecordId,
                    .data$LastChangeDate,
                    .data$WhatChanged
                    #  `Was Dose Decreased?`,
                    #  `Reason Dose Decreased`
                    #`Specify Other for Reason Dose Decreased`
                    ) %>%
        dplyr::mutate(
                      `Reason for not administered` = NA_character_,
                      `Specify Other for Reason not administered` = NA_character_,
                      #`Was Dose Modified?`= NA_character_,
                      #`Reason Dose Modified`= NA_character_,
                      #`Specify Other for Reason Dose Modified`= NA_character_,
                      `Was Dose Missed?` = NA_character_,
                      #`Reason Dose Missed` = NA_character_,
                      #`Specify Other for Reason Dose Missed`= NA_character_,
                      #`Was Dose delayed since the last dose?` = NA_character_,
                      #`Reason Dose delayed ` = NA_character_,
                      #`Specify Other for Reason Dose delayed`= NA_character_,
                      `Was Dose interrupted?` = NA_character_,
                      `Reason Dose interrupted` = NA_character_,
                      `Specify Other for Reason Dose interrupted`  = NA_character_,
                      `Was Dose Discontinued?` = NA_character_,
                      `Reason Dose Discontinued` = NA_character_,
                      `Specify Other for Reason Dose Discontinued` = NA_character_,
                      `Was Dose Decreased?` = NA_character_,
                      `Reason Dose Decreased` = NA_character_,
                      `Specify Other for Reason Dose Decreased`  = NA_character_)

    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Done creating dose1 dataframe"))

    # Dose Data Table #2----
    # Add CRF Origin and Treatment names
    # UpdateVars

    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dose2 dataframe"))
    dose2 <- drug2 %>%
      dplyr::mutate(Treatment = "Tislelizumab", `CRF Origin` = "ec_inf2", `Dose Units` = "mg") %>%
      dplyr::mutate(SCHE_ECDOSE2 = as.character(.data$SCHE_ECDOSE2), 	PERF_ECDOSE2 = as.character(.data$PERF_ECDOSE2)) %>%
      dplyr::select(`Subject name or identifier` = .data$Subject,
                    `Dose Start Date (Interpolated)` = .data$ECSTDAT2_INT,
                    `Dose End Date (Interpolated)` = 	.data$ECENDAT2_INT,
                    `Planned Dose` = .data$SCHE_ECDOSE2,
                    `Actual Dose` = .data$PERF_ECDOSE2,
                    `Cycle Number` = .data$Folder,
                    .data$Treatment,
                    .data$`CRF Origin` ,
                    .data$`Dose Units` ,
                    `Was Study Drug administered?` = 	.data$ECYN2_STD,
                    #`Reason for not administered` = .data$SDANREAS,
                    #`Specify Other for Reason not administered` = .data$SDANOTH,
                    ####################
                    # `Was Dose Modified?` = SDAID_STD,
                    #  `Reason Dose Modified` = SDARRPD,
                    # `Specify Other for Reason Dose Modified`
                    #  `Was Dose Missed?` = EXREAS1_STD,
                    `Reason Dose Missed` = .data$SDADMRES2,
                    `Specify Other for Reason Dose Missed`= .data$SDADMROT2,
                    `Was Dose delayed since the last dose?`= .data$SDADODEL2_STD,
                    `Reason Dose delayed`= .data$SDADDRES2,
                    `Specify Other for Reason Dose delayed` = .data$SDADDOT2,
                    `Was Dose interrupted?`= .data$SDAINRP2_STD,
                    `Reason Dose interrupted`= .data$SDAIRES2,
                    `Specify Other for Reason Dose interrupted` = .data$SDAIOSPY2,
                    # `Was Dose Discontinued?`= .data$SDADISC_STD,
                    # `Reason Dose Discontinued`= .data$SDAYSPY,
                    # `Specify Other for Reason Dose Discontinued` = .data$SDAOSPY,
                    .data$RecordId,
                    .data$LastChangeDate,
                    .data$WhatChanged
                    ) %>%
      dplyr::mutate(
        `Reason for not administered` = NA_character_,
        `Specify Other for Reason not administered` = NA_character_,
        `Was Dose Modified?`= NA_character_,
        `Reason Dose Modified`= NA_character_,
        `Specify Other for Reason Dose Modified`= NA_character_,
        `Was Dose Missed?` = NA_character_,
        # `Reason Dose Missed` = NA_character_,
        # `Specify Other for Reason Dose Missed`= NA_character_,
        #`Was Dose delayed since the last dose?` = NA_character_,
        #`Reason Dose delayed` = NA_character_,
        #`Specify Other for Reason Dose delayed`= NA_character_,
        #`Was Dose interrupted?` = NA_character_,
        #`Reason Dose interrupted` = NA_character_,
        #`Specify Other for Reason Dose interrupted`  = NA_character_,
        `Was Dose Discontinued?` = NA_character_,
        `Reason Dose Discontinued` = NA_character_,
        `Specify Other for Reason Dose Discontinued` = NA_character_,
        `Was Dose Decreased?` = NA_character_,
        `Reason Dose Decreased` = NA_character_,
        `Specify Other for Reason Dose Decreased`  = NA_character_)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Done creating dose2 dataframe"))


    # Dose Data Table #3----
    # Add CRF Origin and Treatment names
    # UpdateVars

    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dose3 dataframe"))
    dose3 <- drug3 %>%
      dplyr::mutate(Treatment = "Oxaliplatin", `CRF Origin` = "ec_inf3", `Dose Units` = "mg") %>%
      dplyr::mutate(`Planned Dose` = dplyr::if_else(.data$SCHE_ECDOSE3=="Other",as.character(.data$SCHE_ECDOSEO3),as.character(.data$SCHE_ECDOSE3)),
                    PERF_ECDOSE3 = as.character(.data$PERF_ECDOSE3)) %>%
      dplyr::select(`Subject name or identifier` = .data$Subject,
                    `Dose Start Date (Interpolated)` = .data$ECSTDAT3_INT,
                    `Dose End Date (Interpolated)` = 	.data$ECENDAT3_INT,
                    .data$`Planned Dose`,
                    `Actual Dose` = .data$PERF_ECDOSE3,
                    `Cycle Number` = .data$Folder,
                    .data$Treatment,
                    .data$`CRF Origin` ,
                    .data$`Dose Units` ,
                    `Was Study Drug administered?` = 	.data$ECYN3_STD,
                    #`Reason for not administered` = .data$SDANREAS,
                    #`Specify Other for Reason not administered` = .data$SDANOTH,
                    ####################
                    `Was Dose Modified?` = .data$ECPMOD3_STD,
                     `Reason Dose Modified` = .data$ECADJ3,
                    `Specify Other for Reason Dose Modified` = .data$ECADJOTH3,
                    #  `Was Dose Missed?` = EXREAS1_STD,
                    `Reason Dose Missed` = .data$SDADMRES3,
                    `Specify Other for Reason Dose Missed`= .data$SDADMROT3,
                    `Was Dose delayed since the last dose?`= .data$SDADODEL3_STD,
                    `Reason Dose delayed`= .data$SDADDRES3,
                    `Specify Other for Reason Dose delayed` = .data$SDADDOT3,
                    `Was Dose interrupted?`= .data$SDAINRP3_STD,
                    `Reason Dose interrupted`= .data$SDAIRES3,
                    `Specify Other for Reason Dose interrupted` = .data$SDAIOSPY3,
                    # `Was Dose Discontinued?`= .data$SDADISC_STD,
                    # `Reason Dose Discontinued`= .data$SDAYSPY,
                    # `Specify Other for Reason Dose Discontinued` = .data$SDAOSPY,
                    .data$RecordId,
                    .data$LastChangeDate,
                    .data$WhatChanged
      ) %>%
      dplyr::mutate(
        `Reason for not administered` = NA_character_,
        `Specify Other for Reason not administered` = NA_character_,
        # `Was Dose Modified?`= NA_character_,
        # `Reason Dose Modified`= NA_character_,
        # `Specify Other for Reason Dose Modified`= NA_character_,
        `Was Dose Missed?` = NA_character_,
        # `Reason Dose Missed` = NA_character_,
        # `Specify Other for Reason Dose Missed`= NA_character_,
        #`Was Dose delayed since the last dose?` = NA_character_,
        #`Reason Dose delayed` = NA_character_,
        #`Specify Other for Reason Dose delayed`= NA_character_,
        #`Was Dose interrupted?` = NA_character_,
        #`Reason Dose interrupted` = NA_character_,
        #`Specify Other for Reason Dose interrupted`  = NA_character_,
        `Was Dose Discontinued?` = NA_character_,
        `Reason Dose Discontinued` = NA_character_,
        `Specify Other for Reason Dose Discontinued` = NA_character_,
        `Was Dose Decreased?` = NA_character_,
        `Reason Dose Decreased` = NA_character_,
        `Specify Other for Reason Dose Decreased`  = NA_character_)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Done creating dose3 dataframe"))


    # Dose Data Table #4----
    # Add CRF Origin and Treatment names
    # UpdateVars

    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dose4 dataframe"))
    dose4 <- drug4 %>%
      dplyr::mutate(Treatment = "Capecitabine", `CRF Origin` = "ec_or", `Dose Units` = "mg") %>%
      dplyr::mutate(`Planned Dose`= dplyr::if_else(.data$SCHE_ECDOSE1!="0",
                                                   paste0(dplyr::if_else(.data$SCHE_ECDOSE1=="Other",as.character(.data$SCHE_ECDOSEO1),.data$SCHE_ECDOSE1),.data$SCHE_ECDOSFRQ1),.data$SCHE_ECDOSE1),
                    `Actual Dose` = dplyr::if_else(.data$PERF_ECDOSE1!=0,
                                                   paste0(.data$PERF_ECDOSE1,dplyr::if_else(.data$PERF_ECDOSFRQ1=="Other",.data$PERF_ECDOSFRQO1,.data$PERF_ECDOSFRQ1)), as.character(.data$PERF_ECDOSE1)),
                    `Was Dose Missed?`= dplyr::if_else(.data$EXREAS1=="Missed dose","Yes","No")) %>%
      dplyr::select(`Subject name or identifier` = .data$Subject,
                    `Dose Start Date (Interpolated)` = .data$ECSTDAT1_INT,
                    `Dose End Date (Interpolated)` = 	.data$ECENDAT1_INT,
                    .data$`Planned Dose`,
                    .data$`Actual Dose`,
                    `Cycle Number` = .data$Folder,
                    .data$Treatment,
                    .data$`CRF Origin` ,
                    .data$`Dose Units` ,
                    `Was Study Drug administered?` = 	.data$ECYN1_STD,
                    #`Reason for not administered` = .data$SDANREAS,
                    #`Specify Other for Reason not administered` = .data$SDANOTH,
                    ####################
                    `Was Dose Modified?` = .data$EXID1_STD,
                    `Reason Dose Modified` = .data$ECADJ1,
                    `Specify Other for Reason Dose Modified` = .data$EXRPDOTH1,
                    .data$`Was Dose Missed?`,
                    # `Reason Dose Missed` = .data$SDADMRES3,
                    # `Specify Other for Reason Dose Missed`= .data$SDADMROT3,
                    # `Was Dose delayed since the last dose?`= .data$SDADODEL3_STD,
                    # `Reason Dose delayed`= .data$SDADDRES3,
                    # `Specify Other for Reason Dose delayed` = .data$SDADDOT3,
                    # `Was Dose interrupted?`= .data$SDAINRP3_STD,
                    # `Reason Dose interrupted`= .data$SDAIRES3,
                    # `Specify Other for Reason Dose interrupted` = .data$SDAIOSPY3,
                    # `Was Dose Discontinued?`= .data$SDADISC_STD,
                    # `Reason Dose Discontinued`= .data$SDAYSPY,
                    # `Specify Other for Reason Dose Discontinued` = .data$SDAOSPY,
                    .data$RecordId,
                    .data$LastChangeDate,
                    .data$WhatChanged
      ) %>%
      dplyr::mutate(
        `Reason for not administered` = NA_character_,
        `Specify Other for Reason not administered` = NA_character_,
        # `Was Dose Modified?`= NA_character_,
        # `Reason Dose Modified`= NA_character_,
        # `Specify Other for Reason Dose Modified`= NA_character_,
        # `Was Dose Missed?` = NA_character_,
        `Reason Dose Missed` = NA_character_,
        `Specify Other for Reason Dose Missed`= NA_character_,
        `Was Dose delayed since the last dose?` = NA_character_,
        `Reason Dose delayed` = NA_character_,
        `Specify Other for Reason Dose delayed`= NA_character_,
        `Was Dose interrupted?` = NA_character_,
        `Reason Dose interrupted` = NA_character_,
        `Specify Other for Reason Dose interrupted`  = NA_character_,
        `Was Dose Discontinued?` = NA_character_,
        `Reason Dose Discontinued` = NA_character_,
        `Specify Other for Reason Dose Discontinued` = NA_character_,
        `Was Dose Decreased?` = NA_character_,
        `Reason Dose Decreased` = NA_character_,
        `Specify Other for Reason Dose Decreased`  = NA_character_)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Done creating dose4 dataframe"))

    # Bind Doses together ---------------------------------------------------------------
      dose_merged <- dplyr::bind_rows(dose1,dose2,dose3,dose4) %>%
      dplyr::select(.data$Treatment,
                    .data$`CRF Origin`,
                    .data$`Subject name or identifier`,
                    .data$`Cycle Number`,
                    .data$`Dose Start Date (Interpolated)`,
                    .data$`Dose End Date (Interpolated)`,
                    .data$`Was Study Drug administered?`,
                    .data$`Reason for not administered` ,
                    .data$`Specify Other for Reason not administered`,
                    .data$`Planned Dose`,
                    .data$`Actual Dose`,
                    .data$`Dose Units`,
                    .data$`Was Dose Modified?`,
                    .data$`Reason Dose Modified`,
                    .data$`Specify Other for Reason Dose Modified`,
                    .data$`Was Dose Missed?`,
                    .data$`Reason Dose Missed`,
                    .data$`Specify Other for Reason Dose Missed`,
                    .data$`Was Dose delayed since the last dose?`,
                    .data$`Reason Dose delayed`,
                    .data$`Specify Other for Reason Dose delayed`,
                    .data$`Was Dose interrupted?`,
                    .data$`Reason Dose interrupted`,
                    .data$`Specify Other for Reason Dose interrupted`,
                    .data$`Was Dose Discontinued?`,
                    .data$`Reason Dose Discontinued`,
                    .data$`Specify Other for Reason Dose Discontinued` ,
                    .data$`Was Dose Decreased?`,
                    .data$`Reason Dose Decreased`,
                    .data$`Specify Other for Reason Dose Decreased`,
                    .data$RecordId,
                    .data$LastChangeDate,
                    .data$WhatChanged ) %>%
      dplyr::mutate(`Was Dose Interrupted/delayed/withheld?` = dplyr::case_when(
        substring(.data$`Was Dose interrupted?`,1,1)=="Y"| substring(.data$`Was Dose delayed since the last dose?`,1,1)=="Y" ~"Y",
        substring(.data$`Was Dose interrupted?`,1,1)=="N"| substring(.data$`Was Dose delayed since the last dose?`,1,1)=="N" ~"N",
        TRUE ~ NA_character_)) %>%
      dplyr::mutate(`Was Dose Modified/reduced/decreased?` = dplyr::case_when(
        substring(.data$`Was Dose Modified?`,1,1)=="Y"| substring(.data$`Was Dose Decreased?`,1,1)=="Y" ~"Y",
        substring(.data$`Was Dose Modified?`,1,1)=="N"| substring(.data$`Was Dose Decreased?`,1,1)=="N" ~"N",
        TRUE ~ NA_character_)) %>%
      tidyr::unite(col = "Reason Dose Interrupted/delayed/withheld", c("Reason Dose interrupted", "Reason Dose delayed"), na.rm = TRUE, sep = ",", remove = FALSE) %>%
      tidyr::unite(col = "Reason Dose Modified/reduced/decreased", c("Reason Dose Modified", "Reason Dose Decreased"), na.rm = TRUE, sep = ",", remove = FALSE) %>%
      tidyr::unite(col = "Specify Other Reason Dose Interrupted/delayed/withheld" , c("Specify Other for Reason Dose interrupted", "Specify Other for Reason Dose delayed"), na.rm = TRUE, sep = ",", remove = FALSE) %>%
      tidyr::unite(col = "Specify Other Reason Dose Modified/reduced/decreased", c("Specify Other for Reason Dose Modified", "Specify Other for Reason Dose Decreased"), na.rm = TRUE, sep = ",", remove = FALSE) %>%
      dplyr::select(-.data$`Specify Other Reason Dose Modified/reduced/decreased`,
                    -.data$`Reason Dose Modified/reduced/decreased`,
                    -.data$`Reason Dose Interrupted/delayed/withheld` ,
                    -.data$`Specify Other Reason Dose Interrupted/delayed/withheld`,
                    tidyselect::everything())


    checkmate::assert_data_frame(dose_merged,min.rows = 1)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Bind doses together"))

    # Assign dose_merged to calling envir ----------------------------------------------------
    assign("dose_merged", dose_merged, envir = parent.frame())
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," dose_merged returned"))

    # End of doseMerge Function ----------------------------------------------------
    log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
