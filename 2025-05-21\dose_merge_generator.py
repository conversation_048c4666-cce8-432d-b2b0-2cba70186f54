import pandas as pd
import re
from typing import Dict, List, Tuple

class DoseMergeGenerator:
    def __init__(self, drug_merge_info_df):
        """Initialize with drug merge info DataFrame."""
        self.drug_info_df = drug_merge_info_df
        self.validate_drug_info()
    
    def validate_drug_info(self) -> None:
        """Validate the drug merge info file structure."""
        required_columns = [
           'Form',
            'drug_number',
            'dose_number',
            'Treatment',
            'FieldOID',
           'label_doseMerged'
       ]
        missing_cols = [col for col in required_columns if col not in self.drug_info_df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns in drug merge info file: {', '.join(missing_cols)}")
    
    def generate_function_header(self, study_id: str) -> str:
        """Generate the function header with documentation."""
        # Get unique drug numbers and their treatments
        unique_drugs = self.drug_info_df[['drug_number', 'Treatment', 'Form']].drop_duplicates()
        drug_params = '\n'.join([
            f"#' @param {row['drug_number']} This dosing crf #{i+1} for {row['Treatment']}"
            for i, row in unique_drugs.iterrows()
        ])
        
        # Create example parameters with safe form handling
        example_params = []
        for _, row in unique_drugs.iterrows():
            form = row['Form'].lower() if pd.notna(row['Form']) else row['drug_number'].lower()
            example_params.append(f"{row['drug_number']} = {form}")
        
        return f'''#' @title doseMerge_{study_id}
#' @description Merge all dosing crfs together
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
{drug_params}
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return dose_merged
#'
#' @export doseMerge_{study_id}
#'
#' @importFrom dplyr mutate select bind_rows case_when
#' @importFrom tidyr unite
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \\dontrun{{
#' doseMerge_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
#'                      tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                      {", ".join(example_params)},
#'                      develop.f = develop.f, vpath = vpath)
#' }}
#'
#'
#'
'''
    
    def generate_function_start(self, study_id: str) -> str:
        """Generate the function start with parameter list."""
        unique_drugs = self.drug_info_df['drug_number'].unique()
        drug_params = ', '.join(unique_drugs)
        return f'''doseMerge_{study_id} <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime,
                                      {drug_params}, develop.f = develop.f, vpath = vpath) {{
  withCallingHandlers(
    expr = {{
      calledFun = "doseMerge_{study_id}"
    # Start Function --------------------------------------------------------------------
    log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

    # Assertions -----------------------------------------------------------------------
    # Assert sourceLocation directory exists
    checkmate::assert_directory_exists(sourceLocation)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))'''
    
    def generate_dose_processing(self) -> str:
        """Generate dose processing code for each drug table."""
        processing_code = []
        unique_drugs = self.drug_info_df[['dose_number','drug_number', 'Treatment', 'Form']].drop_duplicates()
        
        for idx, drug in unique_drugs.iterrows():
            # Get variables for this drug
            drug_vars = self.drug_info_df[
                self.drug_info_df['drug_number'] == drug['drug_number']
            ]
            
            # Add assertions for each drug
            code = [
                f"\n    # Assert {drug['drug_number']} has min.rows and min.cols",
                f"    checkmate::assert_data_frame({drug['drug_number']}, min.rows = 0, min.cols = 2)",
                f'    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {drug["drug_number"]} has min.rows and min.cols."))',
                f"\n    # Dose Data Table #{idx+1} ----",
                f'    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating {drug["drug_number"]} dataframe"))',
                f"    {drug['dose_number']} <- {drug['drug_number']} %>%",
                f'      dplyr::mutate(Treatment = "{drug["Treatment"]}", `CRF Origin` = "{drug["Form"]}", `Dose Units` = " ") %>%'
            ]
            
            # Generate transformations for dose fields
            planned_dose_field = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Planned Dose', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Planned Dose', case=False, na=False)].empty else None
            
            actual_dose_field = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Actual Dose', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Actual Dose', case=False, na=False)].empty else None
            
            # Check for other dose fields
            other_planned_dose = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Other planned dose', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Other planned dose', case=False, na=False)].empty else None
            
            other_actual_dose = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Other actual dose', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Other actual dose', case=False, na=False)].empty else None
            
            # Check for frequency fields
            planned_freq = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Planned Frequency', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Planned Frequency', case=False, na=False)].empty else None
            
            actual_freq = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Actual Frequency', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Actual Frequency', case=False, na=False)].empty else None
            
            # Check for other frequency fields
            other_planned_freq = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Other planned frequency', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Other planned frequency', case=False, na=False)].empty else None
            
            other_actual_freq = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Other actual frequency', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Other actual frequency', case=False, na=False)].empty else None
            
            # Check for dose error field
            dose_error_field = drug_vars[
                drug_vars['label_doseMerged'].str.contains('Was Dose Missed?', case=False, na=False)
            ]['FieldOID'].iloc[0] if not drug_vars[drug_vars['label_doseMerged'].str.contains('Was Dose Missed?', case=False, na=False)].empty else None
            
            # Generate dose transformations
            transforms = []
            if planned_dose_field:
                if other_planned_dose and other_planned_dose != "NA_character_":
                    if planned_freq and planned_freq != "NA_character_":
                        if other_planned_freq and other_planned_freq != "NA_character_":
                            transforms.append(f"`Planned Dose` = dplyr::if_else(.data${planned_dose_field}!=\"0\", paste0(dplyr::if_else(.data${planned_dose_field}==\"Other\",as.character(.data${other_planned_dose}),.data${planned_dose_field}),dplyr::if_else(.data${planned_freq}==\"Other\",as.character(.data${other_planned_freq}),.data${planned_freq})),.data${planned_dose_field})")
                        else:
                            transforms.append(f"`Planned Dose` = dplyr::if_else(.data${planned_dose_field}!=\"0\", paste0(dplyr::if_else(.data${planned_dose_field}==\"Other\",as.character(.data${other_planned_dose}),.data${planned_dose_field}),.data${planned_freq}),.data${planned_dose_field})")
                    else:
                        transforms.append(f"`Planned Dose` = dplyr::if_else(.data${planned_dose_field}==\"Other\",as.character(.data${other_planned_dose}),as.character(.data${planned_dose_field}))")
                else:
                    if planned_freq and planned_freq != "NA_character_":
                        if other_planned_freq and other_planned_freq != "NA_character_":
                            transforms.append(f"`Planned Dose` = dplyr::if_else(.data${planned_dose_field}!=\"0\", paste0(.data${planned_dose_field},dplyr::if_else(.data${planned_freq}==\"Other\",as.character(.data${other_planned_freq}),.data${planned_freq})),.data${planned_dose_field})")
                        else:
                            transforms.append(f"`Planned Dose` = dplyr::if_else(.data${planned_dose_field}!=\"0\", paste0(.data${planned_dose_field},.data${planned_freq}),.data${planned_dose_field})")
                    else:
                        transforms.append(f"`Planned Dose` = as.character(.data${planned_dose_field})")
            
            if actual_dose_field:
                if other_actual_dose and other_actual_dose != "NA_character_":
                    if actual_freq and actual_freq != "NA_character_":
                        if other_actual_freq and other_actual_freq != "NA_character_":
                            transforms.append(f"`Actual Dose` = dplyr::if_else(.data${actual_dose_field}!=\"0\", paste0(dplyr::if_else(.data${actual_dose_field}==\"Other\",as.character(.data${other_actual_dose}),.data${actual_dose_field}),dplyr::if_else(.data${actual_freq}==\"Other\",as.character(.data${other_actual_freq}),.data${actual_freq})),.data${actual_dose_field})")
                        else:
                            transforms.append(f"`Actual Dose` = dplyr::if_else(.data${actual_dose_field}!=\"0\", paste0(dplyr::if_else(.data${actual_dose_field}==\"Other\",as.character(.data${other_actual_dose}),.data${actual_dose_field}),.data${actual_freq}),.data${actual_dose_field})")
                    else:
                        transforms.append(f"`Actual Dose` = dplyr::if_else(.data${actual_dose_field}==\"Other\",as.character(.data${other_actual_dose}),as.character(.data${actual_dose_field}))")
                else:
                    if actual_freq and actual_freq != "NA_character_":
                        if other_actual_freq and other_actual_freq != "NA_character_":
                            transforms.append(f"`Actual Dose` = dplyr::if_else(.data${actual_dose_field}!=\"0\", paste0(.data${actual_dose_field},dplyr::if_else(.data${actual_freq}==\"Other\",as.character(.data${other_actual_freq}),.data${actual_freq})),.data${actual_dose_field})")
                        else:
                            transforms.append(f"`Actual Dose` = dplyr::if_else(.data${actual_dose_field}!=\"0\", paste0(.data${actual_dose_field},.data${actual_freq}),.data${actual_dose_field})")
                    else:
                        transforms.append(f"`Actual Dose` = as.character(.data${actual_dose_field})")
            
            # Add dose error transformation only if field exists and is not NA_character_
            if dose_error_field and dose_error_field != "NA_character_":
                transforms.append(f"`Was Dose Missed?` = dplyr::if_else(.data${dose_error_field}==\"Missed dose\",\"Yes\",\"No\")")
            
            if transforms:
                code.append("      dplyr::mutate(\n        " + ",\n        ".join(transforms) + "\n      ) %>%")
            
            # Generate select statement with all fields
            select_vars = []
            mutate_vars = []
            for _, row in drug_vars.iterrows():
                if pd.notna(row['label_doseMerged']):
                    if row['label_doseMerged'] in ["Actual Frequency", "Planned Frequency", "Other acutual dose", 
                                                 "Other planned dose", "Other actual frequency", "Other planned frequency",
                                                 "Actual Dose", "Planned Dose"]:
                        continue
                    elif row['label_doseMerged'] in ["Dose Start Date (Interpolated)", "Dose End Date (Interpolated)"]:
                        select_vars.append(f"`{row['label_doseMerged']}` = .data${row['FieldOID']}_INT")
                    elif row['FieldOID'] == "NA_character_":
                        mutate_vars.append(f"`{row['label_doseMerged']}` = NA_character_")
                    elif row['label_doseMerged'] == "Was Dose Missed?":
                        if row['FieldOID'] == "NA_character_":
                            mutate_vars.append(f"`{row['label_doseMerged']}` = NA_character_")
                        else:
                            select_vars.append(".data$`Was Dose Missed?`")
                    else:
                        select_vars.append(f"`{row['label_doseMerged']}` = .data${row['FieldOID']}")
            
            # Add system fields
            select_vars.extend([
                ".data$Treatment",
                ".data$`CRF Origin`",
                ".data$`Dose Units`",
                ".data$`Planned Dose`",
                ".data$`Actual Dose`",
                ".data$RecordId",
                ".data$LastChangeDate",
                ".data$WhatChanged"
            ])
            code.append("      dplyr::select(\n        " + ",\n        ".join(select_vars) + "\n      )")
            
            if mutate_vars:
                code.append("      dplyr::mutate(\n        " + ",\n        ".join(mutate_vars) + "\n      ) %>%")
            
            
            
            code.append(f'    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Done creating {drug["drug_number"]} dataframe"))\n')
            
            processing_code.append("\n".join(code))
        
        return "\n".join(processing_code)
    
    def generate_merge_code(self) -> str:
        """Generate code to merge all dose tables."""
        # Get all unique drug numbers
        unique_drugs = self.drug_info_df['dose_number'].unique()
        
        merge_code = [
            "\n    # Bind Doses together ---------------------------------------------------------------",
            f"    dose_merged <- dplyr::bind_rows({', '.join(unique_drugs)}) %>%",
            "      dplyr::select(.data$Treatment,",
            "                    .data$`CRF Origin`,",
            "                    .data$`Subject name or identifier`,",
            "                    .data$`Cycle Number`,",
            "                    .data$`Dose Start Date (Interpolated)`,",
            "                    .data$`Dose End Date (Interpolated)`,",
            "                    .data$`Was Study Drug administered?`,",
            "                    .data$`Reason for not administered`,",
            "                    .data$`Specify Other for Reason not administered`,",
            "                    .data$`Planned Dose`,",
            "                    .data$`Actual Dose`,",
            "                    .data$`Dose Units`,",
            "                    .data$`Was Dose Modified?`,",
            "                    .data$`Reason Dose Modified`,",
            "                    .data$`Specify Other for Reason Dose Modified`,",
            "                    .data$`Was Dose Missed?`,",
            "                    .data$`Reason Dose Missed`,",
            "                    .data$`Specify Other for Reason Dose Missed`,",
            "                    .data$`Was Dose delayed since the last dose?`,",
            "                    .data$`Reason Dose delayed`,",
            "                    .data$`Specify Other for Reason Dose delayed`,",
            "                    .data$`Was Dose interrupted?`,",
            "                    .data$`Reason Dose interrupted`,",
            "                    .data$`Specify Other for Reason Dose interrupted`,",
            "                    .data$`Was Dose Discontinued?`,",
            "                    .data$`Reason Dose Discontinued`,",
            "                    .data$`Specify Other for Reason Dose Discontinued`,",
            "                    .data$`Was Dose Decreased?`,",
            "                    .data$`Reason Dose Decreased`,",
            "                    .data$`Specify Other for Reason Dose Decreased`,",
            "                    .data$RecordId,",
            "                    .data$LastChangeDate,",
            "                    .data$WhatChanged) %>%",
            "      dplyr::mutate(`Was Dose Interrupted/delayed/withheld?` = dplyr::case_when(",
            "        substring(.data$`Was Dose interrupted?`,1,1)==\"Y\"| substring(.data$`Was Dose delayed since the last dose?`,1,1)==\"Y\" ~\"Y\",",
            "        substring(.data$`Was Dose interrupted?`,1,1)==\"N\"| substring(.data$`Was Dose delayed since the last dose?`,1,1)==\"N\" ~\"N\",",
            "        TRUE ~ NA_character_)) %>%",
            "      dplyr::mutate(`Was Dose Modified/reduced/decreased?` = dplyr::case_when(",
            "        substring(.data$`Was Dose Modified?`,1,1)==\"Y\"| substring(.data$`Was Dose Decreased?`,1,1)==\"Y\" ~\"Y\",",
            "        substring(.data$`Was Dose Modified?`,1,1)==\"N\"| substring(.data$`Was Dose Decreased?`,1,1)==\"N\" ~\"N\",",
            "        TRUE ~ NA_character_)) %>%",
            "      tidyr::unite(col = \"Reason Dose Interrupted/delayed/withheld\", c(\"Reason Dose interrupted\", \"Reason Dose delayed\"), na.rm = TRUE, sep = \",\", remove = FALSE) %>%",
            "      tidyr::unite(col = \"Reason Dose Modified/reduced/decreased\", c(\"Reason Dose Modified\", \"Reason Dose Decreased\"), na.rm = TRUE, sep = \",\", remove = FALSE) %>%",
            "      tidyr::unite(col = \"Specify Other Reason Dose Interrupted/delayed/withheld\" , c(\"Specify Other for Reason Dose interrupted\", \"Specify Other for Reason Dose delayed\"), na.rm = TRUE, sep = \",\", remove = FALSE) %>%",
            "      tidyr::unite(col = \"Specify Other Reason Dose Modified/reduced/decreased\", c(\"Specify Other for Reason Dose Modified\", \"Specify Other for Reason Dose Decreased\"), na.rm = TRUE, sep = \",\", remove = FALSE) %>%",
            "      dplyr::select(-.data$`Specify Other Reason Dose Modified/reduced/decreased`,",
            "                    -.data$`Reason Dose Modified/reduced/decreased`,",
            "                    -.data$`Reason Dose Interrupted/delayed/withheld` ,",
            "                    -.data$`Specify Other Reason Dose Interrupted/delayed/withheld`,",
            "                    tidyselect::everything())",
            "",
            "    checkmate::assert_data_frame(dose_merged,min.rows = 1)",
            '    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Bind doses together"))',
            "",
            "    # Assign dose_merged to calling envir ----------------------------------------------------",
            '    assign("dose_merged", dose_merged, envir = parent.frame())',
            '    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," dose_merged returned"))',
            "",
            "    # End of doseMerge Function ----------------------------------------------------",
            '    log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))',
            "    },",
            "",
            "    error = function(e){",
            "      #Log that error was caught, print error, log detail error condition, and stop",
            '      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))',
            '      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))',
            '      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))',
            "      #SCL 2020_05_06 - EMAIL on Error",
            "      if (is.null(develop.f)) {",
            "        mailR::send.mail(from = secret::get_secret(\"mail_smtp\", key = secret::local_key(), vault = vpath)$user.name,",
            "                         to = secret::get_secret(\"mail_to\", key = secret::local_key(), vault = vpath),",
            "                         subject = paste0(\"MDR Transformation Function: \", calledFun, \" Failure at jobdatetime: \",jobdatetime),",
            "                         body = paste0(\"<html><body><p>StudyId: \", studyId, \" </p><p>Error DateTime: \",",
            "                                       format(Sys.time(), \"%Y-%m-%d %X %Z\"), \"</p><p>Error : \", conditionMessage(e),\"</p></body></html>\"),",
            "                         html = TRUE,",
            "                         smtp = secret::get_secret(\"mail_smtp\", key = secret::local_key(), vault = vpath),",
            "                         authenticate = TRUE,",
            "                         send = TRUE)",
            "      }",
            "      stop(paste0(\"Failure in function :\", calledFun))",
            "    },",
            "    warning = function(w){",
            "      # Log that warning was caught, print warning, log detail warning condition",
            '      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))',
            '      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))',
            "      invokeRestart(\"muffleWarning\")",
            "    }",
            "  )",
            "}"
        ]
        
        return "\n".join(merge_code)
    
    def generate_function(self, study_id: str) -> str:
        """Generate the complete R function by combining all parts."""
        # Generate each part of the function
        header = self.generate_function_header(study_id)
        start = self.generate_function_start(study_id)
        processing = self.generate_dose_processing()
        merge = self.generate_merge_code()
        
        # Combine all parts
        return f"{header}\n{start}\n{processing}\n{merge}"