# MDR R Package Generator - Electron Desktop App

This directory contains the Electron wrapper for the MDR R Package Generator, allowing users to install and run the application as a native desktop app.

## 🚀 Quick Start

### Prerequisites

- **Node.js** (version 16 or higher)
- **Python** (version 3.8 or higher)
- **npm** (comes with Node.js)

### Development Setup

1. **Install Node.js dependencies:**
   ```bash
   npm install
   ```

2. **Set up development environment:**
   ```bash
   npm run setup-dev
   ```

3. **Start development server:**
   ```bash
   npm run dev
   ```

### Building for Distribution

1. **Build for current platform:**
   ```bash
   npm run build
   ```

2. **Build for specific platforms:**
   ```bash
   npm run build-win    # Windows
   npm run build-mac    # macOS
   npm run build-linux  # Linux
   npm run build-all    # All platforms
   ```

## 📁 Project Structure

```
├── main.js                 # Electron main process
├── preload.js             # Secure communication bridge
├── start.py               # Python/Streamlit server starter
├── package.json           # Node.js project configuration
├── build-scripts/         # Build and setup scripts
│   ├── build.js          # Main build script
│   └── setup-dev.js      # Development setup
├── build-resources/       # App icons and resources
└── dist/                 # Built applications (generated)
```

## 🔧 Configuration

### App Information
- **App ID**: `com.yourorg.mdr-r-package-generator`
- **Product Name**: MDR R Package Generator
- **Version**: 1.0.0

### Build Targets
- **Windows**: NSIS installer + Portable executable
- **macOS**: DMG installer (x64 + ARM64)
- **Linux**: AppImage + DEB package

## 🎯 Features

### Desktop Integration
- ✅ Native window management
- ✅ System tray integration (optional)
- ✅ Auto-start Streamlit server
- ✅ Proper cleanup on exit
- ✅ Cross-platform support

### Security
- ✅ Context isolation enabled
- ✅ Node integration disabled
- ✅ Secure preload script
- ✅ External link handling

### User Experience
- ✅ Splash screen while loading
- ✅ Error handling and logging
- ✅ Custom styling for desktop
- ✅ Keyboard shortcuts
- ✅ Zoom controls

## 🛠 Development

### Available Scripts

| Script | Description |
|--------|-------------|
| `npm start` | Start the app in production mode |
| `npm run dev` | Start the app in development mode |
| `npm run build` | Build for current platform |
| `npm run setup-dev` | Set up development environment |
| `npm run clean` | Clean build artifacts |

### Development Mode Features
- Hot reload (restart required for main process changes)
- Developer tools access (Ctrl+Shift+I)
- Detailed logging
- Python virtual environment isolation

### Debugging
- **Main Process**: Use VS Code with Node.js debugging
- **Renderer Process**: Use Chrome DevTools (Ctrl+Shift+I)
- **Python/Streamlit**: Check `streamlit_server.log`

## 📦 Distribution

### Installer Creation
The build process creates installers for each platform:

**Windows:**
- `MDR R Package Generator Setup.exe` (NSIS installer)
- `MDR R Package Generator.exe` (Portable)

**macOS:**
- `MDR R Package Generator.dmg` (DMG installer)

**Linux:**
- `MDR R Package Generator.AppImage` (AppImage)
- `mdr-r-package-generator.deb` (DEB package)

### Installation Requirements
- **Windows**: Windows 10 or later
- **macOS**: macOS 10.14 or later
- **Linux**: Ubuntu 18.04+ or equivalent

## 🔍 Troubleshooting

### Common Issues

1. **Python not found:**
   - Ensure Python 3.8+ is installed and in PATH
   - Run `python --version` to verify

2. **Port already in use:**
   - The app automatically finds available ports
   - Check if other Streamlit instances are running

3. **Build fails:**
   - Run `npm run clean` and try again
   - Check Node.js and Python versions
   - Ensure all dependencies are installed

4. **App won't start:**
   - Check `streamlit_server.log` for Python errors
   - Verify all Python requirements are installed
   - Try running `npm run setup-dev`

### Logs Location
- **Electron logs**: Console output
- **Streamlit logs**: `streamlit_server.log`
- **Build logs**: Console output during build

## 🚀 Deployment

### For End Users
1. Download the installer for your platform
2. Run the installer
3. Launch "MDR R Package Generator" from your applications

### For Developers
1. Build the application: `npm run build`
2. Distribute the installer from the `dist/` directory
3. Users can install without needing Python or Node.js

## 📝 Customization

### App Icon
Replace files in `build-resources/`:
- `icon.ico` (Windows)
- `icon.icns` (macOS)
- `icon.png` (Linux)

### App Information
Edit `package.json`:
- Change `name`, `description`, `author`
- Update `build.appId` and `build.productName`

### Streamlit Configuration
Modify `start.py` to change:
- Server port range
- Streamlit configuration options
- Python environment setup

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test on multiple platforms
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
