import pandas as pd
import re
from pathlib import Path
import io

class TDMergeGenerator:
    def __init__(self, als_file):
        """Initialize the TDMergeGenerator with ALS file."""
        try:
            # Save the uploaded file temporarily
            with open("temp_als_file.xlsx", "wb") as f:
                f.write(als_file.getvalue())
            
            # Read ALS file sheets
            fields_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Fields")
            forms_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms")
            
            # Clean up temporary file
            Path("temp_als_file.xlsx").unlink()
            
            # Process fields
            fields_df = fields_df[['FormOID', 'FieldOID', 'SASLabel']].copy()
            fields_df['Form'] = fields_df['FormOID'].str.lower()
            fields_df = fields_df.drop('FormOID', axis=1).dropna()
            
            # Process forms
            forms_df = forms_df[['OID', 'DraftFormName']].copy()
            forms_df = forms_df[~forms_df['DraftFormName'].str.contains('_INACTIVE', na=False)]
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df.drop('OID', axis=1).dropna()
            
            # Join fields and forms
            self.td_info_df = fields_df.merge(forms_df, on='Form')
            self.td_info_df = self.td_info_df[
                self.td_info_df['DraftFormName'].str.contains('End of Treatment')
            ]
            
            # Extract treatment names
            self.td_info_df['Treatment'] = self.td_info_df['DraftFormName'].str.extract(r'.*-\s*(.*)')
            
            # Create a mapping dictionary for each treatment
            self.field_mappings = {}
            for treatment in self.td_info_df['Treatment'].unique():
                treatment_df = self.td_info_df[self.td_info_df['Treatment'] == treatment]
                
                # Get the form name (e.g., ds_eot1)
                form = treatment_df['Form'].iloc[0]
                
                # Find the date field
                date_field = treatment_df[
                    treatment_df['SASLabel'].str.contains('Date of last dose', case=False)
                ]['FieldOID'].iloc[0]
                
                # Find the primary reason field
                reason_field = treatment_df[
                    treatment_df['SASLabel'].str.contains('Indicate primary reason', case=False)
                ]['FieldOID'].iloc[0]
                
                # Find the other specify field
                other_field = treatment_df[
                    treatment_df['SASLabel'].str.contains('Other, specify', case=False)
                ]['FieldOID'].iloc[0]
                
                # Store the mapping
                self.field_mappings[treatment] = {
                    'form': form,
                    'date_field': date_field,
                    'reason_field': reason_field,
                    'other_field': other_field
                }
            
        except Exception as e:
            raise Exception(f"Error initializing TDMergeGenerator: {str(e)}")
    
    def generate_function(self, study_id):
        """Generate the tdMerge function based on the template."""
        try:
            # Get unique treatments
            treatments = list(self.field_mappings.keys())
            num_treatments = len(treatments)
            
            # Generate function parameters
            params = []
            for i in range(1, num_treatments + 1):
                params.append(f"td_{i}")
            
            # Generate function header
            function_name = f"tdMerge_{study_id}"
            header = f"""#' @title {function_name}
#' @description Merge all treatment discontinuation (td) crfs together
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
"""
            
            # Add treatment-specific parameters
            for i, treatment in enumerate(treatments, 1):
                header += f"""#' @param td_{i} This the {treatment} td dataframe(End of Treatment - {treatment})
"""
            
            header += """#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return td_merged
#'
"""
            header += f"""#' @export tdMerge_{study_id}
#'
#' @importFrom dplyr mutate group_by ungroup
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \\dontrun{{
#'{function_name}(studyId = studyIdVar, sourceLocation = OutFolder,
#'                       tempLogger = CentralLogger, jobdatetime = jobdatetime,
"""
            
            # Add example parameters
            for i in range(1, num_treatments + 1):
                header += f"""#'                       td_{i} = td{i},"""
            
            header += """
#'                       develop.f = develop.f, vpath = vpath)
#'}}
#'
"""
            
            # Generate function body
            body = f"{function_name} <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime, "
            body += ", ".join(params)
            body += ", develop.f = develop.f, vpath = vpath){{\n\n"
            
            body += """  withCallingHandlers(
    expr = {
      calledFun = "tdMerge_b_bgb_b3227_101"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|","{function_name}- Started."))

      # Assertions -----------------------------------------------------------------------
"""
            
            # Add assertions for each treatment
            for i in range(1, num_treatments + 1):
                body += f"""      # Assert td_{i} has min.rows and min.cols
      checkmate::assert_data_frame(td_{i}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm td_{i} has min.rows and min.cols."))
"""
            
            # Add treatment-specific processing
            for i, treatment in enumerate(treatments, 1):
                mapping = self.field_mappings[treatment]
                form = mapping['form']
                date_field = mapping['date_field']
                reason_field = mapping['reason_field']
                other_field = mapping['other_field']
                
                body += f"""
      # td_{i}: Add CRF Origin and Treatment names -----
      # UpdateVars for Treatment and CRF Origin
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_{i} dataframe"))

      if (nrow(td_{i}) > 0){{
        td_{i} <- td_{i} %>%
          #for some studies, treatment might exist in a column
          #if the td form has multiple drugs, and no treatment column
          #assign "Combo" to Treatment value.
          dplyr::mutate(Treatment = "{treatment}",
                        `CRF Origin` = "{form}") %>%
          dplyr::group_by(.data$Subject) %>%
          dplyr::mutate("EOT Date" = min(.data${date_field}_INT)) %>%
          dplyr::ungroup()
      }}else {{
        td_{i} <- td_{i} %>%
          dplyr::mutate(Treatment = character(0),
                        `CRF Origin` = character(0),
                        `EOT Date` = as.Date(integer(0),
                                             origin = "1970-01-01"))
      }}

      td_{i}<-td_{i} %>%
        dplyr::mutate("EOT Reason"=dplyr::if_else(!is.na(.data${other_field}),.data${other_field},.data${reason_field}))
      # Assign new labels to td_{i} ----
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Assign new labels to td_{i} dataframe"))
      attr(td_{i}${date_field}, "label") <- "Date of Treatment Discontinuation"
      attr(td_{i}${date_field}_INT, "label") <- "Date of Treatment Discontinuation (Interpolated)"

      td_{i} <- GSDSUtilities::raiseLabels(td_{i}, "label", isNullC = NA)
"""
            
            # Add final processing and return
            body += """
      # RaiseLabels (if avail) as the Column name ------------------------------------------------------------------------------
      # UpdateVars & UpdateScript
      # If multiple td forms exist, repeat steps above as in td_1:
      # Perform row binds after raiseLabels for each td
      # i.e. td_merged <- dplyr::bind_rows(td_1,td2,...)

     # log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of td_1 dataframe"))
     td_merged <- dplyr::bind_rows("""
            
            # Add bind_rows parameters
            body += ", ".join([f"td_{i}" for i in range(1, num_treatments + 1)])
            body += """)

      # Assign td_merged to calling envir ----------------------------------------------------
      assign("td_merged", td_merged, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," td_merged returned"))

      # End of tdMerge Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },
    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
"""
            
            # Combine header and body
            full_function = header + body
            
            return full_function
            
        except Exception as e:
            raise Exception(f"Error generating function: {str(e)}") 