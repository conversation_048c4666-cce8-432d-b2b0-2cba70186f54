#' @title ae_4_MHFlag_b_bgb_b3227_101test
#' @description Create a MHFlag for the ae crf
#'
#' @param studyId This is a character field, studyId
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param ae This the ae dataframe
#' @param mh This is the mh dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return MHFlag
#'
#' @export  ae_4_MHFlag_b_bgb_b3227_101test
#'
#' @importFrom dplyr filter mutate select rowwise
#' @importFrom checkmate assert_data_frame
#' @importFrom log4r error info warn
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom magrittr %>%
#' @importFrom stringr str_to_upper
#' @importFrom rlang .data
#' @importFrom tibble tribble
#'
#' @examples
#' \dontrun{
#'ae_4_MHFlag_b_bgb_b3227_101test(studyId = studyIdVar, tempLogger = CentralLogger,
#'                           jobdatetime = jobdatetime,
#'                           ae = ae, mh = mh, develop.f = develop.f,
#'                           vpath = vpath)
#'}
#'
#'
ae_4_MHFlag_b_bgb_b3227_101test <- function(studyId, tempLogger, jobdatetime = jobdatetime, ae, mh, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "ae_4_MHFlag_b_bgb_b3227_101test"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------

      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))
      # Assert mh has min.rows and min.cols
      checkmate::assert_data_frame(mh, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm mh has min.rows and min.cols."))

      # mh_tmp_upper which has the Prior medical condition/Procedure PT is Upper case ----
      #UpdateVars----
      mh_tmp_upper <- mh %>%
        dplyr::select(.data$Subject, MHTermPT = .data$MHTERM_PT) %>%
        dplyr::mutate(MHTermPT_upper = stringr::str_to_upper(.data$MHTermPT))


      # getMHMatch Function -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating getMHMatch Function"))
      getMHMatch <- function(sub_tmp, aeTermPT_tmp_upper){

        nr <- mh_tmp_upper %>%
          dplyr::filter(.data$Subject == sub_tmp &
                          .data$MHTermPT_upper == aeTermPT_tmp_upper)  %>%
          nrow()

        if (nr > 0) {
          return_val <- "Y"
        } else {
          return_val <- NA_character_
        }

        return(return_val)
      }

      # ae_small_tmp Dataframe  -------------------------------------------------------------

      #UpdateVars----
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_small_tmp dataframe"))
      ae_small_tmp <- ae %>%
        dplyr::select(.data$Subject, .data$RecordId, .data$AETERM_PT, .data$AETERM) %>%
        dplyr::filter(!is.na(.data$AETERM_PT)) %>%
        dplyr::mutate(aeTermPT_upper = stringr::str_to_upper(.data$AETERM_PT)) %>%
        dplyr::select(.data$Subject, .data$RecordId, .data$aeTermPT_upper)

      # Running getMHMatch for each row of ae_small_tmp Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Starting getLabMatch for each row of ae_small_tmp dataframe"))
      if(nrow(ae_small_tmp)>0){
        MHFlag <- ae_small_tmp %>%
        dplyr::rowwise() %>%
        dplyr::mutate(MHFlag =
                        getMHMatch(sub_tmp = .data$Subject,
                                   aeTermPT_tmp_upper = .data$aeTermPT_upper)) %>%
        dplyr::select(.data$Subject, .data$RecordId, .data$MHFlag)}
      else{
        MHFlag <- tibble::tribble(
          ~Subject, ~RecordId, ~MHFlag,
          "abc", 123, NA_character_
        )


      }
      # Finished getMHMatch for each row of ae_small_tmp Dataframe  -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Finished getLabMatch for each row of ae_small_tmp dataframe"))

      # Assign MHFlag to calling envir ----------------------------------------------------
      assign("MHFlag", MHFlag, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," MHFlag returned"))

      # End of aeCalc Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
