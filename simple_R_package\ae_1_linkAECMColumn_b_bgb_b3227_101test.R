#' @title ae_1_linkAECMColumn_b_bgb_b3227_101test
#' @description Link Adverse Events Table to Concomitant Medications Table by
#'   creating and appending the following columns: 1) AEList_Calc in CM and 2)
#'   CMList_Calc in AE
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param ae_tmp The ae crf dataframe
#' @param cm_tmp The cm crf dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return ae_CMList
#' @return cm_AEList
#' @return ae_cm_link
#'
#' @export ae_1_linkAECMColumn_b_bgb_b3227_101test
#'
#' @importFrom magrittr %>%
#' @importFrom dplyr select filter mutate left_join group_by summarise rename
#' @importFrom checkmate assert_directory_exists
#' @importFrom stringr str_replace_all str_trim str_split
#' @importFrom tidyr unnest
#' @importFrom log4r info warn error
#' @importFrom glue glue_collapse
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom writexl write_xlsx
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#' ae_1_linkAECMColumn_b_bgb_b3227_101test(studyId = studyIdVar, sourceLocation = OutFolder,
#'                                    tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                                    ae_tmp = ae, cm_tmp = cm, develop.f = develop.f,
#'                                    vpath = vpath))
#' }
#'
#'
ae_1_linkAECMColumn_b_bgb_b3227_101test <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime, ae_tmp, cm_tmp, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun <- "ae_1_linkAECMColumn_b_bgb_b3227_101test"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Check for duplicates --------------------------------------------------------------
      # `RecordId` is as a unifying key - it can't be duplicated in the source
      # records for the rest of this script to work
      if (any(duplicated(cm_tmp$RecordId))) {
        stop("Error -  RecordId Duplicated in cm_tmp")
      }
      if (any(duplicated(ae_tmp$RecordId))) {
        stop("Error -  RecordId Duplicated in ae_tmp")
      }
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," PASS: No Duplicated RecordId in either ae_tmp or cm_tmp."))
      # Other Assertions
      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))

      ### add `AElist_Calc` to `cm_tmp` table  ----
      # build cm_aeLookup ----
      # Exchange anything that is not a digit with whitespace,and remove leading and lagging whitespace.
      # Any enclosed whitespace will act as a single delimiter downstream in `Split` creation.
      cm_Split  <- cm_tmp %>%
        dplyr::select(.data$Subject, .data$RecordId, .data$RecordPosition, .data$CMINDCAE) %>%
        dplyr::filter(!is.na(.data$CMINDCAE) & !.data$CMINDCAE == "NA" & !.data$CMINDCAE == "") %>%
        dplyr::mutate(.,
                      "CMINDCAE_clean" = stringr::str_replace_all(.data$CMINDCAE, "[^[[:digit:]]]", " "),
                      "CMINDCAE_clean" = stringr::str_trim(.data$CMINDCAE_clean, side = "both"),
                      "Split" = stringr::str_split(.data$CMINDCAE_clean, "\\s+")
        ) %>%
        dplyr::filter(!is.na(.data$Split) & !.data$Split == "") %>%
        tidyr::unnest(cols = c(.data$Split)) %>%
        dplyr::mutate("Split" = ifelse(is.na(.data$Split),
                                       NA_character_,
                                       ifelse(grepl(.data$Split,
                                                    pattern = "^0+[1-9]"),
                                              stringr::str_replace(.data$Split,
                                                                   pattern = "^0+",
                                                                   replacement = ""),
                                              .data$Split)))

      attr(cm_Split$Split, "label") <- "Record number"

      # select elements of ae_tmp we are trying to pull into cm_tmp, and the key we'll use
      # (cm_Split$Split = aeLookup$RecordPosition)
      aeLookup <- ae_tmp %>%
        dplyr::select(.data$Subject, .data$AESEQ, .data$AETERM_PT) %>%
        dplyr::mutate(AESEQ = as.character(.data$AESEQ))

      attr(aeLookup$AESEQ, "label") <- "Record number"

      # Apply the left join, and aggregate what unnest expanded earlier.
      cm_aeLookup <- cm_Split %>%
        dplyr::left_join(aeLookup,
                         by = c("Subject" = "Subject", "Split" = "AESEQ")) %>%
        dplyr::group_by(.data$Subject, .data$RecordId) %>%
        dplyr::summarise(AEList_Calc = paste0(paste0(.data$Split, "-", .data$AETERM_PT), collapse = ", "))
      #log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|"," Okay to Ignore a Split/AESEQ Attribute Warning."))

      # leftjoin cm_tmp to cm_aeLookup ----
      cmOut <- cm_tmp %>%
        dplyr::left_join(cm_aeLookup,
                         by = c("Subject" = "Subject", "RecordId" = "RecordId"))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|"," AEList_Calc Column successfully joined to cm_tmp table."))

      cmLookup <- cm_tmp %>%
        dplyr::select(.data$Subject,
                      .data$CMINDCAE,
                      .data$CMTRT,
                      .data$CMTRT_PROD,
                      .data$RecordId,
                      .data$RecordPosition) %>%
        dplyr::filter(!is.na(.data$CMINDCAE) & !.data$CMINDCAE == "NA" & !.data$CMINDCAE == "") %>%
        dplyr::mutate(
          "CMINDCAE_clean" = stringr::str_replace_all(.data$CMINDCAE, "[^[[:digit:]]]", " "),
          "CMINDCAE_clean" = stringr::str_trim(.data$CMINDCAE_clean, side = "both"),
          "Split" = stringr::str_split(.data$CMINDCAE_clean, "\\s+") # Regexp - one or more whitespace treated as delimiter.
        ) %>%
        dplyr::filter(!is.na(.data$Split) & !.data$Split == "") %>%
        tidyr::unnest(cols = c(.data$Split)) %>%
        dplyr::mutate("Split" = ifelse(is.na(.data$Split),
                                       NA_character_,
                                       ifelse(grepl(.data$Split,
                                                    pattern = "^0+[1-9]"),
                                              stringr::str_replace(.data$Split,
                                                                   pattern = "^0+",
                                                                   replacement = ""),
                                              .data$Split))) %>%
        dplyr::select(.data$Subject, .data$Split, .data$CMTRT, .data$CMTRT_PROD, .data$RecordId, .data$RecordPosition) %>%
        dplyr::rename("CMList_Calc" = .data$CMTRT_PROD) %>%
        dplyr::mutate(CMList_Calc = ifelse(is.na(.data$CMList_Calc), .data$CMTRT, .data$CMList_Calc)) %>%
        dplyr::select(.data$Subject, .data$Split, .data$CMList_Calc, .data$RecordPosition) %>%
        dplyr::group_by(.data$Subject, .data$Split, .data$CMList_Calc) %>%
        dplyr::summarise(recPosCollap = glue::glue_collapse(.data$RecordPosition, sep = ",")) %>%
        dplyr::mutate(recPosCollapCMList_calc = paste0(.data$recPosCollap, "-", .data$CMList_Calc)) %>%
        dplyr::ungroup() %>%
        dplyr::select(.data$Subject, .data$Split, .data$recPosCollapCMList_calc) %>%
        dplyr::group_by(.data$Subject, .data$Split) %>%
        dplyr::summarise(CMList_Calc = glue::glue_collapse(.data$recPosCollapCMList_calc, sep = ", "))

      aeOut <- ae_tmp %>%
        dplyr::mutate(AESEQ = as.character(.data$AESEQ)) %>%
        dplyr::left_join(cmLookup, by = c("Subject" = "Subject", "AESEQ" = "Split"))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|"," CMList_Calc Column successfully joined to ae table."))


      # AE CM Link ------------------------------------------------------------------------
      ae_cm_link <- cm_tmp %>%
        dplyr::select(.data$Subject, .data$RecordId, .data$RecordPosition, .data$CMINDCAE) %>%
        dplyr::filter(!is.na(.data$CMINDCAE) & !.data$CMINDCAE == "NA" & !.data$CMINDCAE == "") %>%
        dplyr::mutate(
          "CMINDCAE_clean" = stringr::str_replace_all(.data$CMINDCAE, "[^[[:digit:]]]", " "),
          "CMINDCAE_clean" = stringr::str_trim(.data$CMINDCAE_clean, side = "both"),
          "Split" = stringr::str_split(.data$CMINDCAE_clean, "\\s+") # Regexp - one or more whitespace treated as delimiter.
        ) %>%
        dplyr::filter(!is.na(.data$Split) & !.data$Split == "") %>%
        tidyr::unnest(cols = c(.data$Split)) %>%
        dplyr::mutate("Split" = ifelse(is.na(.data$Split),
                                       NA_character_,
                                       ifelse(grepl(.data$Split,
                                                    pattern = "^0+[1-9]"),
                                              stringr::str_replace(.data$Split,
                                                                   pattern = "^0+",
                                                                   replacement = ""),
                                              .data$Split))) %>%
        # join cm_tmp$Subject to ae_tmp$
        dplyr::left_join((ae_tmp %>%
                            dplyr::select(.data$Subject, .data$AESEQ, .data$AETERM_PT, .data$RecordId) %>%
                            dplyr::rename(Internal_id_for_the_record_ae = .data$RecordId) %>%
                            dplyr::mutate(AESEQ = as.character(.data$AESEQ)) ),
                         by = c("Subject" = "Subject", "Split" = "AESEQ")) %>%
        dplyr::rename(Record_number_cm = .data$RecordPosition,
                      Record_number_ae_calc = .data$Split,
                      Internal_id_for_the_record_cm = .data$RecordId,
                      `Subject Name or Identifier` = .data$Subject
        ) %>%
        dplyr::select(.data$`Subject Name or Identifier`,
                      .data$`Internal_id_for_the_record_cm`,
                      .data$`Record_number_cm`,
                      .data$`CMINDCAE`,
                      .data$`CMINDCAE_clean`,
                      .data$`Record_number_ae_calc`,
                      .data$`AETERM_PT`,
                      .data$`Internal_id_for_the_record_ae`
        )

      # Assign object to calling envir ----------------------------------------------------
      attr(aeOut$AESEQ, "label") <- "Record number"
      # write out columns
      ae_CMList <- aeOut %>% dplyr::select(.data$RecordId, .data$CMList_Calc)
      # Assign ae_CMList to calling envir
      assign("ae_CMList", ae_CMList, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," ae_CMList returned"))

      cm_AEList <- cmOut %>% dplyr::select(.data$RecordId, .data$AEList_Calc)
      # Assign cm_AEList to calling envir
      assign("cm_AEList", cm_AEList, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," cm_AEList returned"))

      # Assign ae_cm_link to calling envir
      assign("ae_cm_link", ae_cm_link, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," ae_cm_link returned"))

      # add analysis output to list before raiseLabels
      cm_list <- list("cm" = cmOut)
      assign("cm_list", cm_list, envir = parent.frame())

      # Assign Labels (if avail) as the Column name.
      cmTemp <- GSDSUtilities::raiseLabels(cmOut, "label", isNullC = NA)
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|"," raiseLabels function Applied to cm_tmp"))

      # End of linkAECMColumn Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },
    # Error Functions -------------------------------------------------------------------
    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    # Warning Functions -------------------------------------------------------------------
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }

  )
}
