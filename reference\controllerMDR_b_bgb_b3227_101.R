#' @title controllerMDR_b_bgb_b3227_101
#' @description Controller to run all the b_bgb_b3227_101 MDR Scripts
#'
#' @param EDC_input_File EDC DateTime data frame 1 by 1 tibble
#' @param testing_outFolder path to a testing directory
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#' @param da_config_full_path The folder name where the diseaseAssessment
#' config files are stored. The default is set to the production version
#' of the config file.
#'
#' @return DF_b_bgb_b3227_101
#'
#' @export controllerMDR_b_bgb_b3227_101
#'
#' @importFrom dplyr filter select pull mutate_at mutate_if distinct bind_rows
#' @importFrom readr read_csv
#' @importFrom readxl read_xlsx
#' @importFrom stringr str_replace_all str_to_lower
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_directory_exists assert_file_exists assert_data_frame
#' @importFrom log4r info warn error file_appender
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data :=
#' @importFrom mdr.simplifiedDiseaseAssessment recist_1_1_fn new_get_da_config_info_fn standardize_crfs_fn
#' @importFrom mdr.labCTCAE labCTCAE
#' @importFrom stats na.omit
#' @importFrom utils write.table
#' @importFrom fs dir_delete
#'
#' @examples
#' \dontrun{
#' controllerMDR_b_bgb_b3227_101(EDC_input_File = b_bgb_b3227_101_datestamp.txt,
#'                              testing_outFolder = NULL,
#'                              develop.f = NULL, vpath = ".vault",
#'                              da_config_full_path =
#'                              "/usrfiles/spotfire/MDR_config/diseaseAssessment/")
#' }
#'
#'
controllerMDR_b_bgb_b3227_101 <- function(EDC_input_File,
                                        testing_outFolder = NULL,
                                        develop.f = NULL,
                                        vpath,
                                        da_config_full_path = "/usrfiles/spotfire/MDR_config/diseaseAssessment/") {
  withCallingHandlers(
    expr = {
      # Define Variables ------------------------------------------------------------------
      alldatasetsList <- NULL
      dose_merged <- NULL
      td_merged <- NULL
      ae_to_lab_toxflag <- NULL
      ae_CMList <- NULL
      SAEFlag <- NULL
      MHFlag <- NULL
      SubjectInfo <- NULL
      lb_calc <- NULL
      Treated <- NULL
      PPandCorevars <- NULL
      aePatientProfile <- NULL
      lb_toxgrades <- NULL
      c_lab <- NULL
      PatientProfile <- NULL
      ae_list<-NULL
      ae_cm_link <- NULL
      cm_list <-NULL
	    rs_tb<-NULL
      tl_tb <-NULL
      waterfall_spider_tb<-NULL
      da_crf_lst <- NULL
      crf_config <- NULL
      standard_da_crf_lst <- NULL
      ae_to_vs_toxflag <- NULL
      TESTDAT_INT <- NULL
      current_date <- Sys.Date()


      # Create studyIdVar -----------------------------------------------------------------
      # Define studyIdVar
      studyIdVar <- "b_bgb_b3227_101"
      # Create Controller Called Fun
      calledFun <- "controllerMDR_b_bgb_b3227_101"

      # Define EDC extract Date -----------------------------------------------------------------
      EDC_File <- readr::read_delim("/mnt/usrfiles/bgcrh/cp/blinded/bgb_b3227/bgb_b3227_101/prod/crts/sptfrvis/sas2rds/b_bgb_b3227_101_datestamp.txt", ",")
      EDCextractDate <- EDC_File$EDCextractDate

      # Setup TZ and digits ---------------------------------------------------------------
      Sys.setenv(TZ = "UTC")
      options(digits.secs = 6)
      jobdatetime <- format(Sys.time(),"%Y_%m_%d_%H_%M_%OS3")

      # Set up archive and logger -----------------------------------------------------------
      #in case of copy/paste error in hard-coded path
      #for advantig studies, will need to hard code path_study_id
      # to "advantig_###"
      path_study_id <- stringr::str_replace(studyIdVar,
                                            pattern = "^(u|b)_",
                                            replacement = "")

      # Check if testing_outFolder is not null
	    OutFolder <- paste0("/mnt/usrfiles/bgcrh/cp/blinded/bgb_b3227/bgb_b3227_101/prod/crts/sptfrvis/mdr.b.bgb.b3227.101/current/analysis/")
      # if called from Compare_outputs, testing_outFolder is not null
      OutFolder <- dplyr::if_else(!is.null(testing_outFolder),
                     paste0(stringr::str_replace(stringr::str_replace(OutFolder, "/current/analysis", ""), "prod", "dev"),current_date,"/current/analysis/"),
                     OutFolder)

      # create archive folder ---------------
      archive_root <- stringr::str_replace(OutFolder,
                                           pattern = "analysis/",
                                           replacement = "") %>%
        stringr::str_replace(.,
                             pattern = "current",
                             replacement = "archive")

      archive_folder <-  archive_root %>%
        paste0(., "archive_", jobdatetime, "/")

      if (!dir.exists(archive_folder)) {
        dir.create(archive_folder, recursive = TRUE)

      }

      # Create a logger ---------------------------------------------------------------
      # Create Log layout
      file_layout <- function(level, ...) {
        paste0(format(Sys.time(), "%d/%b/%Y %H:%M:%OS3 %z"),"|", "[",level,"]","|", ...,"|",
               collapse = "",gsub("@beigenecorp.net", "", as.character(as.data.frame(Sys.info())[("user"),1])),"\r")
      }
      # Create Log file (empty)
      file.create(paste0(archive_folder,"MDRLogging_",jobdatetime,".log"))
      # Write Header for log file
      writeLines("DateTime|LogLevel|Type|FunctionName|Message|UserName",
                 paste0(archive_folder,"MDRLogging_",jobdatetime,".log"))
      # Create logger object
      CentralLogger <- log4r::logger(threshold = "DEBUG",
                                     appenders = c(log4r::file_appender(paste0(archive_folder,
                                                                               "MDRLogging_",
                                                                               jobdatetime,
                                                                               ".log"),
                                                                        append = TRUE, file_layout)
                                     )
      )
      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Start of ",calledFun))

      # Email started ------------------------------------------------------
      #SCL 2020_03_30 - EMAIL Started
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("Start of MDR Transformation ", calledFun, " at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>", calledFun, " Started at: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }

	 checkmate::assert_true(stringr::str_detect(OutFolder,
                                                 pattern = path_study_id))

      archive_xlsx_folder <- paste0(archive_folder, "xlsx/")

      if (!dir.exists(archive_xlsx_folder)) {
        dir.create(archive_xlsx_folder, recursive = TRUE)

      }

      checkmate::assert_directory_exists(archive_xlsx_folder)

      archive_analysis_folder <- paste0(archive_folder, "analysis/")

      if (!dir.exists(archive_analysis_folder)) {
        dir.create(archive_analysis_folder, recursive = TRUE)

      }

      checkmate::assert_directory_exists(archive_analysis_folder)

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","archive xlsx and analysis folders created."))

      # create archive_preprocess folder (only if xlsx version of preprocess outside of c_lab is req.) ---------
      #preprocessing not necessary for every study

      # archive_preprocess_folder <- paste0(archive_folder, "preprocess/")
      # if (!dir.exists(archive_preprocess_folder)) {
      #   dir.create(archive_preprocess_folder, recursive = TRUE)
      #
      # }
      #
      # checkmate::assert_directory_exists(archive_preprocess_folder)
      #
      # log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","archive preprocess folders created."))

      #CREATE OUTFOLDER - DO NOT DELETE -----------------------------------------

      if (!dir.exists(OutFolder)) {
        dir.create(OutFolder, recursive = TRUE)

      }

      checkmate::assert_directory_exists(OutFolder)
      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","OutFolder created."))

      #create xlsx folder --------
      xlsx_folder <- stringr::str_replace(OutFolder,
                                          pattern = "analysis",
                                          replacement = "xlsx")

      checkmate::assert_true(stringr::str_detect(xlsx_folder,
                                                 pattern = path_study_id))

      if (!dir.exists(xlsx_folder)) {
        dir.create(xlsx_folder, recursive = TRUE)

      }

      checkmate::assert_directory_exists(xlsx_folder)

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","xlsx and analysis folders."))

      #create preprocess folder -----
      #preprocessing not necessary for every study
      # preprocess_folder <- stringr::str_replace(OutFolder,
      #                                           pattern = "analysis",
      #                                           replacement = "preprocess")
      #
      # checkmate::assert_true(stringr::str_detect(preprocess_folder,
      #                                            pattern = path_study_id))
      #
      # if (!dir.exists(preprocess_folder)) {
      #   dir.create(preprocess_folder, recursive = TRUE)
      #
      # }
      #
      # checkmate::assert_directory_exists(preprocess_folder)
      #
      # log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","preprocess folders."))

      #Archive xlsx and analysis files --------------------
      all_xlsx_files <- list.files(xlsx_folder)

      purrr::walk2(.x = paste0(xlsx_folder, all_xlsx_files),
                   .y = paste0(archive_folder, "xlsx/", all_xlsx_files),
                   .f = function(.x, .y) file.copy(from = .x, to = .y))

      #will delete empty xlsx directory without conditional
      if (length(all_xlsx_files) > 0) {
        purrr::walk(paste0(xlsx_folder, all_xlsx_files),
                    file.remove)

      }

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","xlsx files archived."))

      all_analysis_files <- list.files(OutFolder)

      purrr::walk2(.x = paste0(OutFolder, all_analysis_files),
                   .y = paste0(archive_folder, "analysis/", all_analysis_files),
                   .f = function(.x, .y) file.copy(from = .x, to = .y))

      #will delete empty analysis directory without conditional
      if (length(all_analysis_files) > 0) {
        purrr::walk(paste0(OutFolder, all_analysis_files),
                    file.remove)

      }

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","analysis files archived."))

      #preprocessing not necessary for every study
      # all_preprocess_files <- list.files(preprocess_folder)
      #
      # purrr::walk2(.x = paste0(preprocess_folder, all_preprocess_files),
      #              .y = paste0(archive_folder, "preprocess/", all_preprocess_files),
      #              .f = function(.x, .y) file.copy(from = .x, to = .y))
      #
      # #will delete empty analysis directory without conditional
      # if (length(all_preprocess_files) > 0) {
      #   purrr::walk(paste0(preprocess_folder, all_preprocess_files),
      #               file.remove)
      #
      # }
      #
      # log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","preprocess files archived."))

      # delete archives > 30 days out -------------------------
      archive_dirs <- list.dirs(archive_root,
                                full.names = FALSE,
                                recursive = FALSE)

      archive_dates <- archive_dirs %>%
        stringr::str_replace(.,
                             pattern = "archive_",
                             replacement = "") %>%
        purrr::map(.x = .,
                   .f = function(.x) paste0(stringr::str_split(.x,
                                                               pattern = "_",
                                                               simplify = TRUE)[c(1, 2, 3)],
                                            collapse = "-")) %>%
        purrr::map(., as.Date)

      delete_lgl_lst <- purrr::map(.x = archive_dates,
                                   .f = function(.x) difftime(Sys.Date(), .x, units = "days") > 30)

      dirs_to_delete <- purrr::map2(.x = archive_dirs,
                                    .y = delete_lgl_lst,
                                    .f = function(.x, .y) ifelse(.y == TRUE, .x, NA)) %>%
        unlist() %>%
        stats::na.omit()

      if (length(dirs_to_delete) > 0) {
        dirs_to_delete <- dirs_to_delete %>%
          paste0(archive_root, ., "/")
        purrr::walk(dirs_to_delete, fs::dir_delete)

      }

      #print EDC input file to analysis location ----------------
      edc_input_folder <- stringr::str_replace(OutFolder,
                                               pattern = "analysis/",
                                               replacement = "")

      utils::write.table(EDC_input_File,
                         file = paste0(edc_input_folder, "b_bgb_b3227_101_datestamp.txt"),
                         sep = "\t",
                         row.names = FALSE,
                         na = "")

      # Load Study alldatasetsList objects ------------------------------------------------------------
      # Extract the specified `sourceLocation`
      crf_sourceLocation <- "/mnt/usrfiles/bgcrh/cp/blinded/bgb_b3227/bgb_b3227_101/prod/crts/sptfrvis/sas2rds/"
      fst_sourceLocation <- "/mnt/usrfiles/bgcrh/cp/blinded/bgb_b3227/bgb_b3227_101/prod/crts/sptfrvis/diffTrackR/output/"

      # in case of copy/paste error with paths
      checkmate::assert_true(stringr::str_detect(crf_sourceLocation,
                                                 pattern = path_study_id))

      checkmate::assert_true(stringr::str_detect(fst_sourceLocation,
                                                 pattern = path_study_id))

      alldatasetsList <- mdrAddNewDataFlag(diff_track_path = fst_sourceLocation,
                                           crf_path = crf_sourceLocation,
                                           dev_ind = develop.f,
                                           vault_path = vpath,
                                           temp_logger = CentralLogger,
                                           job_date_time = jobdatetime,
                                           study_id = studyIdVar)

      log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","All alldatasetsList created"))

      #Print preprocess file to study folder(only if xlsx version of preprocess outside of c_lab is req.)-----------------
      #preprocessing not necessary for every study
      # preprocess_file_lst <- c("replace_me")
      #
      # preprocess_path_lst <- paste0(crf_sourceLocation, preprocess_file_lst, "_", studyIdVar, ".rds")
      #
      # purrr::walk(.x = preprocess_path_lst, .f = checkmate::assert_file_exists)
      #
      # preprocess_print_lst <- purrr::map(.x = preprocess_path_lst, .f = readRDS)
      #
      # purrr::walk2(.x = preprocess_print_lst,
      #              .y = paste0(preprocess_folder, preprocess_file_lst, ".xlsx"),
      #              .f = function(.x, .y) writexl::write_xlsx(list("Sheet 1" =  .x),
      #                                                        path = .y,
      #                                                        format_headers = FALSE))


  #UpdateVars
  crfs_to_extract <- c("ae", "cm", "mh", "lab", "ds_enr", "rs_r", "pr_pc", "sd", "dd", "td", "td2", "td3","td4", "dm",
                       "subject", "ec_or","ec_inf", "ec_inf2", "ec_inf3","mh_dx", "tu_tlr", "tu_ntr", "tu_newr", "vs_scr", "vs",
                       "vs2", "eg", "eg_trp")
  # Extract required CRFs
  ae <- alldatasetsList$ae_mix
  cm <- alldatasetsList$cm
  mh <- alldatasetsList$mh
  lab <- alldatasetsList$lab
  ds_enr <- alldatasetsList$ds_enr
  rs_r <- alldatasetsList$rs_r
  pr_pc <- alldatasetsList$pr_pc
  sd <- alldatasetsList$ds_eos
  dd <- alldatasetsList$dd
  td <- alldatasetsList$ds_eot
  td2 <- alldatasetsList$ds_eot2
  td3 <- alldatasetsList$ds_eot3
  td4 <- alldatasetsList$ds_eot4
  dm <- alldatasetsList$dm
  subject <- alldatasetsList$subject
  ec_or <- alldatasetsList$ec_or
  ec_inf <- alldatasetsList$ec_inf
  ec_inf2 <- alldatasetsList$ec_inf2
  ec_inf3 <- alldatasetsList$ec_inf3
  mh_dx <- alldatasetsList$mh_dx
  tu_tlr <- alldatasetsList$tu_tlr
  tu_newr <- alldatasetsList$tu_newr
  tu_ntr <- alldatasetsList$tu_ntr
  vs_scr <- alldatasetsList$vs_scr
  vs <- alldatasetsList$vs
  vs2 <- alldatasetsList$vs2
  eg <- alldatasetsList$eg
  eg_trp <- alldatasetsList$eg_trp

  #assert dataframes have atleast 2 rows
  for (i_crfs in seq_along(crfs_to_extract)) {
    checkmate::assert_data_frame(get(crfs_to_extract[i_crfs]), min.rows = 0)
  }
  rm(i_crfs)
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Extracted required CRFs"))

  #Directories -----------------------------------------------------------------------
  ctcaelookupFolder <-  "/usrfiles/spotfire/MDR_config/labCTCAE/"
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if ctcaelookupFolder exists", ctcaelookupFolder))
  checkmate::assert_directory_exists(ctcaelookupFolder)

  ctcvslookupFolder <-  "/usrfiles/spotfire/MDR_config/vsCTCAE/"
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if ctcvslookupFolder exists", ctcvslookupFolder))
  checkmate::assert_directory_exists(ctcvslookupFolder)

  # Define Study Parameters/Variables -------------------------------------------------

  # Central Lab (if available set to TRUE)
  central_lab.f <- FALSE
  if (central_lab.f) {
    # Check Central Lab File exists and load
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check Central Lab files"))
    c_lab_path <- "replace_me"
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if c_lab_path file exists"))
    checkmate::assert_file_exists(c_lab_path)
    c_lab <- readRDS(c_lab_path)
    log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Created c_lab dataframe"))
    #UpdateVars: Map central lab variables to standardized names
    c_lab <- c_lab %>%
      dplyr::mutate(`Subject` =  dplyr::if_else(!is.na(.data$SUBJID) & !.data$SUBJID == "", .data$SUBJID, .data$SCRNNUM)) %>%
      dplyr::select(
        Subject = .data$Subject,
        LBTEST = .data$LBTEST ,
        `SiteNumber` = .data$SITEID ,
        Folder = .data$VISIT,
        project = .data$STUDYID,
        `RecordDate` = .data$LBDTM ,
        NumericValue = .data$RPTRESN,
        LabLow = .data$RPTNRLO,
        LabHigh =  .data$RPTNRHI,
        LabUnits = .data$RPTU,
        StdValue = .data$SIRESN,
        StdLow  =  .data$SINRLO,
        StdHigh = .data$SINRHI,
        StdUnits = .data$SIU,
        LabName = .data$LBNAM,
        LabFlag = .data$ALRTFL,
        AnalyteValue = .data$RPTRESC,
        FormName = .data$BATTRNAM
      )


  }

  # AnalytePT file
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if AnalytePT file exists"))
  checkmate::assert_file_exists(paste0(ctcaelookupFolder,"AnalytePT.xlsx"))
  AnalytePT <- readxl::read_xlsx(paste0(ctcaelookupFolder,"AnalytePT.xlsx"))

  # vitalPT file
  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","Check if vitalPT file exists"))
  checkmate::assert_file_exists(paste0(ctcvslookupFolder,"vitalPT.xlsx"))
  vitalPT <- readxl::read_xlsx(paste0(ctcvslookupFolder,"vitalPT.xlsx"))

	# create a llst to store analysis output------------------------------------------------------
		analysis_list <- list()

	# Call ae_1_linkAECMColumn_b_bgb_b3227_101 Function ------------------------------------------------------
	  ae_1_linkAECMColumn_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
									tempLogger = CentralLogger, jobdatetime = jobdatetime, ae_tmp = ae, cm_tmp = cm,
									develop.f = develop.f, vpath = vpath)
			# add analysis output to list
			ae_cm_list <- list("ae_cm_link" = ae_cm_link)
			analysis_list<-append(analysis_list, ae_cm_list)
			analysis_list<-append(analysis_list, cm_list)

	# Call doseMerge_b_bgb_b3227_101 Function ------------------------------------------------------
	  doseMerge_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
							   tempLogger = CentralLogger, jobdatetime = jobdatetime,
							   drug1 = ec_inf , drug2 = ec_inf2, drug3 = ec_inf3, drug4 = ec_or,
							   develop.f = develop.f, vpath = vpath)
			# add analysis output to list
			dose_list <- list("dose_merged" = dose_merged)
			analysis_list<-append(analysis_list, dose_list)

	# Call tdMerge_b_bgb_b3227_101 Function ------------------------------------------------------
	  tdMerge_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
							 tempLogger = CentralLogger, jobdatetime = jobdatetime,
							 td_1 = td, td_2 = td2, td_3 = td3, td_4 = td4, develop.f = develop.f, vpath = vpath)
		   # add analysis output to list
			tdmerge_list <- list("td_merged" = td_merged)
			analysis_list<-append(analysis_list, tdmerge_list)

	# Call subjectInfo_b_bgb_b3227_101 Function ------------------------------------------------------
	  subjectInfo_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
								 tempLogger = CentralLogger, jobdatetime = jobdatetime, dose_merged = dose_merged, ds_enr = ds_enr,
								 subject = subject, sd = sd, dd=dd, td_merged = td_merged, dm = dm,
								 rs_r = rs_r, mh_dx = mh_dx, develop.f = develop.f, vpath = vpath)
       # add analysis output to list
        subject_list <- list("SubjectInfo" = SubjectInfo)
        analysis_list<-append(analysis_list, subject_list)

  # Call diseaseAssessment Functions ------------------------------------------------------------
  #get study-specific configuration information
  da_config <-mdr.simplifiedDiseaseAssessment::new_get_da_config_info_fn(study_name = studyIdVar,
                                                                config_path = da_config_full_path)
  #create list of used CRFs for the study
  da_crf_name_lst <- purrr::map(.x = da_config$crf_config,
                                .f = function(.x) .x$crf_name)
  da_crf_name_lst$subject_info <- NULL

  da_crf_lst <- sapply(da_crf_name_lst, get, envir = sys.frame(sys.parent(0)), simplify = FALSE)
  names(da_crf_lst) <- da_crf_name_lst
  da_crf_lst$SubjectInfo <- SubjectInfo

  standard_da_crf_lst <-mdr.simplifiedDiseaseAssessment::standardize_crfs_fn(da_crf_lst,
                                                                    da_config$crf_config,
                                                                    studyIdVar,
                                                                    CentralLogger)

  # SEE WORKING INSTRUCTIONS FOR DISEASE ASSESSMENT
  # controller updates here may be necessary
  standard_da_crf_lst$clean$nl_tb <- standard_da_crf_lst$clean$nl_tb %>%
    dplyr::filter(.data$nl_yn == "Yes")

standard_da_crf_lst$clean$tl_tb <- standard_da_crf_lst$clean$tl_tb %>%
  dplyr::mutate(tl_split_b_diam = dplyr::if_else(
      is.na(.data$tl_split_b_diam) &
      is.na(.data$tl_split_c_diam) , NA_real_,
      dplyr::select(.,
                    c(.data$tl_split_b_diam,
                      .data$tl_split_c_diam
                      )) %>% rowSums(na.rm = TRUE))) %>%
  dplyr::select(-.data$tl_split_c_diam)



  # get results of disease assessment analysis
  da_results <-mdr.simplifiedDiseaseAssessment::recist_1_1_fn(study_name = studyIdVar,
                                                     study_data_lst = standard_da_crf_lst,
                                                     temp_logger = CentralLogger,
                                                     job_date_time = jobdatetime,
                                                     develop.f = develop.f,
                                                     vpath = vpath,
                                                     config_info = da_config,
                                                     spotfire_ind = 1)

	# add analysis output to list
	  waterfall_spider_list <- list("waterfall_spider_tb" = waterfall_spider_tb)
	  analysis_list<-append(analysis_list, waterfall_spider_list)

	  swimmers_list <- list("swimmers_tb" = da_results$swimmers_tb)
	  analysis_list<-append(analysis_list, swimmers_list)

	  tl_list <- list("tl_tb" = tl_tb)
	  analysis_list<-append(analysis_list, tl_list)

	  rs_list <- list("rs_tb" = rs_tb)
	  analysis_list<-append(analysis_list, rs_list)

	  da_error_list <- list("da_error_tb" = da_results$error_tb)
	  analysis_list<-append(analysis_list, da_error_list)

	  waterfall_spider_removal_list <- list("waterfall_spider_removal_tb" = da_results$waterfall_spider_removal_tb)
	  analysis_list<-append(analysis_list, waterfall_spider_removal_list)

	  rs_mismatch_pp_list <- list("rs_mismatch_pp" = da_results$rs_mismatch_pp)
	  analysis_list <- append(analysis_list, rs_mismatch_pp_list)


  # Call labCTCAE function -------------------------------------------------------------------------
  lb_toxgrades <- mdr.labCTCAE::labCTCAE(lab = lab,
                                         c_lab = c_lab,
                                         central_lab.f = central_lab.f,
                                         SubjectInfo = SubjectInfo,
                                         version = 5,
                                         Hallek = "N",
                                         studyIdVar = studyIdVar,
                                         OutFolder = OutFolder,
                                         CentralLogger = CentralLogger,
                                         jobdatetime = jobdatetime,
                                         develop.f = develop.f, vpath = vpath)


  # Call ae_2_ToxFlag_b_bgb_b3227_101 Function ------------------------------------------------------
  ae_2_ToxFlag_b_bgb_b3227_101(studyId = studyIdVar,
                        tempLogger = CentralLogger,
                        jobdatetime = jobdatetime,
                        ae = ae,
                        lb_toxgrades = lb_toxgrades,
                        AnalytePT = AnalytePT,
                        develop.f = develop.f, vpath = vpath)

	# Call ae_3_SAEFlag_b_bgb_b3227_101 Function ----------------------------------------------


	  ae_3_SAEFlag_b_bgb_b3227_101(studyId = studyIdVar, tempLogger = CentralLogger,
								  jobdatetime = jobdatetime, ae = ae,
								  develop.f = develop.f, vpath = vpath)


	# Call ae_4_MHFlag_b_bgb_b3227_101 Function ----------------------------------------------

	  ae_4_MHFlag_b_bgb_b3227_101(studyId = studyIdVar, tempLogger = CentralLogger,
								 jobdatetime = jobdatetime,
								 ae = ae, mh = mh,
								 develop.f = develop.f, vpath = vpath)


      # Call vitalctcae_grading_b_bgb_b3227_101 Function---------------------------------------------------------
      config_path <- paste0(ctcvslookupFolder,"b_bgb_b3227_101_vs_crf_config.csv")
      vitalctcae_config_b_bgb_b3227_101(studyId =  studyIdVar, tempLogger = CentralLogger, config_path = config_path)

      # create list of used CRFs for the study
      da_crf_name_lst <- purrr::map(.x = crf_config, .f = function(.x) .x$crf_name)
      da_crf_lst <- sapply(da_crf_name_lst, get, envir = sys.frame(sys.parent(0)), simplify = FALSE)
      names(da_crf_lst) <- da_crf_name_lst


      vitalctcae_datastandardization_b_bgb_b3227_101(studyId =  studyIdVar, tempLogger = CentralLogger,da_crf_lst = da_crf_lst, crf_config = crf_config)

      # merge all study specific Vital/ECG CRFs

      # if weight data is collected as a seperated CRF
      # weight_stand <- standard_da_crf_lst$weight %>%
      #   dplyr::filter(.data$WEYN =="Yes" ) %>%
      #   dplyr::mutate(Test = "weight",
      #                 Timepoint = NA_character_) %>%
      #   dplyr::select(-.data$WEYN)


      # if there are multiple ECG CRFs
      eg_stand <- dplyr::bind_rows(standard_da_crf_lst$eg1,standard_da_crf_lst$eg2) %>%
        dplyr::filter(.data$EGPERF =="Yes") %>%
        dplyr::mutate(Test = "QTc",
                      unit = "ms") %>%
        dplyr::select(-.data$EGPERF) %>%
        dplyr::group_by(.data$Subject, .data$InstanceName,.data$TESTDAT_INT) %>%
        dplyr::mutate(stdvalue = mean(.data$stdvalue, na.rm = TRUE)) %>%
        dplyr::distinct(.data$Subject, .data$instanceId,.keep_all = T)


      vs_merged <-  dplyr::bind_rows(standard_da_crf_lst$vs1,standard_da_crf_lst$vs2,standard_da_crf_lst$vs3) %>%
        dplyr::filter(.data$VSYN == "Yes") %>%
        dplyr::mutate(SystolicBP_unit ="mmHg",
                      DiastolicBP_unit = "mmHg")  %>%
        dplyr::select(-.data$VSYN) %>%
        tidyr::pivot_longer(cols = c(.data$weight_stdvalue,.data$temp_stdvalue,.data$SystolicBP_stdvalue,.data$DiastolicBP_stdvalue,
                                     .data$weight_unit,.data$SystolicBP_unit,.data$DiastolicBP_unit),
                            names_to = c("Test",".value"),
                            names_pattern = "(.*)_(.*)") %>%
        dplyr::bind_rows(eg_stand)

      vitalctcae_grading_b_bgb_b3227_101(studyId =  studyIdVar,
                         tempLogger = CentralLogger,
                         vs_merged = vs_merged,
                         vitalPT = vitalPT,
                         lb_toxgrades_tmp = lb_toxgrades,
                         SubjectInfo_tmp = SubjectInfo,
                         ae = ae)


	# add analysis output to list
	  vital <- list("vital" = vital)
	  analysis_list <- append(analysis_list, vital)

	# Call ae_5_AEWriteOut_b_bgb_b3227_101 Function ----------------------------------------------

	  ae_5_AEWriteOut_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
									 tempLogger = CentralLogger, jobdatetime = jobdatetime,
									 ae = ae, ae_to_lab_toxflag = ae_to_lab_toxflag,
									 ae_to_vs_toxflag = ae_to_vs_toxflag,
									 ae_CMList = ae_CMList, SAEFlag = SAEFlag, MHFlag = MHFlag , SubjectInfo = SubjectInfo,
									 develop.f = develop.f, vpath = vpath)
			# add analysis output to list
			analysis_list<-append(analysis_list, ae_list)

	# Call labCalc_b_bgb_b3227_101 Function ------------------------------------------------------
	  labCalc_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
							 tempLogger = CentralLogger, jobdatetime = jobdatetime,
							 ae = ae, lab = lab, lb_toxgrades = lb_toxgrades,
							 AnalytePT = AnalytePT, SubjectInfo_tmp = SubjectInfo,
							 develop.f = develop.f, vpath = vpath)
			# add analysis output to list
			lb_list <- list("lab" = lb_calc)
			analysis_list<-append(analysis_list, lb_list)

	# Call patientProfile_b_bgb_b3227_101 Function ------------------------------------------------------
	  patientProfile_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
									tempLogger = CentralLogger, jobdatetime = jobdatetime, EDCextractDate = EDCextractDate,
									SubjectInfo = SubjectInfo, ae = ae_list$ae,
									cm = cm, rs_r = rs_r, lb_calc = lb_calc, pr_pc = pr_pc,
									td_merged = td_merged, dose_merged = dose_merged, Treated = Treated,
									develop.f = develop.f, vpath = vpath)
		   # add analysis output to list
			patient_list <- list("PatientProfile" = PatientProfile)
			analysis_list<-append(analysis_list, patient_list)
			aepatient_list <- list("aePatientProfile" = aePatientProfile)
			analysis_list<-append(analysis_list, aepatient_list)

   #add studyday/studyweek, cohort/enrolled/treated related, column cleaning -------------------------
	  #UpdateVars
	  # cohort/arm is study specific-remember to update
		appendCol<- c("Cohort","Phase","Part",
					"FirstDoseDate","OnTreatment",
					"Enrolled","TotalEnrolled", "TotalEnrolled by Cohort/Arm",
					"Treated","TotalTreated" , "TotalTreated by Cohort/Arm")

	  #UpdateVars
	  # set TRUE for studies that need standardized output
	  standardcol<- TRUE

	  if (standardcol== TRUE) {
		checkmate::assert_true(all(appendCol %in% names(SubjectInfo)))
		called_fun <- "mdrJoinCohortStudyDay"
			StudyDay_Cohort_added_tb <- mdrJoinCohortStudyDay(studyId = studyIdVar,alldatasetsList,
														  appendCol=appendCol, SubjectInfo, sourcetype="crf",  tempLogger = CentralLogger)
			log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","StudyDay_Cohort_added_tb created for crf."))
	  }
	  else {
		StudyDay_Cohort_added_tb <-alldatasetsList
	  }
	  # #Print xlsx -------------------------
	  # #raise labels
	  print_new_data_flag_lst <- purrr::map(StudyDay_Cohort_added_tb,
											GSDSUtilities::raiseLabels,
											attrC = "label",
											isNullC = NA)

	  purrr::walk2(.x =  print_new_data_flag_lst,
				   .y = names( print_new_data_flag_lst),
				   new_data_out_path = xlsx_folder,
				   .f = function(.x,
								 .y,
								 new_data_out_path) writexl::write_xlsx(list("Sheet 1" = .x),
																		path = paste0(new_data_out_path,
																					  .y,
																					  ".xlsx")))
	  #Print analysis output list to rds------------------------
	   saveRDS(analysis_list, file = paste0(OutFolder,"analysis_list.rds"))
	   log4r::info(CentralLogger,paste0(studyIdVar,"|",calledFun,"|","Print analysis output list to rds."))

	  #Add/Clean columns for analysis files and print------------------------
	  if (standardcol== TRUE) {
		checkmate::assert_true(all(appendCol %in% names(SubjectInfo)))
		called_fun <- "mdrJoinCohortStudyDay"
		print_new_analysis <- mdrJoinCohortStudyDay(studyId = studyIdVar, analysis_list,
														  appendCol=appendCol, SubjectInfo, sourcetype="analysis", tempLogger = CentralLogger)
		log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"|","StudyDay_Cohort_added_tb created for analysis."))

	  }
	  else {
		#ae,cm etc not gone through raiseLabels yet
		for (n in c("ae","waterfall_spider_tb","tl_tb","rs_tb")){
		  analysis_list[[n]]<-GSDSUtilities::raiseLabels(analysis_list[[n]], "label", isNullC = NA)
		}
		print_new_analysis <-analysis_list
	  }

	  files_name<- names(print_new_analysis)

	  purrr::walk2(.x =  print_new_analysis,
              .y = files_name,
              new_data_out_path = OutFolder,
              .f = function(.x,
                            .y,
                            new_data_out_path) if ((.y == 'PatientProfile') | (.y == 'lab')) { readr::write_delim(
                              x = .x,
                              path =  paste0(new_data_out_path,.y,".csv"),
                              delim  = "|",
                              quote_escape = "double",
                              na = "")
                            }
                            else writexl::write_xlsx(list("Sheet 1" = .x),
                                                     path = paste0(new_data_out_path,
                                                                   .y,
                                                                   ".xlsx"))
                )

	  # Remove alldatasetsList list
	  rm(alldatasetsList)
	  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove alldatasetsList list"))
	  rm(print_new_data_flag_lst)
	  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove print_new_data_flag_lst list"))
	  rm(StudyDay_Cohort_added_tb)
	  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove StudyDay_Cohort_added_tb list"))
	  rm(analysis_list)
	  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove analysis_list list"))
	  rm(print_new_analysis)
	  log4r::info(CentralLogger, paste0(studyIdVar,"|",calledFun,"| ","Remove print_new_analysis list"))

  # End of Controller -----------------------------------------------------------------
  log4r::info(CentralLogger,paste0(studyIdVar,"|",calledFun,"|",calledFun,"- All done, quitting."))
  # Email finished ------------------------------------------------------
  #SCL 2029_03_30 - EMAIL finished
  if (is.null(develop.f)) {
    mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                     to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                     subject = paste0("Completion of MDR Transformation ", calledFun," jobdatetime ",jobdatetime),
                     body = paste0("<html><body><p>", calledFun, " Finished at: ", format(Sys.time(), "%Y-%m-%d %X %Z"),"</p></body></html>"),
                     html = TRUE,
                     smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                     authenticate = TRUE,
                     send = TRUE)
  }
  # Return EDC_input_File dataframe for drake ------
  return(EDC_input_File)
 },

 # Error Functions -------------------------------------------------------------------
 error = function(e){
   #Log that error was caught, print error, log detail error condition, and stop
   log4r::error(CentralLogger,paste0(studyIdVar,"|",calledFun,"|",calledFun,": Caught an error!"))
   log4r::error(CentralLogger, paste0(studyIdVar,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
   log4r::info(CentralLogger,paste0(studyIdVar,"|",calledFun,"|",calledFun,"- All done, quitting."))
   #SCL 2029_03_30 - EMAIL on Error
   if (is.null(develop.f)) {
     mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                      to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                      subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                      body = paste0("<html><body><p>StudyId: ", studyIdVar, " </p><p>Error DateTime: ",
                                    format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                      html = TRUE,
                      smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                      authenticate = TRUE,
                      send = TRUE)
   }
   stop(paste0("Failure in function :", calledFun))
 },

 # Warning Functions -----------------------------------------------------------------
 warning = function(w){
   # Log that warning was caught, print warning, log detail warning condition
   log4r::warn(CentralLogger,paste0(studyIdVar,"|",calledFun,"|",calledFun,": Caught a warning!"))
   log4r::warn(CentralLogger, paste0(studyIdVar,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
   invokeRestart("muffleWarning")
  }
 )
}
# Add StudyDay, Cohort, Column Cleaning Function ------------------------------------------------------
#' @title mdrJoinCohortStudyDay
#' @description To each output, add cohort related, enrolled/treated related, study day/week for dates, remove not-needed columns for each output per config file
#'
#' @param studyId This is a character field, studyId
#' @param alldatasetsList A list of tibbles of crfs extracted from sas2rds
#' @param appendCol A list of columns to be append to each output
#' @param SubjectInfo This is the SubjectInfo dataframe
#' @param sourcetype Categories used for filter in config file
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#'
#' @return A list of tibbles of crfs and analysis output with cohort related, study day added, with columns cleaned
#'
#' @export mdrJoinCohortStudyDay
#'
#' @importFrom dplyr filter select distinct pull
#' @importFrom tibble tibble
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_tibble
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#' mdrJoinCohortStudyDay(studyId=studyId,alldatasetsList = alldatasetsList,
#' appendCol=appendCol,SubjectInfo = SubjectInfo,sourcetype=sourcetype,
#' tempLogger=tempLogger)
#' }
mdrJoinCohortStudyDay = function(studyId,alldatasetsList,appendCol,SubjectInfo,sourcetype,tempLogger){

  calledFun <- "mdrJoinCohortStudyDay"
  crf_col_exclusion_config <- readr::read_csv("/usrfiles/spotfire/mdr_config/mdr_column_cleaning_config.csv")
  checkmate::assert_tibble(crf_col_exclusion_config)
  checkmate::assert_tibble(SubjectInfo, min.rows = 1, min.cols = 2)
  checkmate::assert_list(alldatasetsList)

  exact_exclusions <- crf_col_exclusion_config %>%
    dplyr::filter(.data$edc == "Rave" & .data$match_type == "exact" & .data$category != "date") %>%
    dplyr::pull(.data$match_value)

  grep_exclusions <- crf_col_exclusion_config %>%
    dplyr::filter(.data$edc == "Rave" & .data$match_type == "grep"& .data$category != "date") %>%
    dplyr::pull(.data$match_value)

  dates_studyday <- crf_col_exclusion_config %>%
    dplyr::filter(.data$edc == "Rave" & .data$match_type == "grep"& .data$category == "date") %>%
    dplyr::pull(.data$match_value)

  StudyDay_Cohort_added_tb <- purrr::map(.x = seq_along(alldatasetsList), .f = ~{
    if (length(alldatasetsList[.x][[1]])!=0) {#avoid null tibble
      crfname<- names(alldatasetsList[.x])
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",crfname," - process started."))
      #exclude columns that already in the table, for example cohort is already in enr, Treated already in ae
      #comparison is on label not variable name
      col_label <- GSDSUtilities::raiseLabels(alldatasetsList[[.x]], "label", isNullC = NA)
      col_to_add <- setdiff(appendCol, names(col_label))
      FirstDose_Cohort_tb <- SubjectInfo %>%
        dplyr::select("Subject name or identifier", col_to_add)
      if (crfname!="SubjectInfo") {
        # SubjectInfo already has col_to_add
        if ("Subject name or identifier" %in% colnames(alldatasetsList[[.x]])){
          # for joining tibble after raiselabel
          FirstDose_Cohort_df <- alldatasetsList[[.x]] %>%
            dplyr::left_join(FirstDose_Cohort_tb, by =c("Subject name or identifier")) %>%
            dplyr::mutate_if(purrr::is_character, list(~dplyr::na_if(., "")))
        }
        else if ("Subject Name or Identifier" %in% colnames(alldatasetsList[[.x]])) {
          # for subject label variation, for example, in ae_1_linkAECMColumn file
          FirstDose_Cohort_df <- alldatasetsList[[.x]] %>%
            dplyr::left_join(FirstDose_Cohort_tb, by =c("Subject Name or Identifier"="Subject name or identifier")) %>%
            dplyr::mutate_if(purrr::is_character, list(~dplyr::na_if(., "")))
        }
        else {
          # for joining tibble before raiselabel
          FirstDose_Cohort_df <- alldatasetsList[[.x]] %>%
            dplyr::left_join(FirstDose_Cohort_tb, by =c("Subject"="Subject name or identifier")) %>%
            dplyr::mutate_if(purrr::is_character, list(~dplyr::na_if(., "")))
        }
      }
      else {

        FirstDose_Cohort_df <- alldatasetsList[[.x]]
      }
      return_tb <- FirstDose_Cohort_df
      for(i in 1:ncol(FirstDose_Cohort_df))
      {
        if (((any(dates_studyday %>% purrr::map(function(x) grepl(x, colnames(FirstDose_Cohort_df)[i]))==TRUE)) &
             ((sapply(FirstDose_Cohort_df[i],class)[1]== "Date"
               |sapply(FirstDose_Cohort_df[i],class)[1]== "POSIXct")))
            |(crfname=="lab"  & (colnames(FirstDose_Cohort_df)[i]=="RecordDate"
                                 |colnames(FirstDose_Cohort_df)[i]=="Clinical date of record (ex: visit date)")))
        {

          if (is.null(attr(FirstDose_Cohort_df[[i]], "label"))) {
            studyday_column <- paste0(colnames(FirstDose_Cohort_df)[i],"_StudyDay")
          } else {
            studyday_column <- paste0(attr(FirstDose_Cohort_df[[i]], "label"),"_StudyDay")
          }
          studyweek_column<-stringr::str_replace(studyday_column, "_StudyDay", "_StudyWeek")
          return_tb <- return_tb %>%
            dplyr::mutate(!!studyday_column :=
                            ifelse(
                              !is.na(.data[[colnames(FirstDose_Cohort_df)[i]]]) &
                                !is.na(.data$FirstDoseDate),
                              as.integer(difftime(.data[[colnames(FirstDose_Cohort_df)[i]]],
                                                  .data$FirstDoseDate,
                                                  units = "days")),
                              NA_integer_)) %>%
            dplyr::mutate(!!studyweek_column :=
                            ifelse(
                              !is.na(.data[[colnames(FirstDose_Cohort_df)[i]]]) &
                                !is.na(.data$FirstDoseDate),
                              as.integer(difftime(.data[[colnames(FirstDose_Cohort_df)[i]]],
                                                  .data$FirstDoseDate,
                                                  units = "weeks")),
                              NA_integer_))
        }
        else if ( colnames(FirstDose_Cohort_df)[i] %in%  exact_exclusions)
        {
          return_tb[colnames(FirstDose_Cohort_df)[i]]<- NULL
        }
        else if (any(grep_exclusions %>% purrr::map(function(x) grepl(x, colnames(FirstDose_Cohort_df)[i]))==TRUE))
        {
          if ( !grepl("\\sDay$", colnames(FirstDose_Cohort_df)[i]) & crfname !="PatientProfile") # not remove 'EOT Day' and 'EOS Day'
          {
            if ( !(grepl("_STD", colnames(FirstDose_Cohort_df)[i]) & grepl("^vs[1-9]*", crfname))) # not remove '_STD' from Vitals
            {
              return_tb[colnames(FirstDose_Cohort_df)[i]]<- NULL
            }
          }
        }
      }

      if(crfname!="SubjectInfo")
      {
        return_tb <-return_tb %>%  dplyr::select(-.data$FirstDoseDate)
        log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",crfname," - process ended."))
      }

      if (sourcetype=="analysis" & crfname %in% c("ae","cm","waterfall_spider_tb","tl_tb","rs_tb")){
        #ae,cm etc not gone through raiseLabels yet
        return_tb <- GSDSUtilities::raiseLabels(return_tb, "label", isNullC = NA)
      }
      else {
        return_tb<-return_tb
      }

    }
    else
    {
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"| ", names(alldatasetsList[.x]),"- has no data"))
    }
  }) %>% purrr::set_names(nm = names(alldatasetsList))
  # exclude null tibble from the output
  StudyDay_Cohort_added_tb <- StudyDay_Cohort_added_tb[which(!sapply(StudyDay_Cohort_added_tb, is.null))]
  return(StudyDay_Cohort_added_tb)
}
