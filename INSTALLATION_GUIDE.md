# MDR R Package Generator - Installation Guide

## 🎯 Overview

The MDR R Package Generator is now available as a desktop application! This guide will help you set up and distribute the app to end users.

## 🔧 For Developers: Building the Desktop App

### Prerequisites

1. **Node.js** (version 16 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **Python** (version 3.8 or higher)
   - Download from: https://python.org/
   - Verify installation: `python --version`

3. **Git** (optional, for version control)
   - Download from: https://git-scm.com/

### Step 1: Setup Development Environment

```bash
# Clone or navigate to your project directory
cd path/to/your/project

# Install Node.js dependencies
npm install

# Set up development environment (creates Python virtual environment)
npm run setup-dev
```

### Step 2: Test in Development Mode

```bash
# Start the app in development mode
npm run dev
```

This will:
- Create a Python virtual environment
- Install Python dependencies
- Start the Streamlit server
- Launch the Electron app

### Step 3: Build for Distribution

```bash
# Build for your current platform
npm run build

# Or build for specific platforms
npm run build-win     # Windows
npm run build-mac     # macOS  
npm run build-linux   # Linux
npm run build-all     # All platforms
```

### Step 4: Add App Icons (Important!)

Before building for distribution, add proper app icons:

1. Create or obtain high-quality icons (1024x1024 PNG recommended)
2. Place the following files in `build-resources/`:
   - `icon.ico` (Windows)
   - `icon.icns` (macOS)
   - `icon.png` (Linux)

See `build-resources/README.md` for detailed icon requirements.

### Step 5: Customize App Information

Edit `package.json` to customize:

```json
{
  "name": "mdr-r-package-generator",
  "description": "Your custom description",
  "author": {
    "name": "Your Organization",
    "email": "<EMAIL>"
  },
  "build": {
    "appId": "com.yourorg.mdr-r-package-generator",
    "productName": "MDR R Package Generator"
  }
}
```

## 📦 For End Users: Installing the Desktop App

### Windows Installation

1. **Download** the installer:
   - `MDR R Package Generator Setup.exe` (recommended)
   - Or `MDR R Package Generator.exe` (portable version)

2. **Run the installer**:
   - Double-click the setup file
   - Follow the installation wizard
   - Choose installation directory (optional)

3. **Launch the app**:
   - Find "MDR R Package Generator" in Start Menu
   - Or double-click the desktop shortcut

### macOS Installation

1. **Download** the installer:
   - `MDR R Package Generator.dmg`

2. **Install the app**:
   - Double-click the DMG file
   - Drag the app to Applications folder
   - Eject the DMG

3. **Launch the app**:
   - Open Applications folder
   - Double-click "MDR R Package Generator"
   - If prompted about security, go to System Preferences > Security & Privacy > Allow

### Linux Installation

#### AppImage (Recommended)
1. **Download**: `MDR R Package Generator.AppImage`
2. **Make executable**: `chmod +x "MDR R Package Generator.AppImage"`
3. **Run**: `./MDR\ R\ Package\ Generator.AppImage`

#### DEB Package (Ubuntu/Debian)
1. **Download**: `mdr-r-package-generator.deb`
2. **Install**: `sudo dpkg -i mdr-r-package-generator.deb`
3. **Fix dependencies** (if needed): `sudo apt-get install -f`
4. **Launch**: Find in applications menu or run `mdr-r-package-generator`

## 🚀 Using the Desktop App

### First Launch
1. The app will show a splash screen while starting
2. Python environment is automatically set up
3. Streamlit server starts automatically
4. Main application window opens

### Features
- **No browser needed**: Runs in its own window
- **Auto-start**: Everything starts automatically
- **File handling**: Drag and drop support
- **Keyboard shortcuts**: Standard desktop shortcuts
- **Zoom controls**: Ctrl/Cmd + Plus/Minus
- **Developer tools**: Ctrl/Cmd + Shift + I (if needed)

### Troubleshooting

#### App Won't Start
1. **Check system requirements**:
   - Windows 10+ / macOS 10.14+ / Ubuntu 18.04+
   - 4GB RAM minimum, 8GB recommended

2. **Check logs**:
   - Look for `streamlit_server.log` in the app directory
   - Check console output if running from terminal

3. **Reinstall**:
   - Uninstall the current version
   - Download and install the latest version

#### Performance Issues
1. **Close other applications** to free up memory
2. **Check available disk space** (500MB minimum)
3. **Restart the app** if it becomes unresponsive

#### Python/R Issues
- The app includes its own Python environment
- No need to install Python separately
- R is not included - install R separately if needed for generated scripts

## 🔄 Updates

### For Developers
1. Update version in `package.json`
2. Rebuild: `npm run build`
3. Distribute new installers

### For End Users
- Download and install the new version
- Previous settings and data are preserved
- Uninstall old version if prompted

## 📋 System Requirements

### Minimum Requirements
- **OS**: Windows 10, macOS 10.14, or Ubuntu 18.04
- **RAM**: 4GB
- **Storage**: 500MB free space
- **Display**: 1024x768 resolution

### Recommended Requirements
- **OS**: Latest version of Windows 11, macOS, or Ubuntu
- **RAM**: 8GB or more
- **Storage**: 2GB free space
- **Display**: 1920x1080 resolution

## 🆘 Support

### For Users
1. Check this installation guide
2. Look for error messages in the app
3. Contact your system administrator
4. Report issues to the development team

### For Developers
1. Check `ELECTRON_README.md` for technical details
2. Review build logs for errors
3. Test on target platforms before distribution
4. Use development mode for debugging

## 📝 Distribution Checklist

Before distributing to end users:

- [ ] App icons added to `build-resources/`
- [ ] App information updated in `package.json`
- [ ] Tested on target platforms
- [ ] Installation tested by non-technical users
- [ ] Documentation updated
- [ ] Version number incremented
- [ ] Release notes prepared

## 🎉 Success!

Your MDR R Package Generator is now ready for desktop distribution! Users can install and run it like any other desktop application, without needing to know about Python, Streamlit, or web browsers.
