library(shiny)
library(shinyWidgets)
library(readxl)
library(openxlsx)
library(dplyr)
library(magrittr)
library(stringr)
library(tidyr)

# Define server logic
server <- function(input, output) {
  # Create a reactive expression to generate the config combo
  config_combo <- eventReactive(input$generate, {
    # Check if a file has been uploaded
    if (is.null(input$file)) {
      return("No file uploaded")
    }

    # Read the uploaded file
    file <- input$file
    file_path <- file$datapath

    # Check if the necessary sheets exist
    necessary_sheets <- c("Fields", "DataDictionaryEntries", "Forms", "CRFDraft", "Folders")
    sheets <- excel_sheets(file_path)
    missing_sheets <- setdiff(necessary_sheets, sheets)
    if (length(missing_sheets) > 0) {
      return(paste("The following sheets are missing: ", paste(missing_sheets, collapse = ", ")))
    }

    # Process the data
    withProgress(
      expr = {
        # Read in ALS file
        incProgress(0.05, detail = "Reading Fields sheet...")
        ALS_field <- read_excel(file_path, sheet = "Fields")

        incProgress(0.05, detail = "Reading DataDictionaryEntries sheet...")
        ALS_dataDic <- read_excel(file_path, sheet = "DataDictionaryEntries")

        incProgress(0.05, detail = "Reading Forms sheet...")
        ALS_form <- read_excel(file_path, sheet = "Forms")

        incProgress(0.05, detail = "Reading CRFDraft sheet...")
        ALS_project <- read_excel(file_path, sheet = "CRFDraft")

        incProgress(0.05, detail = "Reading Folders sheet...")
        ALS_folders <- read_excel(file_path, sheet = "Folders")

        # Your processing code here
        incProgress(0.1, detail = "Processing data...")
        StudyName <- str_remove_all(paste(input$Blind,"_", str_replace_all(tolower(first(ALS_project$ProjectName)),"-","_") )," ")


        variable_label_tab <- ALS_form %>%
          dplyr::select(.data$OID, .data$DraftFormName) %>%
          dplyr::filter(!is.na(.data$OID)) %>%
          dplyr::left_join(ALS_field %>% select(.data$FormOID, .data$FieldOID, .data$SASLabel, .data$DataDictionaryName), by = c("OID" = "FormOID")) %>%
          dplyr::mutate(OID = tolower(.data$OID))

        Variable_value_tab <- ALS_dataDic %>%
          dplyr::select(.data$DataDictionaryName, .data$UserDataString) %>%
          dplyr::filter(!is.na(.data$DataDictionaryName))

        # map variableName, labelName and value
        var_label_value <- Variable_value_tab %>%
          dplyr::full_join(variable_label_tab, by = "DataDictionaryName") %>%
          dplyr::arrange(.data$OID, .data$FieldOID) %>%
          dplyr::select(-.data$DataDictionaryName)

        DA_ALS <- var_label_value %>%
          dplyr::mutate(study_name=StudyName) %>%
          dplyr::filter(grepl("RECIST|Enrollment",.data$DraftFormName,ignore.case=T)) %>%
          dplyr::mutate(r_name = case_when(grepl("Time-point Response Assessment", .data$DraftFormName, ignore.case=T) ~
                                             "rs_tb",

                                           grepl("Non-Target Lesions", .data$DraftFormName, ignore.case=T) ~
                                             "ntl_tb",

                                           grepl("Target Lesions", .data$DraftFormName, ignore.case = T) ~
                                             "tl_tb",

                                           grepl("New Lesions", .data$DraftFormName, ignore.case= T)~
                                             "nl_tb",

                                           grepl("Enrollment", .data$DraftFormName, ignore.case = T) ~
                                             "enr_tb",

                                           TRUE ~ NA_character_
          )) %>%
          dplyr::filter(!is.na(.data$r_name)) %>%
          dplyr::mutate(r_col_name = case_when(grepl("Date",.data$SASLabel, ignore.case=T)&.data$r_name == "rs_tb" ~
                                                 "rs_date",

                                               grepl("^Overall response", .data$SASLabel, ignore.case=T)&.data$r_name=="rs_tb" ~
                                                 "rs_overall_response",

                                               grepl("Evaluation of target lesions", .data$SASLabel, ignore.case =T)&.data$r_name =="rs_tb" ~
                                                 "rs_tl_response",

                                               grepl("Evaluation of non-target lesions" ,.data$SASLabel, ignore.case = T)&.data$r_name =="rs_tb" ~
                                                 "rs_ntl_response",

                                               grepl("New lesion*", .data$SASLabel, ignore.case = T)&.data$r_name =="rs_tb" ~
                                                 "rs_nl_response",

                                               #For most studies, this will be the target lesion diameter sum column. For prostate cancer studies, this will be the target lesion maximum diameter column.
                                               grepl("Sum|Max*)", .data$SASLabel, ignore.case = T)&.data$r_name =="tl_tb" ~
                                                 "tl_diam_metric",

                                               grepl("Evaluated: Diameter measurement",.data$SASLabel, ignore.case = T)&.data$r_name=="tl_tb" ~
                                                 "tl_diam",

                                               grepl("Diameter lesion [A-Za-z]",.data$SASLabel, ignore.case=T)&.data$r_name=="tl_tb" ~
                                                  str_remove_all(paste("tl_split_",tolower(sub("Diameter lesion ([A-Za-z])", "\\1", .data$SASLabel)),"_diam"), " "),

                                               grepl("Coalesced, lesion diameter", .data$SASLabel, ignore.case = T)&.data$r_name=="tl_tb" ~
                                                 "tl_coalesce_diam",

                                               grepl("Too small to measure", .data$SASLabel, ignore.case=T)&.data$r_name =="tl_tb" ~
                                                 "tl_too_small_diam",

                                               grepl("Date of procedure" ,.data$SASLabel, ignore.case=T)&.data$r_name=="tl_tb" ~
                                                 "tl_date",

                                               grepl("Location",.data$SASLabel, ignore.case =T)&.data$r_name=="tl_tb" ~
                                                 "tl_location",

                                               grepl("Lesion status", .data$SASLabel, ignore.case = T)&.data$r_name =="tl_tb" ~
                                                 "tl_status",

                                               grepl("Target lesion number", .data$SASLabel, ignore.case = T)&.data$r_name=="tl_tb" ~
                                                 "tl_id",

                                               grepl("Lesion status", .data$SASLabel, ignore.case = T)&.data$r_name =="ntl_tb" ~
                                                 "ntl_response",

                                               grepl("Date of procedure", .data$SASLabel, ignore.case = T)&.data$r_name =="ntl_tb" ~
                                                 "ntl_date",

                                               grepl("Location",.data$SASLabel,ignore.case =T)&.data$r_name=="ntl_tb" ~
                                                 "ntl_location",

                                               grepl("Were there new lesions to report", .data$SASLabel, ignore.case = T)&.data$r_name=="nl_tb" ~
                                                 "nl_yn",

                                               grepl("Date of enrollment", .data$SASLabel, ignore.case =T)&.data$r_name=="enr_tb" ~
                                                 "enr_date",

                                               TRUE ~NA_character_)) %>%
          dplyr::filter(!is.na(.data$r_col_name)) %>%

          dplyr::mutate(crf_col_type = case_when(grepl("^tl(_.*_diam|_diam)|tl_id|tl_diam_metric", .data$r_col_name) ~"numeric",

                                                 grepl("Date",.data$SASLabel, ignore.case =T) ~"date",

                                                 TRUE ~ "character")) %>%


          dplyr::mutate(r_col_val_name = case_when(grepl("Absent",.data$UserDataString,ignore.case = T) ~"Absent",

                                                   grepl("Coalesced" ,.data$UserDataString,ignore.case = T) ~"cl",

                                                   grepl("Complete",.data$UserDataString, ignore.case = T) ~"CR",

                                                   grepl("^Evaluated", .data$UserDataString, ignore.case = T) ~"el",

                                                   grepl("No longer visible",.data$UserDataString, ignore.case = T) ~"invisible",

                                                   grepl("Lymph",.data$UserDataString, ignore.case = T) ~"lymph",

                                                   grepl("Not Applicable",.data$UserDataString, ignore.case = T) ~"NA",

                                                   grepl("Not.*Evalua|not done",.data$UserDataString , ignore.case = T) ~"NE",
                                                   #
                                                   # .data$UserDataString %in% c("Not Evaluable","Not Evaluated","Not all Evaluated")
                                                   grepl("No",.data$UserDataString,ignore.case = T)&.data$r_col_name%in%c("rs_nl_response","nl_yn") ~"No",

                                                   grepl("Yes" ,.data$UserDataString,ignore.case = T) ~"Yes",

                                                   grepl("Partial", .data$UserDataString, ignore.case = T) ~"PR",

                                                   grepl("Stable" ,.data$UserDataString, ignore.case = T) ~"SD",

                                                   grepl("Non-CR", .data$UserDataString, ignore.case=T)  ~"Non-CR/Non-PD",

                                                   grepl("Unequivocal Progression",.data$UserDataString, ignore.case=T) ~"Unequivocal Progression",

                                                   grepl("Progress",.data$UserDataString, ignore.case = T)  ~ "PD",

                                                   grepl("Present",.data$UserDataString, ignore.case = T) ~"Present",

                                                   grepl("Split", .data$UserDataString, ignore.case = T) ~"sp",

                                                   grepl("Unknown",.data$UserDataString, ignore.case = T) ~"un",

                                                   grepl("Too small to measure" , .data$UserDataString, ignore.case = T) ~ "tstm",

                                                   TRUE ~ NA_character_
          )) %>%
          dplyr::mutate(Flag_for_lymph = ifelse(grepl("Location",.data$SASLabel,ignore.case=T) &!grepl("Lymph",.data$UserDataString, ignore.case=T),1,0)) %>%
          dplyr::filter(Flag_for_lymph!=1) %>%
          dplyr::rename(  "crf_col_val_name" = "UserDataString",
                          "crf_name" = "OID",
                          "crf_col_name" = "FieldOID",
                          "crf_col_desc" = "SASLabel") %>%
          dplyr::mutate(crf_col_name = ifelse(grepl("Date", .data$crf_col_desc, ignore.case=T)&.data$r_name!="subject_info",
                                              str_remove_all(paste(.data$crf_col_name, "_INT")," "),
                                              .data$crf_col_name)) %>%
          dplyr::select(.data$study_name,.data$r_name,.data$crf_name,.data$r_col_name,.data$crf_col_name,.data$crf_col_type,.data$r_col_val_name,.data$crf_col_val_name,.data$crf_col_desc)

        # add non-ALS information
        config_systemVar<-c("subj_id","instance_id","instance_name")
        crf_col<-c("Subject","instanceId","InstanceName")
        type_systemVar<-c("character","numeric","character")
        col_des<-c("Unique subject identifier",
                   "Unique folder instance identifier",
                   "Name of the folder instance")

        config_system<-tibble(study_name = StudyName,
                              r_name = rep(unique(DA_ALS$r_name), each =3),
                              crf_name = rep(unique(DA_ALS$crf_name),each = 3),
                              r_col_name = rep(config_systemVar,length(unique(DA_ALS$crf_name))),
                              crf_col_name = rep(crf_col, length(unique(DA_ALS$crf_name))),
                              crf_col_type = rep(type_systemVar ,length(unique(DA_ALS$crf_name))),
                              r_col_val_name = NA_character_,
                              crf_col_val_name = NA_character_,
                              crf_col_desc = rep(col_des, length(unique(DA_ALS$crf_name))))
        #Capture active treatment from StudyName
        activeTreatment<-str_split(first(ALS_project$ProjectName),"-\\d+$")

        # add subjectInfo table
        SubjectInfo_tab<- tibble(study_name=StudyName,
                                 r_name = "subject_info",
                                 crf_name = "SubjectInfo",
                                 r_col_name = c("subj_id", "first_dose_date", "last_dose_date","eos_date","eot_date","eot_reason","death_date","eos_reason","death_cause"),
                                 crf_col_name = c("Subject name or identifier","FirstDoseDate","LastDoseDate","EOS Date",paste0("EOT Date -", activeTreatment[[1]][1]), paste0("EOT Reason -", activeTreatment[[1]][1]),
                                                  "Death Date","EOS Reason","Death Cause"),
                                 crf_col_desc = c("Unique subject identifier",
                                                  "Calculated first dose date",
                                                  "Calculated last dose date",
                                                  "End of Study Date",
                                                  "End of Treatment Date",
                                                  "Death date",
                                                  "End of Treatment Reason",
                                                  "End of Study Reason",
                                                  "cause of death"),
                                 crf_col_val_name = NA_character_,
                                 r_col_val_name = NA_character_,
        ) %>%
          dplyr::mutate(crf_col_type = ifelse(grepl("Date",.data$crf_col_name, ignore.case=T), "date","character"))


        Config_DA_crf <-rbind(DA_ALS, config_system, SubjectInfo_tab) %>%
          dplyr::mutate(flag_for_enr=ifelse(.data$r_name=="enr_tb"&.data$r_col_name%in%c("instance_id","instance_name"),1,0)) %>%
          dplyr::filter(.data$flag_for_enr!=1) %>%
          dplyr::select(-.data$flag_for_enr) %>%
          dplyr::arrange(.data$r_name)


        # write out the da_crf_config
        # write.csv(Config_DA_crf, "da_crf_config")

        # Prepare for da_meta_config
        RSFolderName <- ALS_folders$FolderName[grep("Response Assessment",ALS_folders$FolderName,ignore.case = T)]
        
        # Extract unscheduled pattern
        unscheduled_grep <- ifelse(
          any(grepl("unscheduled", RSFolderName, ignore.case = T)),
          paste(unique(gsub(".*?(unscheduled.*?)(?=\\s|$).*", "\\1", RSFolderName[grep("unscheduled", RSFolderName, ignore.case = T)], perl=TRUE)), collapse="|"),
          NA_character_
        )
        
        # Extract EOT pattern
        eot_grep <- ifelse(
          any(grepl("end of treatment|eot", RSFolderName, ignore.case = T)),
          paste(unique(gsub(".*?(end of treatment|eot.*?)(?=\\s|$).*", "\\1", RSFolderName[grep("end of treatment|eot", RSFolderName, ignore.case = T)], perl=TRUE)), collapse="|"),
          ifelse(
            any(grepl("end of treatment|eot", ALS_folders$FolderName, ignore.case = T)),
            paste(unique(gsub(".*?(end of treatment|eot.*?)(?=\\s|$).*", "\\1", ALS_folders$FolderName[grep("end of treatment|eot", ALS_folders$FolderName, ignore.case = T)], perl=TRUE)), collapse="|"),
            NA_character_
          )
        )
        
        # Extract baseline visit pattern
        baseline_visit_name <- ifelse(
          any(grepl("screening|baseline", RSFolderName, ignore.case = T)),
          paste(unique(gsub(".*?(screening|baseline.*?)(?=\\s|$).*", "\\1", RSFolderName[grep("screening|baseline", RSFolderName, ignore.case = T)], perl=TRUE)), collapse="|"),
          NA_character_
        )
        
        # Extract scheduled visit pattern
        scheduled_grep <- case_when(
          grepl("C[[:digit:]]+D[[:digit:]]+|cycle[[:digit:]]+day[[:digit:]]+|W[[:digit:]]+D[[:digit:]]+|week[[:digit:]]+day[[:digit:]]+", RSFolderName, ignore.case = TRUE) ~
            paste(unique(gsub(".*?(C[[:digit:]]+D[[:digit:]]+|cycle[[:digit:]]+day[[:digit:]]+|W[[:digit:]]+D[[:digit:]]+|week[[:digit:]]+day[[:digit:]]+).*", "\\1", RSFolderName, perl=TRUE)), collapse="|"),
          grepl("week(s)?|cycle", RSFolderName, ignore.case = TRUE) ~
            paste(unique(gsub(".*?(week(s)?|cycle).*", "\\1", RSFolderName, perl=TRUE)), collapse="|"),
          grepl("after", RSFolderName, ignore.case = TRUE) ~ "(a|A)fter",
          TRUE ~ NA_character_
        )
        
        Config_DA_meta <- tibble(
          study_name = StudyName,
          config_name = c("lesion_metric", "anchor_type", "study_unit", "unscheduled_grep", "eot_grep", "scheduled_grep", "baseline_visit_name"),
          config_value = c(input$lesion_metric, input$anchor_type, input$study_unit, unscheduled_grep, eot_grep, scheduled_grep, baseline_visit_name)
        )
        #write out the da_meta_config
        # write.csv(Config_DA_meta, "da_meta_config")
        # prepare for vs_config
        ## get info from ALS only
        vs_crf_config_ALS<-var_label_value %>%
          dplyr::filter(grepl("Vital Sign.*|ECG|Weight",.data$DraftFormName)&!grepl("NotinUse",.data$DraftFormName)) %>%
          dplyr::filter(!grepl("Central",.data$DraftFormName)) %>%

          dplyr::mutate(study_name = StudyName) %>%

          dplyr::mutate(r_col_name = case_when(
            grepl("Were vital signs taken?",.data$SASLabel,ignore.case=T)~
              "VSYN",

            grepl("Date",.data$SASLabel,ignore.case=T)~
              "TESTDAT_INT",

            grepl("Temperature" ,.data$SASLabel, ignore.case=T)~
              "temp_stdvalue",

            grepl("weight",.data$SASLabel,ignore.case=T)~
              "weight_stdvalue",

            grepl("Systolic blood pressure",.data$SASLabel, ignore.case=T)~
              "SystolicBP_stdvalue",

            grepl("Diastolic blood pressure",.data$SASLabel, ignore.case=T)~
              "DiastolicBP_stdvalue",

            grepl("Time point", .data$SASLabel, ignore.case=T)&grepl("Vital Sign.*",.data$DraftFormName)~
              "Timepoint",

            grepl("Was an ECG performed?" ,.data$SASLabel, ignore.case =T)~
              "EGPERF",

            grepl("QTcF" ,.data$SASLabel ,ignore.case=T) ~
              "stdvalue",

            grepl("Was weight.* taken?" ,.data$SASLabel,ignore.case=T)~
              "WEYN",

            TRUE~NA_character_)) %>%

          dplyr::filter(!is.na(.data$r_col_name)) %>%
          dplyr::filter(!grepl("Weight",.data$DraftFormName,ignore.case=T)&!grepl("Date of weight.*",.data$SASLabel,ignore.case=T)) %>%
          dplyr::filter(!grepl("RSG",.data$FieldOID)) %>%
          dplyr::select(-.data$DraftFormName) %>%
          dplyr::rename("crf_name" = "OID",
                        "crf_col_name" = "FieldOID",
                        "crf_col_desc" = "SASLabel",
                        "crf_col_val_name" = "UserDataString") %>%
          dplyr::mutate(crf_col_val_name = ifelse(!is.na(.data$crf_col_val_name )&!grepl("^(Yes|No)",.data$crf_col_val_name, ignore.case=T),NA_character_, .data$crf_col_val_name)) %>%
          dplyr::distinct() %>%
          dplyr::mutate(crf_col_name = case_when(grepl("Date",.data$crf_col_desc, ignore.case=T)~
                                                   str_remove_all(paste(.data$crf_col_name, "_INT")," "),

                                                 grepl("^(Was|Were)" ,.data$crf_col_desc, ignore.case=T) ~
                                                   str_remove_all(paste(.data$crf_col_name, "_STD")," "),

                                                 grepl("Time Point", .data$crf_col_desc, ignore.case=T) ~
                                                   str_remove_all(paste(.data$crf_col_name, "_STD")," ") ,

                                                 grepl("Temperature" ,.data$crf_col_desc, ignore.case=T) ~
                                                   str_remove_all(paste(.data$crf_col_name, "_STD")," ") ,

                                                 grepl("Weight" ,.data$crf_col_desc, ignore.case=T) ~
                                                   str_remove_all(paste(.data$crf_col_name, "_STD")," ") ,


                                                 TRUE ~ .data$crf_col_name)) %>%
          dplyr::mutate(r_col_val_name = case_when(grepl("^Yes" ,.data$crf_col_val_name, ignore.case=T) ~
                                                     "Yes",

                                                   grepl("^No" ,.data$crf_col_val_name, ignore.case=T)~
                                                     "No",
                                                   TRUE~ NA_character_))

        ## add in EDC and R package specific var including subject, instanceid, instanceName, LastChangeDate, WhatChanged
        config_systemVar_vs<-c("Subject","instanceId","InstanceName","RecordDate","LastChangeDate","WhatChanged")
        col_des_vs<-c("Unique subject identifier",
                      "Unique folder instance identifier",
                      "Name of the folder instance",
                      "RecordDate",
                      "LastChangeDate",
                      "WhatChanged")
        config_system_vs<-tibble(study_name = StudyName,
                                 #r_name = rep(unique(vs_crf_config_ALS$r_name), each =3),
                                 crf_name = rep(unique(vs_crf_config_ALS$crf_name),each = 6),
                                 r_col_name = rep(config_systemVar_vs,length(unique(vs_crf_config_ALS$crf_name))),
                                 crf_col_name = rep(config_systemVar_vs, length(unique(vs_crf_config_ALS$crf_name))),
                                 #crf_col_type = rep(crf_col ,length(unique(vs_crf_config_ALS$crf_name))),
                                 r_col_val_name = NA_character_,
                                 crf_col_val_name = NA_character_,
                                 crf_col_desc = rep(col_des_vs, length(unique(vs_crf_config_ALS$crf_name))))
        ##generate tab with temp_unit and weight_unit
        forms_with_unit<-vs_crf_config_ALS %>%
          dplyr::filter(grepl("^vs" , .data$crf_name ,ignore.case=T)&grepl("Temperature|Weight", .data$crf_col_desc, ignore.case=T)) %>%
          dplyr::mutate(temp_unit = ifelse(grepl("Temperature", .data$crf_col_desc,ignore.case=T),
                                            str_remove_all(paste(.data$crf_col_name, "_UN")," "),
                                            NA_character_),
                        weight_unit = ifelse(grepl("Weight", .data$crf_col_desc,ignore.case=T),
                                             str_remove_all(paste(.data$crf_col_name, "_UN")," "),
                                             NA_character_)) %>%
          dplyr::select(.data$crf_name,.data$temp_unit,.data$weight_unit) %>%
          tidyr::pivot_longer(cols = ends_with("_unit"), names_to = "r_col_name", values_to = "crf_col_name") %>%
          dplyr::filter(!is.na(.data$crf_col_name)) %>%
          dplyr::mutate(study_name =StudyName,
                        r_col_val_name = NA_character_,
                        crf_col_val_name = NA_character_,
                        crf_col_desc = rep(c("Standard unit of temperature", "Standard unit of weight"), length(unique(.data$crf_name))))


        # generate r_name for vs_config
        r_name_vs_gen<- vs_crf_config_ALS %>%
          dplyr::select(.data$crf_name) %>%
          dplyr::distinct() %>%
          dplyr::mutate(category = ifelse(grepl("vs",.data$crf_name), "vs","eg")) %>%
          dplyr::group_by(.data$category) %>%
          dplyr::mutate(index = row_number(.data$crf_name)) %>%
          dplyr::mutate(r_name= str_remove_all(paste(.data$category,.data$index)," ") ) %>%
          dplyr::ungroup() %>%
          dplyr::select(.data$crf_name, .data$r_name)


        vs_crf_config <- rbind(config_system_vs,vs_crf_config_ALS,forms_with_unit) %>%
          dplyr::mutate(crf_col_type = case_when(grepl("Date",.data$crf_col_desc, ignore.case=T) ~
                                                   "date",
                                                 grepl("stdvalue|instanceId" ,.data$r_col_name, ignore.case=T) ~
                                                   "numeric",
                                                 TRUE ~ "character")) %>%
          dplyr::arrange(.data$crf_name,.data$r_col_name) %>%
          dplyr::left_join(r_name_vs_gen,by = "crf_name") %>%
          dplyr::select(.data$study_name, .data$r_name,.data$crf_name, .data$r_col_name, .data$crf_col_name, .data$crf_col_type,
                        .data$r_col_val_name,.data$crf_col_val_name, .data$crf_col_desc) %>%
          dplyr::distinct() %>%
          dplyr::filter(!is.na(.data$r_col_name)) %>%
          dplyr::filter(!is.na(.data$crf_col_name))


        ### prepare for Spotfire CRFGlossary
        CRFGlossary<- var_label_value %>%
          dplyr::select(.data$OID,.data$DraftFormName) %>%
          dplyr::distinct() %>%
          dplyr::rename("AbbreviatedCRFName" = "OID",
                        "CRFname" ="DraftFormName") %>%
          dplyr::filter(!is.na(.data$AbbreviatedCRFName)) %>%
          dplyr::add_row("AbbreviatedCRFName" = "dose_merged", "CRFname" = "Merged dosing forms") %>%
          dplyr::add_row("AbbreviatedCRFName" = "subjectinfo", "CRFname" = "Important info about subjects") %>%
          dplyr::add_row("AbbreviatedCRFName" = "td_merged", "CRFname" = "Merged EOT forms") %>%
          dplyr::add_row("AbbreviatedCRFName" = "vs (merged)", "CRFname" = "Merged vitals forms") %>%
          dplyr::add_row("AbbreviatedCRFName" = "ecg (merged)", "CRFname" = "Merged ecg forms") %>%
          dplyr::add_row("AbbreviatedCRFName" = "lab", "CRFname" = "Merged edc and central labs") %>%
          dplyr::add_row("AbbreviatedCRFName" = "ae", "CRFname" = "Adverse Event(Analysis)")


        incProgress(0.1, detail = "Generating da_crf_config...")
        # Create a new workbook
        wb <- createWorkbook()

        addWorksheet(wb, "Variable mapping table")
        writeData(wb, "Variable mapping table", var_label_value)

        addWorksheet(wb, "da_crf_config")
        writeData(wb, "da_crf_config", Config_DA_crf)

        incProgress(0.1, detail = "Generating da_meta_config...")
        addWorksheet(wb, "da_meta_config")
        writeData(wb, "da_meta_config", Config_DA_meta)

        incProgress(0.1, detail = "Generating vs_crf_config...")
        addWorksheet(wb, "vs_crf_config")
        writeData(wb, "vs_crf_config", vs_crf_config)

        incProgress(0.1, detail = "Generating CrfGlossary4Spotfire...")
        addWorksheet(wb, "CrfGlossary4Spotfire")
        writeData(wb, "CrfGlossary4Spotfire", CRFGlossary)

        # Save the workbook to an .xlsx file
        incProgress(0.1, detail = "Saving config combo...")
        saveWorkbook(wb, paste("/usrfiles/spotfire/MDR_Auto/Config/config_combo_", StudyName, ".xlsx"), overwrite = TRUE)

        # Return a success message
        return(paste0("Config combo generated successfully and you can find it under the path: /usrfiles/spotfire/MDR_Auto/Config/config_combo_", StudyName, ".xlsx"))
      },

      detail = "Please wait..."
    )
  })

  # Show the status of the config combo generation
  output$status <- renderText({
    config_combo()
  })

  # Show any errors
  output$error <- renderText({
    if (is.null(input$file)) {
      "No file uploaded"
    } else {
      ""
    }
  })
}

