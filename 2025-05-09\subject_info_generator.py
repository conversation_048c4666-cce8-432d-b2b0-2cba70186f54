import pandas as pd
import re
from typing import Dict, List, Tuple

class SubjectInfoGenerator:
    def __init__(self, als_file):
        """Initialize the SubjectInfoGenerator with ALS file."""
        self.als_file = als_file
        self.subject_info_df = self._generate_subject_info_mapping()
        self.table_mappings = self._generate_table_mappings()
        
    def _generate_table_mappings(self) -> Dict[str, str]:
        """Generate mappings between standard table names and actual table names from ALS."""
        try:
            # Read ALS file sheets
            fields_df = pd.read_excel(self.als_file, sheet_name="Fields", engine='openpyxl')
            forms_df = pd.read_excel(self.als_file, sheet_name="Forms", engine='openpyxl')
            
            # Process forms to get table mappings
            forms_df = forms_df[['OID', 'DraftFormName']].copy()
            
            # Map standard names to actual form names and convert to lowercase
            table_mappings = {
                'ds_enr': forms_df[forms_df['DraftFormName'].str.contains('Enrollment', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Enrollment', case=False, na=False)].empty else 'ds_enr',
                'subject': forms_df[forms_df['DraftFormName'].str.contains('Subject', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Subject', case=False, na=False)].empty else 'subject',
                'sd': forms_df[forms_df['DraftFormName'].str.contains('End of Study', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('End of Study', case=False, na=False)].empty else 'sd',
                'dd': forms_df[forms_df['DraftFormName'].str.contains('Death', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Death', case=False, na=False)].empty else 'dd',
                'dm': forms_df[forms_df['DraftFormName'].str.contains('Demographics', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Demographics', case=False, na=False)].empty else 'dm',
                'rs_r': forms_df[forms_df['DraftFormName'].str.contains('Disease Assessment|Time-point Response Assessment', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Disease Assessment|Time-point Response Assessment', case=False, na=False)].empty else 'rs_r',
                'mh_dx': forms_df[forms_df['DraftFormName'].str.contains('Disease History', case=False, na=False)]['OID'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Disease History', case=False, na=False)].empty else 'mh_dx'
            }
            
            return table_mappings
            
        except Exception as e:
            raise Exception(f"Error generating table mappings: {str(e)}")
        
    def _generate_subject_info_mapping(self):
        """Generate subject info mapping from ALS file."""
        try:
            # Read ALS file sheets
            fields_df = pd.read_excel(self.als_file, sheet_name="Fields", engine='openpyxl')
            forms_df = pd.read_excel(self.als_file, sheet_name="Forms", engine='openpyxl')
            
            # Process fields
            fields_df = fields_df[['FormOID', 'FieldOID', 'SASLabel']].copy()
            fields_df['Form'] = fields_df['FormOID'].str.lower()
            fields_df = fields_df.drop('FormOID', axis=1).dropna()
            
            # Process forms
            forms_df = forms_df[['OID', 'DraftFormName']].copy()
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df.drop('OID', axis=1).dropna()
            
            # Join fields and forms
            var_label_form = fields_df.merge(forms_df, on='Form')
            
            # Generate mappings for each summary table
            enr_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Enrollment', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Was the subject enrolled|^Phase$|^Cohort$|Dose Level', case=False, na=False)
            ].copy()
            enr_summary['part'] = 'enr_summary'
            
            subject_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Subject', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Country', case=False, na=False)
            ].copy()
            subject_summary['part'] = 'subject_summary'
            
            sd_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('End of Study', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Date of subject completion/discontinuation from the study|primary reason|Other', case=False, na=False)
            ].copy()
            sd_summary['part'] = 'sd_summary'
            
            dd_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Death', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Date of death|Cause of death', case=False, na=False)
            ].copy()
            dd_summary['part'] = 'dd_summary'
            
            dm_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Demographics', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Age|Sex|child-bearing potential|Ethnicity|American Indian or Alaska Native|^Asian$|Black or African American|Native Hawaiian or other Pacific Islander|^White$|^Other$|Not reported|Unknown', case=False, na=False)
            ].copy()
            dm_summary['part'] = 'dm_summary'
            
            rsrc_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Disease Assessment|Time-point Response Assessment', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Date of .*response', case=False, na=False)
            ].copy()
            rsrc_summary['part'] = 'rsrc_summary'
            
            candig_summary = var_label_form[
                var_label_form['DraftFormName'].str.contains('Disease History', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Medical history term|Type of solid tumor|Disease type at study entry', case=False, na=False)
            ].copy()
            candig_summary['part'] = 'candig_summary'
            
            # Combine all mappings
            subject_info_df = pd.concat([
                enr_summary, subject_summary, sd_summary,
                dd_summary, dm_summary, rsrc_summary, candig_summary
            ], ignore_index=True)
            
            # Add treatment column
            treatments = var_label_form[
                var_label_form['DraftFormName'].str.contains('Study Drug Administration - ', case=False, na=False)
            ]['DraftFormName'].str.replace('Study Drug Administration - ', '').unique()
            
            subject_info_df['treatment'] = treatments[0] if len(treatments) > 0 else None
            
            return subject_info_df
            
        except Exception as e:
            raise Exception(f"Error generating subject info mapping: {str(e)}")
    
    def _get_variable_mapping(self, part: str) -> Dict[str, str]:
        """Get variable mappings for a specific part of the subject info."""
        part_df = self.subject_info_df[self.subject_info_df['part'] == part]
        return dict(zip(part_df['SASLabel'], part_df['FieldOID']))
    
    def generate_function(self, study_id: str) -> str:
        """Generate the subjectInfo R function."""
        try:
            # Get variable mappings for each part
            enr_vars = self._get_variable_mapping('enr_summary')
            subject_vars = self._get_variable_mapping('subject_summary')
            sd_vars = self._get_variable_mapping('sd_summary')
            dd_vars = self._get_variable_mapping('dd_summary')
            dm_vars = self._get_variable_mapping('dm_summary')
            rsrc_vars = self._get_variable_mapping('rsrc_summary')
            candig_vars = self._get_variable_mapping('candig_summary')
            
            # Template for the R function with placeholders
            template = f"""#' @title subjectInfo_{study_id}
#' @description Create a Subject Info file and dataframe
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param dose_merged This the dose_merged dataframe
#' @param {self.table_mappings['ds_enr']} This is the enrollment dataframe
#' @param {self.table_mappings['subject']} This is the subject dataframe
#' @param {self.table_mappings['sd']} This is the study discontinuation dataframe
#' @param {self.table_mappings['dd']} This is the death dataframe
#' @param td_merged This is the td_merged dataframe
#' @param {self.table_mappings['dm']} This is the demographics dataframe
#' @param {self.table_mappings['rs_r']} This is the response assessment dataframe
#' @param {self.table_mappings['mh_dx']} This is the medical history dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return SubjectInfo
#'
#' @export subjectInfo_{study_id}
#'
#' @importFrom dplyr select distinct mutate case_when filter group_by summarise left_join vars mutate_at n_distinct count pull
#' @importFrom GSDSUtilities raiseLabels
#' @importFrom tidyr pivot_wider
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data :=
#' @importFrom lubridate as_datetime
#'
#' @examples
#' \\dontrun{{
#'subjectInfo_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
#'                           tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                           dose_merged = dose_merged, {self.table_mappings['ds_enr']} = {self.table_mappings['ds_enr']}, {self.table_mappings['subject']} = {self.table_mappings['subject']},
#'                           {self.table_mappings['sd']} = {self.table_mappings['sd']}, {self.table_mappings['dd']} = {self.table_mappings['dd']}, td_merged = td_merged, {self.table_mappings['dm']} = {self.table_mappings['dm']},
#'                           {self.table_mappings['rs_r']} = {self.table_mappings['rs_r']}, {self.table_mappings['mh_dx']} = {self.table_mappings['mh_dx']},
#'                           develop.f = develop.f, vpath = vpath)
#' }}
#'
subjectInfo_{study_id} <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime, dose_merged,
                                       {self.table_mappings['ds_enr']}, {self.table_mappings['subject']}, {self.table_mappings['sd']}, {self.table_mappings['dd']}, td_merged, {self.table_mappings['dm']}, {self.table_mappings['rs_r']}, {self.table_mappings['mh_dx']}, develop.f = develop.f, vpath = vpath) {{
  withCallingHandlers(
    expr = {{
      calledFun = "subjectInfo_{study_id}"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert dose_merged has min.rows and min.cols
      checkmate::assert_data_frame(dose_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm dose_merged has min.rows and min.cols."))
      # Assert {self.table_mappings['ds_enr']} has min.rows and min.cols
      checkmate::assert_data_frame({self.table_mappings['ds_enr']}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self.table_mappings['ds_enr']} has min.rows and min.cols."))
      # Assert {self.table_mappings['subject']} has min.rows and min.cols
      checkmate::assert_data_frame({self.table_mappings['subject']}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self.table_mappings['subject']} has min.rows and min.cols."))
      # Assert {self.table_mappings['sd']} has min.rows and min.cols
      checkmate::assert_data_frame({self.table_mappings['sd']}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self.table_mappings['sd']} has min.rows and min.cols."))
      # Assert {self.table_mappings['dd']} has min.rows and min.cols
      checkmate::assert_data_frame({self.table_mappings['dd']}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self.table_mappings['dd']} has min.rows and min.cols."))
      # Assert td_merged has min.rows and min.cols
      checkmate::assert_data_frame(td_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm td_merged has min.rows and min.cols."))
      # Assert {self.table_mappings['dm']} has min.rows and min.cols
      checkmate::assert_data_frame({self.table_mappings['dm']}, min.rows = 1, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self.table_mappings['dm']} has min.rows and min.cols."))
      # Assert {self.table_mappings['rs_r']} has min.rows and min.cols
      checkmate::assert_data_frame({self.table_mappings['rs_r']}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self.table_mappings['rs_r']} has min.rows and min.cols."))
      # Assert {self.table_mappings['mh_dx']} has min.rows and min.cols
      checkmate::assert_data_frame({self.table_mappings['mh_dx']}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {self.table_mappings['mh_dx']} has min.rows and min.cols."))
      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))

      # enr_summary Table -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating enr_summary dataframe"))
      enr_summary <- {self.table_mappings['ds_enr']} %>%
        dplyr::select(.data$Subject, .data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, .data${enr_vars.get('Phase', 'PHASE')}, .data${enr_vars.get('Part', 'PART')}, .data${enr_vars.get('Cohort', 'COHORT')})
      attr(enr_summary${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, "label") <- "Enrolled"
      enr_summary <- GSDSUtilities::raiseLabels(enr_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of enr_summary dataframe"))

      # subject_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating subject_summary dataframe"))
      subject_summary <- {self.table_mappings['subject']} %>%
        dplyr::select(.data$Subject, .data${subject_vars.get('Country', 'COUNTRY')})
      attr(subject_summary${subject_vars.get('Country', 'COUNTRY')}, "label") <- "Country"
      subject_summary <- GSDSUtilities::raiseLabels(subject_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of subject_summary dataframe"))

      # sd_summary Table --------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating sd_summary dataframe"))
      sd_summary <- {self.table_mappings['sd']} %>%
        dplyr::select(.data$Subject, .data${sd_vars.get('Date of subject completion/discontinuation from the study', 'DSSTDAT_INT')}, .data${sd_vars.get('Primary reason for discontinuation', 'DSDECOD')})
      attr(sd_summary${sd_vars.get('Date of subject completion/discontinuation from the study', 'DSSTDAT_INT')}, "label") <- "EOS Date"
      attr(sd_summary${sd_vars.get('Primary reason for discontinuation', 'DSDECOD')}, "label") <- "EOS Reason"
      sd_summary <- GSDSUtilities::raiseLabels(sd_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of sd_summary dataframe"))

      # dd_summary Table --------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dd_summary dataframe"))
      dd_summary <- {self.table_mappings['dd']} %>%
        dplyr::select(.data$Subject, .data${dd_vars.get('Date of death', 'DDDAT2_INT')}, .data${dd_vars.get('Cause of death', 'DDRES')})
      attr(dd_summary${dd_vars.get('Date of death', 'DDDAT2_INT')}, "label") <- "Death Date"
      attr(dd_summary${dd_vars.get('Cause of death', 'DDRES')}, "label") <- "Death Cause"
      dd_summary <- GSDSUtilities::raiseLabels(dd_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of dd_summary dataframe"))

      # dm_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dm_summary dataframe"))
      dm_summary <- {self.table_mappings['dm']} %>%
        dplyr::select(.data$Subject, .data${dm_vars.get('Age', 'AGE')}, .data${dm_vars.get('Sex', 'SEX')}, .data${dm_vars.get('Child-bearing potential', 'DMCBP')}, .data${dm_vars.get('Ethnicity', 'ETHNIC')}, 
                      .data${dm_vars.get('Asian', 'RACASN')}, .data${dm_vars.get('American Indian or Alaska Native', 'RACAIAN')},
                      .data${dm_vars.get('Black or African American', 'RACBAA')}, .data${dm_vars.get('Native Hawaiian or other Pacific Islander', 'RACNHOPI')}, 
                      .data${dm_vars.get('White', 'RACWC')}, .data${dm_vars.get('Not reported', 'RACNR')}, .data${dm_vars.get('Unknown', 'RACUNK')}, 
                      .data${dm_vars.get('Other', 'RACOTH')}, .data$SiteNumber, .data$Site, .data$project) %>%
        dplyr::mutate(rcsum = rowSums(dplyr::select(., .data${dm_vars.get('Asian', 'RACASN')}, .data${dm_vars.get('American Indian or Alaska Native', 'RACAIAN')}, 
                                                    .data${dm_vars.get('Black or African American', 'RACBAA')}, .data${dm_vars.get('Native Hawaiian or other Pacific Islander', 'RACNHOPI')}, 
                                                    .data${dm_vars.get('White', 'RACWC')}, .data${dm_vars.get('Other', 'RACOTH')}))) %>%
        dplyr::mutate(Race = dplyr::case_when(.data$rcsum > 1 ~ "Multiple",
                                              .data${dm_vars.get('American Indian or Alaska Native', 'RACAIAN')} == 1 ~ "American Indian or Alaska Native",
                                              .data${dm_vars.get('Asian', 'RACASN')} == 1 ~ "Asian" ,
                                              .data${dm_vars.get('Black or African American', 'RACBAA')} == 1 ~ "Black or African American" ,
                                              .data${dm_vars.get('Native Hawaiian or other Pacific Islander', 'RACNHOPI')} == 1 ~ "Native Hawaiian or Other Pacific Islander",
                                              .data${dm_vars.get('White', 'RACWC')} == 1 ~ "White",
                                              .data${dm_vars.get('Other', 'RACOTH')} == 1 ~ "Other",
                                              .data${dm_vars.get('Not reported', 'RACNR')} == 1 ~ "Not Reported",
                                              .data${dm_vars.get('Unknown', 'RACUNK')} == 1 ~ "Unknown"
        )) %>% dplyr::select(.data$Subject, .data${dm_vars.get('Age', 'AGE')}, .data${dm_vars.get('Sex', 'SEX')}, .data${dm_vars.get('Child-bearing potential', 'DMCBP')}, 
                            .data${dm_vars.get('Ethnicity', 'ETHNIC')}, .data$Race, .data$SiteNumber, .data$Site, .data$project)
      attr(dm_summary${dm_vars.get('Age', 'AGE')}, "label") <- "Age"
      attr(dm_summary${dm_vars.get('Sex', 'SEX')}, "label") <- "Sex"
      attr(dm_summary${dm_vars.get('Child-bearing potential', 'DMCBP')}, "label") <- "Child-bearing potential"
      attr(dm_summary${dm_vars.get('Ethnicity', 'ETHNIC')}, "label") <- "Ethnicity"
      dm_summary <- GSDSUtilities::raiseLabels(dm_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of dm_summary dataframe"))

      # rsrc_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating rsrc_summary dataframe"))
      rsrc_summary <- {self.table_mappings['rs_r']} %>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `Date of Response Interpolated` = .data${rsrc_vars.get('Date of response', 'RSRCDAT_INT')}) %>%
        dplyr::filter(!is.na(.data$`Subject name or identifier`)) %>%
        dplyr::group_by(.data$`Subject name or identifier`) %>%
        dplyr::summarise(FirstResponseDate = min(.data$`Date of Response Interpolated`),
                         LastResponseDate = max(.data$`Date of Response Interpolated`))

      # candig_summary Table ----------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating candig_summary dataframe"))
      candig_summary <- {self.table_mappings['mh_dx']} %>%
        dplyr::select(.data$Subject, .data${candig_vars.get('Medical history term', 'DX_MHTERM')})
      attr(candig_summary${candig_vars.get('Medical history term', 'DX_MHTERM')}, "label") <- "Type of Tumor"
      candig_summary <- GSDSUtilities::raiseLabels(candig_summary, "label", isNullC = NA)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","raiseLabels of candig_summary dataframe"))

      # SubjectInfo dataframe -------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating SubjectInfo dataframe"))
      SubjectInfo <- dplyr::left_join(dm_summary, enr_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, subject_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, rsrc_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, sd_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, dd_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, td_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, ex_summary, by = "Subject name or identifier")
      SubjectInfo <- dplyr::left_join(SubjectInfo, candig_summary, by = "Subject name or identifier")

      # Calculate SiteEnrolled, TotalEnrolled, and # of Females Enrolled
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Calculating EnrollmentbySite"))
      EnrollmentbySite <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data$SiteNumber, .data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}) %>%
        dplyr::filter(substr(.data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, 1, 1) == "Y") %>%
        dplyr::group_by(.data$SiteNumber) %>%
        dplyr::summarise(SiteEnrolled = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, EnrollmentbySite, by = "SiteNumber") %>%
        dplyr::mutate(TotalEnrolled = (SubjectInfo %>%
                                         dplyr::filter(substr(.data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, 1, 1) == "Y") %>%
                                         dplyr::select(.data$`Subject name or identifier`) %>%
                                         dplyr::distinct() %>%
                                         dplyr::count() %>%
                                         dplyr::pull())) %>%
        dplyr::mutate(TotalFemalesEnrolled = (SubjectInfo %>%
                                               dplyr::filter(substr(.data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, 1, 1) == "Y" & substr(.data${dm_vars.get('Sex', 'SEX')}, 1, 1) == "F") %>%
                                               dplyr::select(.data$`Subject name or identifier`) %>%
                                               dplyr::distinct() %>%
                                               dplyr::count() %>%
                                               dplyr::pull()))

      # Calculate Active Treatment Status
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Calculate OnTreatment Status"))
      SubjectInfo <- SubjectInfo %>%
        dplyr::mutate(`OnTreatment` = dplyr::case_when(
          substr(.data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, 1, 1) == "Y" & (!is.na(.data$`EOT Date -{self.subject_info_df['treatment'].iloc[0]}`) |  !is.na(.data$`EOS Date`) | !is.na(.data$`Death Date`)) ~ "N",
          substr(.data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, 1, 1) == "Y" & is.na(.data$`EOT Date -{self.subject_info_df['treatment'].iloc[0]}`) ~ "Y",
          substr(.data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, 1, 1) == "N" ~ "Not Enrolled"
        )) %>%
        dplyr::select(-.data$project, tidyselect::everything())

      # Calculate Cohort Enrollment
      CohortEnroll <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data${enr_vars.get('Cohort', 'COHORT')}, .data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}) %>%
        dplyr::filter(substr(.data${enr_vars.get('Was the subject enrolled?', 'ENRYN')}, 1, 1) == "Y") %>%
        dplyr::group_by(.data${enr_vars.get('Cohort', 'COHORT')}) %>%
        dplyr::summarise(`TotalEnrolled by Cohort/Arm` = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, CohortEnroll, by = .data${enr_vars.get('Cohort', 'COHORT')})

      # Calculate Treatment Status
      Treated <- SubjectInfo %>%
        dplyr::filter(!is.na(.data$FirstDoseDate)) %>%
        dplyr::select(.data$`Subject name or identifier`, .data${enr_vars.get('Cohort', 'COHORT')}) %>%
        dplyr::mutate(TotalTreated = dplyr::n_distinct(.data$`Subject name or identifier`),
                      Treated = "Yes") %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, Treated, by = c("Subject name or identifier", .data${enr_vars.get('Cohort', 'COHORT')}))

      CohortTreated <- Treated %>%
        dplyr::group_by(.data${enr_vars.get('Cohort', 'COHORT')}) %>%
        dplyr::summarise(`TotalTreated by Cohort/Arm` = dplyr::n_distinct(.data$`Subject name or identifier`)) %>%
        dplyr::distinct()

      SubjectInfo <- dplyr::left_join(SubjectInfo, CohortTreated, by = .data${enr_vars.get('Cohort', 'COHORT')})

      # Assign SubjectInfo to calling envir
      assign("SubjectInfo", SubjectInfo, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," SubjectInfo returned"))

      # End of subjectInfo Function
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    }},
    error = function(e) {{
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",e))
      stop(e)
    }},
    warning = function(w) {{
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",w))
    }}
  )
}}"""
            return template
            
        except Exception as e:
            raise Exception(f"Error generating function: {str(e)}") 