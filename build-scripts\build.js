#!/usr/bin/env node

/**
 * Build Script for MDR R Package Generator Electron App
 * This script handles the complete build process including Python environment setup
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');
const os = require('os');

class ElectronBuilder {
  constructor() {
    this.platform = os.platform();
    this.arch = os.arch();
    this.projectRoot = path.resolve(__dirname, '..');
    this.buildDir = path.join(this.projectRoot, 'build');
    this.distDir = path.join(this.projectRoot, 'dist');
    this.pythonEnvDir = path.join(this.projectRoot, 'python-env');
  }

  log(message) {
    console.log(`[BUILD] ${new Date().toISOString()} - ${message}`);
  }

  error(message) {
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}`);
  }

  async run() {
    try {
      this.log('Starting build process...');
      
      // Clean previous builds
      await this.cleanBuild();
      
      // Setup Python environment
      await this.setupPythonEnvironment();
      
      // Install Node.js dependencies
      await this.installNodeDependencies();
      
      // Build Electron app
      await this.buildElectronApp();
      
      this.log('Build completed successfully!');
      
    } catch (error) {
      this.error(`Build failed: ${error.message}`);
      process.exit(1);
    }
  }

  async cleanBuild() {
    this.log('Cleaning previous builds...');
    
    const dirsToClean = [this.buildDir, this.distDir];
    
    for (const dir of dirsToClean) {
      if (fs.existsSync(dir)) {
        this.log(`Removing ${dir}`);
        fs.rmSync(dir, { recursive: true, force: true });
      }
    }
  }

  async setupPythonEnvironment() {
    this.log('Setting up Python environment...');
    
    try {
      // Check if Python is available
      execSync('python --version', { stdio: 'pipe' });
      this.log('Python found');
    } catch (error) {
      throw new Error('Python is not installed or not in PATH');
    }

    // Create virtual environment
    if (!fs.existsSync(this.pythonEnvDir)) {
      this.log('Creating Python virtual environment...');
      execSync(`python -m venv "${this.pythonEnvDir}"`, { 
        cwd: this.projectRoot,
        stdio: 'inherit' 
      });
    }

    // Get Python executable path
    const pythonExe = this.platform === 'win32' 
      ? path.join(this.pythonEnvDir, 'Scripts', 'python.exe')
      : path.join(this.pythonEnvDir, 'bin', 'python');

    // Upgrade pip
    this.log('Upgrading pip...');
    execSync(`"${pythonExe}" -m pip install --upgrade pip`, { 
      cwd: this.projectRoot,
      stdio: 'inherit' 
    });

    // Install requirements
    const requirementsFile = path.join(this.projectRoot, 'requirements.txt');
    if (fs.existsSync(requirementsFile)) {
      this.log('Installing Python requirements...');
      execSync(`"${pythonExe}" -m pip install -r "${requirementsFile}"`, { 
        cwd: this.projectRoot,
        stdio: 'inherit' 
      });
    }

    // Install Streamlit if not in requirements
    this.log('Ensuring Streamlit is installed...');
    execSync(`"${pythonExe}" -m pip install streamlit>=1.31.1`, { 
      cwd: this.projectRoot,
      stdio: 'inherit' 
    });

    this.log('Python environment setup completed');
  }

  async installNodeDependencies() {
    this.log('Installing Node.js dependencies...');
    
    try {
      execSync('npm install', { 
        cwd: this.projectRoot,
        stdio: 'inherit' 
      });
      this.log('Node.js dependencies installed');
    } catch (error) {
      throw new Error('Failed to install Node.js dependencies');
    }
  }

  async buildElectronApp() {
    this.log('Building Electron application...');
    
    const buildCommand = this.getBuildCommand();
    
    try {
      execSync(buildCommand, { 
        cwd: this.projectRoot,
        stdio: 'inherit' 
      });
      this.log('Electron build completed');
    } catch (error) {
      throw new Error('Failed to build Electron application');
    }
  }

  getBuildCommand() {
    const target = process.argv[2] || 'current';
    
    switch (target) {
      case 'win':
      case 'windows':
        return 'npm run build-win';
      case 'mac':
      case 'macos':
        return 'npm run build-mac';
      case 'linux':
        return 'npm run build-linux';
      case 'all':
        return 'npm run build';
      default:
        // Build for current platform
        if (this.platform === 'win32') {
          return 'npm run build-win';
        } else if (this.platform === 'darwin') {
          return 'npm run build-mac';
        } else {
          return 'npm run build-linux';
        }
    }
  }

  static printUsage() {
    console.log(`
Usage: node build.js [target]

Targets:
  current   Build for current platform (default)
  win       Build for Windows
  mac       Build for macOS
  linux     Build for Linux
  all       Build for all platforms

Examples:
  node build.js
  node build.js win
  node build.js all
`);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  ElectronBuilder.printUsage();
  process.exit(0);
}

// Run the build
const builder = new ElectronBuilder();
builder.run().catch(error => {
  console.error('Build failed:', error);
  process.exit(1);
});
