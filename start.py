#!/usr/bin/env python3
"""
Streamlit Server Starter for Electron App
This script starts the Streamlit server for the MDR R Package Generator
"""

import sys
import os
import subprocess
import time
import socket
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('streamlit_server.log')
    ]
)
logger = logging.getLogger(__name__)

def check_port_available(port):
    """Check if a port is available for use."""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            return result != 0
    except Exception as e:
        logger.error(f"Error checking port {port}: {e}")
        return False

def find_available_port(start_port=8501, max_port=8600):
    """Find an available port starting from start_port."""
    for port in range(start_port, max_port + 1):
        if check_port_available(port):
            return port
    raise RuntimeError(f"No available ports found between {start_port} and {max_port}")

def install_requirements():
    """Install required Python packages if not already installed."""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        logger.warning("requirements.txt not found, skipping package installation")
        return True
    
    try:
        logger.info("Checking and installing required packages...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True, capture_output=True, text=True)
        logger.info("Package installation completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to install packages: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error during package installation: {e}")
        return False

def start_streamlit_server(port):
    """Start the Streamlit server."""
    app_file = Path(__file__).parent / "app.py"
    
    if not app_file.exists():
        raise FileNotFoundError(f"app.py not found at {app_file}")
    
    # Streamlit configuration
    config_args = [
        "--server.port", str(port),
        "--server.address", "localhost",
        "--server.headless", "true",
        "--browser.gatherUsageStats", "false",
        "--server.enableCORS", "false",
        "--server.enableXsrfProtection", "false",
        "--server.enableWebsocketCompression", "false",
        "--logger.level", "info"
    ]
    
    # Build command
    cmd = [sys.executable, "-m", "streamlit", "run", str(app_file)] + config_args
    
    logger.info(f"Starting Streamlit server on port {port}")
    logger.info(f"Command: {' '.join(cmd)}")
    logger.info(f"Working directory: {Path(__file__).parent}")
    
    # Set environment variables
    env = os.environ.copy()
    env["STREAMLIT_SERVER_HEADLESS"] = "true"
    env["STREAMLIT_BROWSER_GATHER_USAGE_STATS"] = "false"
    
    try:
        # Start the process
        process = subprocess.Popen(
            cmd,
            cwd=Path(__file__).parent,
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # Monitor the process output
        logger.info("Streamlit server starting...")
        
        for line in iter(process.stdout.readline, ''):
            if line:
                print(line.rstrip())  # Print to stdout for Electron to capture
                
                # Check for successful startup
                if any(phrase in line for phrase in [
                    "You can now view your Streamlit app",
                    "Network URL:",
                    "Local URL:"
                ]):
                    logger.info("Streamlit server started successfully")
                
                # Check for errors
                if any(phrase in line for phrase in [
                    "Address already in use",
                    "Error",
                    "Exception"
                ]):
                    logger.error(f"Streamlit error: {line.rstrip()}")
        
        # Wait for process to complete
        return_code = process.wait()
        logger.info(f"Streamlit server exited with code {return_code}")
        return return_code
        
    except KeyboardInterrupt:
        logger.info("Received interrupt signal, shutting down...")
        if 'process' in locals():
            process.terminate()
            process.wait()
        return 0
    except Exception as e:
        logger.error(f"Failed to start Streamlit server: {e}")
        return 1

def main():
    """Main entry point."""
    try:
        # Get port from command line argument or use default
        if len(sys.argv) > 1:
            try:
                port = int(sys.argv[1])
            except ValueError:
                logger.error(f"Invalid port number: {sys.argv[1]}")
                port = find_available_port()
        else:
            port = find_available_port()
        
        logger.info(f"MDR R Package Generator - Starting on port {port}")
        
        # Check if port is available
        if not check_port_available(port):
            logger.warning(f"Port {port} is not available, finding alternative...")
            port = find_available_port(port + 1)
            logger.info(f"Using alternative port: {port}")
        
        # Install requirements
        if not install_requirements():
            logger.error("Failed to install required packages")
            return 1
        
        # Start Streamlit server
        return start_streamlit_server(port)
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
