import pandas as pd
import re
from typing import Dict, List, Tuple

class PatientProfileGenerator:
    def __init__(self, als_file, tumor_type=None):
        """Initialize the PatientProfileGenerator with ALS file and tumor type.

        Args:
            als_file: Path to the ALS file
            tumor_type: Type of tumor ('Solid Tumor' or 'Heme')
        """
        self.als_file = als_file
        self.tumor_type = tumor_type
        self.patient_profile_df = self._generate_patient_profile_mapping()
        self.table_mappings = self._generate_table_mappings()

    def _generate_table_mappings(self) -> Dict[str, str]:
        """Generate mappings between standard table names and actual table names from ALS."""
        try:
            # Read ALS file sheets
            fields_df = pd.read_excel(self.als_file, sheet_name="Fields", engine='openpyxl')
            forms_df = pd.read_excel(self.als_file, sheet_name="Forms", engine='openpyxl')

            # Process forms to get table mappings
            forms_df = forms_df[['OID', 'DraftFormName']].copy()
            # Filter out rows where DraftFormName contains "_INACTIVE"
            forms_df = forms_df[~forms_df['DraftFormName'].str.contains('_INACTIVE', na=False)]
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df.drop('OID', axis=1).dropna()

            # Map standard names to actual form names and convert to lowercase
            table_mappings = {
                'ae': forms_df[forms_df['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \{Mixed form\}$', case=False, na=False)]['Form'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \{Mixed form\}$', case=False, na=False)].empty else ' ',
                'cm': forms_df[forms_df['DraftFormName'].str.contains('Prior/Concomitant Medications$', case=False, na=False)]['Form'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Prior/Concomitant Medications$', case=False, na=False)].empty else ' ',
                'pproc': forms_df[forms_df['DraftFormName'].str.contains('Prior/Concomitant Procedures/Surgeries$', case=False, na=False)]['Form'].iloc[0].lower() if not forms_df[forms_df['DraftFormName'].str.contains('Prior/Concomitant Procedures/Surgeries$', case=False, na=False)].empty else ' ',
            }

            # Find all response assessment forms (rs_*)
            rs_forms = forms_df[forms_df['DraftFormName'].str.contains('Time-point Response Assessment|Disease Assessment', case=False, na=False)]

            # Add each rs form to the mappings
            for idx, row in rs_forms.iterrows():
                form_name = row['Form'].lower()

                # Check if this is a generic response form (rs_r)
                if form_name == 'rs_r':
                    table_mappings['rs_r'] = form_name
                else:
                    # Extract a short name for the form (e.g., rs_cll, rs_nhl, rs_wm)
                    try:
                        # Try to extract a meaningful suffix from the form name
                        if '-' in row['DraftFormName']:
                            suffix = re.sub(r'[^a-zA-Z0-9]', '', row['DraftFormName'].split('-')[-1].lower())[:3]
                        else:
                            # If no hyphen, use the last 3 characters of the form name
                            suffix = re.sub(r'[^a-zA-Z0-9]', '', row['DraftFormName'].lower())[-3:]

                        short_name = 'rs_' + suffix
                    except:
                        # Fallback to a generic name with an index
                        short_name = f'rs_{idx+1}'

                    table_mappings[short_name] = form_name

            return table_mappings

        except Exception as e:
            raise Exception(f"Error generating table mappings: {str(e)}")

    def _generate_patient_profile_mapping(self):
        """Generate patient profile mapping from ALS file."""
        try:
            # Read ALS file sheets
            fields_df = pd.read_excel(self.als_file, sheet_name="Fields", engine='openpyxl')
            forms_df = pd.read_excel(self.als_file, sheet_name="Forms", engine='openpyxl')
            data_dic_df = pd.read_excel(self.als_file, sheet_name="DataDictionaryEntries", engine='openpyxl')

            # Process fields
            fields_df = fields_df[['FormOID', 'FieldOID', 'SASLabel','DraftFieldName']].copy()
            # Filter out rows where DraftFieldName contains "_INACTIVE"
            if 'DraftFieldName' in fields_df.columns:
                fields_df = fields_df[~fields_df['DraftFieldName'].str.contains('_INACTIVE', na=False)]
            fields_df['Form'] = fields_df['FormOID'].str.lower()
            fields_df = fields_df.drop('FormOID', axis=1).dropna()

            # Process forms
            forms_df = forms_df[['OID', 'DraftFormName']].copy()
            # Filter out rows where DraftFormName contains "_INACTIVE"
            forms_df = forms_df[~forms_df['DraftFormName'].str.contains('_INACTIVE', na=False)]
            forms_df['Form'] = forms_df['OID'].str.lower()
            forms_df = forms_df.drop('OID', axis=1).dropna()

            # Join fields and forms
            var_label_form = fields_df.merge(forms_df, on='Form')

            # Generate mappings for each part of the patient profile
            ae_std = var_label_form[
                var_label_form['DraftFormName'].str.contains('^Adverse Events$|^Adverse Events \{Mixed form\}$', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Start date|Stop date|^Adverse event$|Toxicity grade|Result in death|Was adverse event serious|Outcome|dose limiting toxicity|clinical interest|special interest|immune-mediated|immune-related|infusion related', case=False, na=False) &
                ~var_label_form['SASLabel'].str.contains('RSG', case=False, na=False)
            ].copy()
            ae_std['part'] = 'ae_std'

            cm_std = var_label_form[
                var_label_form['DraftFormName'].str.contains('^Prior/Concomitant Medications$', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Start date|Stop date|Medication name|Ongoing', case=False, na=False)
            ].copy()
            cm_std['part'] = 'cm_std'

            rs_std = var_label_form[
                var_label_form['DraftFormName'].str.contains('Time-point Response Assessment|Disease Assessment', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Overall response|Date of response|Date of overall response', case=False, na=False)
            ].copy()

            # Join with data dictionary for response values
            rs_std = rs_std.merge(
                data_dic_df[['UserDataString', 'CodedData', 'DataDictionaryName']],
                left_on='FieldOID',
                right_on='DataDictionaryName',
                how='left'
            )
            rs_std['part'] = 'rs_std'

            pproc_std = var_label_form[
                var_label_form['DraftFormName'].str.contains('^Prior/Concomitant Procedures/Surgeries$', case=False, na=False) &
                var_label_form['SASLabel'].str.contains('Date of procedure/surgery|Type or name of procedure/surgery', case=False, na=False)
            ].copy()
            pproc_std['part'] = 'pproc_std'

            # Combine all mappings
            patient_profile_df = pd.concat([
                ae_std, cm_std, rs_std, pproc_std
            ], ignore_index=True)

            return patient_profile_df

        except Exception as e:
            raise Exception(f"Error generating patient profile mapping: {str(e)}")

    def _get_variable_mapping(self, part: str) -> Dict[str, str]:
        """Get variable mappings for a specific part of the patient profile."""
        part_df = self.patient_profile_df[self.patient_profile_df['part'] == part]

        # For rs_std, also include the response values from the data dictionary
        if part == 'rs_std':
            # Create a dictionary with SASLabel -> FieldOID mappings
            var_mapping = dict(zip(part_df['SASLabel'], part_df['FieldOID']))

            # Add response values from the data dictionary
            response_values = {}
            for _, row in part_df.iterrows():
                if pd.notna(row.get('UserDataString')) and pd.notna(row.get('CodedData')):
                    response_values[row['UserDataString']] = row['CodedData']

            # Add response values to the mapping dictionary
            var_mapping['response_values'] = response_values

            # Identify the overall response field
            response_field = None
            for label, field_oid in var_mapping.items():
                if 'overall response' in label.lower() or (
                    'response' in label.lower() and 'date' not in label.lower()
                ):
                    response_field = field_oid
                    var_mapping['Overall response'] = field_oid
                    break

            # Identify the date field
            date_field = None
            date_labels = [
                'date of overall response',
                'date of response',
                'assessment date',
                'response date'
            ]

            for label, field_oid in var_mapping.items():
                if any(date_label in label.lower() for date_label in date_labels):
                    date_field = field_oid
                    # Add both standardized keys to ensure compatibility
                    var_mapping['Date of response'] = field_oid
                    var_mapping['Date of overall response'] = field_oid
                    break

            # Print for debugging
            print(f"RS mapping - Response field: {response_field}, Date field: {date_field}")
            print(f"Full RS mapping: {var_mapping}")

            return var_mapping
        else:
            # For other parts, just return the SASLabel -> FieldOID mappings
            return dict(zip(part_df['SASLabel'], part_df['FieldOID']))

    def generate_function(self, study_id: str) -> str:
        """Generate the patientProfile R function."""
        try:
            # Get variable mappings for each part
            # These mappings contain the field names from the ALS file
            # that correspond to each part of the patient profile
            ae_vars = self._get_variable_mapping('ae_std')
            cm_vars = self._get_variable_mapping('cm_std')
            rs_vars = self._get_variable_mapping('rs_std')
            pproc_vars = self._get_variable_mapping('pproc_std')

            # Log the mappings for debugging
            print(f"AE variables: {ae_vars}")
            print(f"CM variables: {cm_vars}")
            print(f"RS variables: {rs_vars}")
            print(f"PPROC variables: {pproc_vars}")

            # Extract table mappings
            ae = self.table_mappings.get('ae', ' ')
            cm = self.table_mappings.get('cm', ' ')
            pproc = self.table_mappings.get('pproc', ' ')

            # Get all rs tables (rs_cll, rs_nhl, rs_wm)
            rs_tables = []
            for key, value in self.table_mappings.items():
                if key.startswith('rs_'):
                    rs_tables.append((key, value))

            # Template for the R function with placeholders
            template = """#' @title patientProfile_{study_id}
#' @description Create a Patient Profile
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param SubjectInfo This the SubjectInfo dataframe
#' @param ae This is the ae dataframe
#' @param cm This is the cm dataframe
"""

            # Add rs tables to the template
            for rs_key, rs_value in rs_tables:
                template += f"#' @param {rs_key} This is the {rs_value} dataframe\n"

            # Continue with the rest of the template
            template += """#' @param lb_calc This is the lb_calc dataframe
#' @param {pproc} This is the {pproc} dataframe
#' @param td_merged This is the td_merged dataframe
#' @param dose_merged This is the dose_merged dataframe
#' @param Treated This is the Treaded dataframe
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#' @param EDCextractDate This is the EDC extract Date
#'
#' @return PPandCorevars
#' @return PatientProfile
#' @return aePatientProfile
#'
#' @export patientProfile_{study_id}
#'
#' @importFrom dplyr select mutate if_else row_number filter group_by distinct bind_rows left_join case_when
#' @importFrom tidyselect starts_with everything
#' @importFrom tidyr pivot_longer unite
#' @importFrom lubridate as_date
#' @importFrom tibble add_row
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame assert_directory_exists
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \\dontrun{{
#'patientProfile_{study_id}(studyId = studyIdVar, sourceLocation = OutFolder,
#'                              tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                              EDCextractDate = EDCextractDate,
#'                              SubjectInfo = SubjectInfo, ae = {ae},
#'                              cm = {cm}, """

            # Add rs tables to the examples
            rs_examples = []
            for rs_key, rs_value in rs_tables:
                rs_examples.append(f"{rs_key} = {rs_value}")

            template += ", ".join(rs_examples)

            # Continue with the rest of the examples
            template += """,
#'                              lb_calc = lb_calc, {pproc} = {pproc},
#'                              td_merged = td_merged, dose_merged = dose_merged,
#'                              Treated = Treated,
#'                              develop.f = develop.f, vpath = vpath)
#'}}
#'
patientProfile_{study_id} <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime,
                                  EDCextractDate, SubjectInfo, ae, cm, """

            # Add rs tables to the function parameters
            rs_params = []
            for rs_key, _ in rs_tables:
                rs_params.append(rs_key)

            template += ",".join(rs_params)

            # Continue with the rest of the function parameters
            template += """,
                                  lb_calc, {pproc}, td_merged, dose_merged,
                                  Treated, develop.f = develop.f, vpath = vpath ) {{
  withCallingHandlers(
    expr = {{
      calledFun = "patientProfile_{study_id}"
      # Start Function --------------------------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

      # Assertions -----------------------------------------------------------------------
      # Assert SubjectInfo has min.rows and min.cols
      checkmate::assert_data_frame(SubjectInfo, min.rows = 1, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm SubjectInfo has min.rows and min.cols."))
      # Assert ae has min.rows and min.cols
      checkmate::assert_data_frame(ae, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm ae has min.rows and min.cols."))
      # Assert cm has min.rows and min.cols
      checkmate::assert_data_frame(cm, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm cm has min.rows and min.cols."))"""

            # Add assertions for rs tables
            for rs_key, _ in rs_tables:
                template += f"""
      # Assert {rs_key} has min.rows and min.cols
      checkmate::assert_data_frame({rs_key}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm {rs_key} has min.rows and min.cols."))"""

            # Continue with the rest of the assertions
            template += """
      # Assert lb_calc has min.rows and min.cols
      checkmate::assert_data_frame(lb_calc, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm lb_calc has min.rows and min.cols."))
      # Assert {pproc} has min.rows and min.cols
      checkmate::assert_data_frame({pproc}, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm proc has min.rows and min.cols."))
      # Assert td_merged has min.rows and min.cols
      checkmate::assert_data_frame(td_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm td_merged has min.rows and min.cols."))
      # Assert dose_merged has min.rows and min.cols
      checkmate::assert_data_frame(dose_merged, min.rows = 0, min.cols = 2)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm dose_merged has min.rows and min.cols."))
      # Assert sourceLocation directory exists
      checkmate::assert_directory_exists(sourceLocation)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))
"""

            # Add ae_std data table processing
            # Get field mappings for ae_std
            ae_start_date = ae_vars.get('Start date', ' ')
            ae_end_date = ae_vars.get('Stop date', ' ')
            ae_term = ae_vars.get('Adverse event', ' ')
            #ae_dict_term = ae_vars.get('AE Dictionary-Derived Term', 'AETERM_PT')
            #ae_soc = ae_vars.get('AE SOC', 'AETERM_SOC')
            ae_tox_grade = ae_vars.get('Toxicity grade', ' ')
            ae_death = ae_vars.get('Result in death', ' ')
            ae_serious = ae_vars.get('Was adverse event serious?', ' ')
            ae_outcome = ae_vars.get('Outcome', ' ')

            # Check if DLT and Clinical/Special interest fields exist in the ALS file
            has_dlt = 'Is this adverse event a dose limiting toxicity (DLT)?' in ae_vars

            # Check for either Clinical interest or Special interest, but not both
            has_clinical_interest = 'Is this adverse event of clinical interest (AECI)?' in ae_vars
            has_special_interest = 'Is this adverse event of special interest (AESI)?' in ae_vars

            # check for immune-related
            immune_related_fields = [
                'Is this adverse event immune-mediated?',
                'Is this adverse event immune-related?',
                'Is this adverse event immune mediated?',
                'Is this adverse event immune related?'
            ]
            has_immune_mediated = any(field in ae_vars for field in immune_related_fields)

            # check for infusion related
            infusion_related_fields = [
                'Is this adverse event infusion related AE?',
                'Is this adverse event an infusion related reaction (IRR)?',
                'Is this adverse event infusion related reaction?'
            ]
            has_infusion_related = any(field in ae_vars for field in infusion_related_fields)

            # Get DLT field name if it exists
            ae_dlt = ae_vars.get('Is this adverse event a dose limiting toxicity (DLT)?', None)

            # Get either Clinical interest or Special interest field name
            ae_clinical_interest = ae_vars.get('Is this adverse event of clinical interest (AECI)?', None) if has_clinical_interest else None
            ae_special_interest = ae_vars.get('Is this adverse event of special interest (AESI)?', None) if has_special_interest else None

            # Get immune-related field name if it exists
            ae_immune_mediated = None
            if has_immune_mediated:
                for field in immune_related_fields:
                    if field in ae_vars:
                        ae_immune_mediated = ae_vars.get(field)
                        break

            # Get infusion related field name if it exists
            ae_infusion_related = None
            if has_infusion_related:
                for field in infusion_related_fields:
                    if field in ae_vars:
                        ae_infusion_related = ae_vars.get(field)
                        break

            # Start building the select statement for ae_std
            select_statement = f"""
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `AE Start Date` = .data${ae_start_date}_INT,
                      `AE End Date` = .data${ae_end_date}_INT,
                      `AE Dictionary-Derived Term` = .data${ae_term}_PT,
                      AETerm = .data${ae_term},
                      `AE SOC` = .data${ae_term}_SOC,
                      `Tox Grade` = .data${ae_tox_grade},
                      `AE Death` = .data${ae_death},
                      Serious = .data${ae_serious},
                      Outcome = .data${ae_outcome}"""

            # Add DLT field if it exists
            if has_dlt and ae_dlt:
                select_statement += f""",
                      DLT = .data${ae_dlt}"""

            # Add Clinical interest field if it exists
            if has_clinical_interest and ae_clinical_interest:
                select_statement += f""",
                      `Clinical interest` = .data${ae_clinical_interest}"""

            # Add Special interest field if it exists
            if has_special_interest and ae_special_interest:
                select_statement += f""",
                      `Special interest` = .data${ae_special_interest}"""

            # Add immune-related field if it exists
            if has_immune_mediated and ae_immune_mediated:
                select_statement += f""",
                      `Immune-related` = .data${ae_immune_mediated}"""

            # Add infusion related field if it exists
            if has_infusion_related and ae_infusion_related:
                select_statement += f""",
                      `Infusion related` = .data${ae_infusion_related}"""

            # Add CCQ fields if tumor type is Solid Tumor
            if self.tumor_type == "Solid Tumor":
                select_statement += """,
                      .data$`CCQ Category`,
                      .data$CCQ_flag"""

            # Close the select statement
            select_statement += """
                      ) %>%"""

            template += f"""
      # ae_std Data Table ----------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae_std dataframe"))
      ae_std <- ae %>%{select_statement}
        dplyr::mutate(`CRF Origin` = "{ae}",
                      Event = dplyr::if_else(is.na(.data$`AE Dictionary-Derived Term`),
                                             as.character(.data$`AETerm`),
                                             as.character(.data$`AE Dictionary-Derived Term`)),
                      rowid = paste("ae", dplyr::row_number()),
                      domain = "ae")

      # cm_std Data Table ----------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating cm_std dataframe"))"""

            # Get field mappings for cm_std
            cm_start_date = cm_vars.get('Start date', ' ')
            cm_end_date = cm_vars.get('Stop date', ' ')
            cm_term = cm_vars.get('Medication name', ' ')
            cm_ongoing = cm_vars.get('Ongoing', ' ')

            template += f"""
      cm_std <- cm %>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      # Use field names from ALS file mapping
                      `CM Start Date` = .data${cm_start_date}_INT,
                      `CM End Date` = .data${cm_end_date}_INT,
                      `CM Term` = .data${cm_term},
                      `CM Preferred Term` = .data${cm_term}_PROD,
                      `Ongoing` = .data${cm_ongoing}) %>%
        dplyr::mutate(`CRF Origin` = "{cm}", Event = dplyr::if_else(is.na(.data$`CM Preferred Term`),
                                                                  as.character(.data$`CM Term`),
                                                                  as.character(.data$`CM Preferred Term`)),
                      rowid = paste("cm", dplyr::row_number()),
                      domain = "cm")

      # rs_std Data Table ----------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating rs_std dataframe"))
"""

            # Process each rs table
            rs_bind_tables = []

            for rs_key, _ in rs_tables:
                # Create a variable name for the standardized dataframe
                rs_var = rs_key.replace('-', '_') + '_std'
                rs_bind_tables.append(rs_var)

                # Get field mappings for rs table
                rs_response = rs_vars.get('Overall response', ' ')

                # Check for multiple possible date field names
                rs_date = ' '
                date_field_options = ['Date of response', 'Date of overall response']
                for date_field in date_field_options:
                    if date_field in rs_vars:
                        rs_date = rs_vars.get(date_field)
                        break

                # Print debug info
                print(f"Processing {rs_key} with response field: {rs_response}, date field: {rs_date}")

                # Get response values from the data dictionary
                response_values = rs_vars.get('response_values', {})

                # Build case_when statements for each response value
                case_when_statements = []
                for user_data_string, coded_data in response_values.items():
                    # Escape any double quotes in the strings
                    user_data_string = user_data_string.replace('"', '\\"')
                    coded_data = coded_data.replace('"', '\\"')

                    # Map response values to standard abbreviations
                    if "complete response" in user_data_string.lower():
                        coded_data = "CR"
                    elif "partial response" in user_data_string.lower():
                        coded_data = "PR"
                    elif "stable disease" in user_data_string.lower():
                        coded_data = "SD"
                    elif "progressive disease" in user_data_string.lower():
                        coded_data = "PD"
                    elif "not evaluable" in user_data_string.lower():
                        coded_data = "NE"
                    elif "non-cr/non-pd" in user_data_string.lower():
                        coded_data = "Non-CR/Non-PD"

                    case_when_statements.append(
                        f'          grepl("{user_data_string}", .data${rs_response}, ignore.case = TRUE) ~ "{coded_data}"'
                    )

                # Join the case_when statements with newlines
                case_when_code = "\n".join(case_when_statements)

                # If no case_when statements, add default mappings for common response values
                if not case_when_statements:
                    default_mappings = [
                        f'          grepl("Complete response", .data${rs_response}, ignore.case = TRUE) ~ "CR"',
                        f'          grepl("Partial response", .data${rs_response}, ignore.case = TRUE) ~ "PR"',
                        f'          grepl("Stable disease", .data${rs_response}, ignore.case = TRUE) ~ "SD"',
                        f'          grepl("Progressive disease", .data${rs_response}, ignore.case = TRUE) ~ "PD"',
                        f'          grepl("Not evaluable", .data${rs_response}, ignore.case = TRUE) ~ "NE"',
                        f'          grepl("Non-CR/Non-PD", .data${rs_response}, ignore.case = TRUE) ~ "Non-CR/Non-PD"',
                        f'          !is.na(.data${rs_response}) ~ as.character(.data${rs_response})'
                    ]
                    case_when_code = "\n".join(default_mappings)

                # Add additional standard mappings to ensure common terms are always mapped
                standard_mappings = [
                    f'          grepl("Complete response", .data${rs_response}, ignore.case = TRUE) ~ "CR"',
                    f'          grepl("Partial response", .data${rs_response}, ignore.case = TRUE) ~ "PR"',
                    f'          grepl("Stable disease", .data${rs_response}, ignore.case = TRUE) ~ "SD"',
                    f'          grepl("Progressive disease", .data${rs_response}, ignore.case = TRUE) ~ "PD"',
                    f'          grepl("Not evaluable", .data${rs_response}, ignore.case = TRUE) ~ "NE"',
                    f'          grepl("Non-CR/Non-PD", .data${rs_response}, ignore.case = TRUE) ~ "Non-CR/Non-PD"'
                ]

                # Only add these if they're not already in the case_when statements
                if case_when_statements:
                    # Check if we need to add any standard mappings
                    for mapping in standard_mappings:
                        # Extract the response term from the mapping
                        term = mapping.split('grepl("')[1].split('"')[0].lower()
                        # Check if this term is already covered in our case_when statements
                        if not any(term in stmt.lower() for stmt in case_when_statements):
                            # Add it to the beginning of our case_when_code
                            case_when_code = mapping + ",\n" + case_when_code

                # Generate processing code for this rs table
                template += f"""
      # Preparing {rs_key}
      {rs_var} <- {rs_key} %>%
        dplyr::mutate(`Overall response (Coded)` = dplyr::case_when(
{case_when_code},
          TRUE ~ as.character(.data${rs_response})
        )) %>%
        dplyr::select(`Subject name or identifier` = .data$Subject,
                      `Date of Response Interpolated` = .data${rs_date}_INT,
                      .data$`Overall response (Coded)`) %>%
        dplyr::mutate(`CRF Origin` = "{rs_key}",
                      Event = "rs",
                      rowid = paste("rs", dplyr::row_number()),
                      domain = "rs",
                      EventType = "rs",
                      Date = lubridate::as_date(.data$`Date of Response Interpolated`))
"""

            if rs_bind_tables:
                template += f"""
      # Bind all rs tables
      rs_std <- dplyr::bind_rows({','.join(rs_bind_tables)})
"""
            else:
                template += """
      # No rs tables to bind
      rs_std <- tibble::tibble(`Subject name or identifier` = character(0),
                              `Date of Response Interpolated` = as.Date(character(0)),
                              `Overall response (Coded)` = character(0),
                              `CRF Origin` = character(0),
                              Event = character(0),
                              rowid = character(0),
                              domain = character(0),
                              EventType = character(0),
                              Date = as.Date(character(0)))
"""

            # Add lb_std data table processing
            template += """

      # lab_std Data Table ----------------------------------------------------------------
      # UpdateVars if `LB Toxicity-CTCAE` is not available from ctcae scripts
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating lb_std dataframe"))
      lb_std <- lb_calc %>%
        dplyr::select(.data$`Subject name or identifier`,
                      .data$AnalyteName,
                      .data$`Clinical date of record (ex: visit date)`,
                      .data$NumericValue,
                      .data$LabUnits,
                      .data$StdValue,
                      .data$StdUnits,
                      `Tox Grade` = .data$`LB Toxicity-CTCAE`,
                      .data$`Lab Category` ) %>%
        dplyr::mutate(`CRF Origin` = "lb",
                      Event = paste(.data$AnalyteName),
                      rowid = paste("lb", dplyr::row_number()),
                      domain = "lab",
                      EventType = "lab",
                      Date = lubridate::as_date(.data$`Clinical date of record (ex: visit date)`))
      # proc_std Data Table ----------------------------------------------------------------
      # UpdateVars
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating proc_std dataframe"))"""

            # Get field mappings for pproc_std
            pproc_date = pproc_vars.get('Date of procedure/surgery', 'PRSTDAT')
            pproc_name = pproc_vars.get('Type or name of procedure/surgery', 'PRTRT')

            template += f"""
      pproc_std <- {{pproc}} %>%
         dplyr::select(`Subject name or identifier` = .data$Subject,
                      # Use field names from ALS file mapping
                      `Date of Procedure/Surgery` = .data${pproc_date}_INT,
                      `Type or Name of Procedure` = .data${pproc_name}) %>%
        dplyr::mutate(`CRF Origin` = "{pproc}",
                      Event = as.character(.data$`Type or Name of Procedure`),
                      rowid = paste("proc", dplyr::row_number()),
                      domain = "proc",
                      EventType = "proc",
                      Date = lubridate::as_date(.data$`Date of Procedure/Surgery`))

      # FirstDoseDate Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating FirstDoseDate dataframe"))
      FirstDoseDate <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data$FirstDoseDate)

       # sd_std Data Table  ----------------------------------------------------------------
      # UpdateVars (CRF Origin if different from sd)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating sd_std dataframe"))
      sd_std <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`EOS Date`, .data$`EOS Reason` ) %>%
        dplyr::filter(!is.na(.data$`EOS Date`) |
                        !is.na(.data$`EOS Reason`) ) %>%
        dplyr::mutate(`CRF Origin` = "sd", domain = "EOT/EOS", subdomain = "EOS")
      # td_std Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating td_std dataframe"))
      td_std <- td_merged %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`EOT Reason`, .data$`EOT Date`, .data$Treatment,
                      .data$`CRF Origin`) %>%
        dplyr::mutate(domain = "EOT/EOS", subdomain = "EOT")
      # death_std Data Table  ----------------------------------------------------------------
      # UpdateVars (CRF Origin if different from sd)
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating death_std dataframe"))
      death_std <- SubjectInfo %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`Death Date`, .data$`Death Cause` ) %>%
        dplyr::filter(!is.na(.data$`Death Date`) |
                        !is.na(.data$`Death Cause`) ) %>%
        dplyr::mutate(`CRF Origin` = "dd", domain = "EOT/EOS", subdomain = "Death")

      # ex_std Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex_std dataframe"))
      ex_std <- dose_merged %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`Dose Start Date (Interpolated)`,
                      .data$`Dose End Date (Interpolated)`, .data$`Planned Dose`, .data$`Actual Dose`, .data$`Dose Units`, .data$`Cycle Number`,
                      .data$`Was Dose Missed?`, .data$`Reason Dose Missed`, .data$`Was Dose Interrupted/delayed/withheld?`,
                      .data$`Reason Dose Interrupted/delayed/withheld`, .data$`Was Dose Modified/reduced/decreased?`,
                      .data$`Reason Dose Modified/reduced/decreased`,  .data$`Was Dose Discontinued?`, .data$`Reason Dose Discontinued`,
                      .data$Treatment, .data$`CRF Origin`) %>%
        tidyr::unite(col = "Event" , c("Treatment", "Planned Dose"), sep = "-", remove = FALSE) %>%
        tidyr::unite(col = "Event" , c("Event", "Dose Units"), sep = "", remove = FALSE) %>%
        dplyr::select(-.data$Event, tidyselect::everything()) %>%
        dplyr::mutate(rowid = paste("ex", dplyr::row_number()), domain = "dose")
      # ae1 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae1 dataframe"))
      ae1 <- ae_std %>%
        dplyr::mutate(EventType = "start",  Date = lubridate::as_date(.data$`AE Start Date`))
      # ae2 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ae2 dataframe"))
     ae2 <-  ae_std %>%
        dplyr::left_join(.,SubjectInfo %>% dplyr::select(.data$`Subject name or identifier`, .data$`Death Date`, .data$`EOS Date`), by = c("Subject name or identifier")) %>%
        dplyr::mutate(EventType = dplyr::case_when(
          !is.na(.data$`AE End Date`) ~ "end",
          is.na(.data$`AE End Date`) & !is.na(.data$`Death Date`) ~ "death",
          is.na(.data$`AE End Date`) & is.na(.data$`Death Date`) ~ "ongoing",
          TRUE ~ NA_character_),
          Date = dplyr::case_when(
            !is.na(.data$`AE End Date`) ~ lubridate::as_date(.data$`AE End Date`),
            is.na(.data$`AE End Date`) & !is.na(.data$`Death Date`) ~ lubridate::as_date(.data$`Death Date`),
            is.na(.data$`AE End Date`) & !is.na(.data$`EOS Date`) ~ lubridate::as_date(.data$`EOS Date`),
            is.na(.data$`AE End Date`) & is.na(.data$`Death Date`) & is.na(.data$`EOS Date`) ~ lubridate::as_date(EDCextractDate),
            TRUE ~ lubridate::as_date(NA))
        ) %>%
        dplyr::select(-.data$`Death Date`, -.data$`EOS Date`)

      # cm1 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating cm1 dataframe"))
      cm1 <- cm_std %>%
        dplyr::mutate(EventType = "start",  Date = lubridate::as_date(.data$`CM Start Date`))
      # cm2 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating cm2 dataframe"))
      cm2 <- cm_std %>%
        dplyr::left_join(.,SubjectInfo %>% dplyr::select(.data$`Subject name or identifier`, .data$`Death Date`, .data$`EOS Date`), by = c("Subject name or identifier")) %>%
        dplyr::mutate(EventType = dplyr::if_else(.data$Ongoing == "Yes", "ongoing", "end"),
                      Date = dplyr::case_when(
                        !is.na(.data$`CM End Date`) ~ lubridate::as_date(.data$`CM End Date`),
                        is.na(.data$`CM End Date`) & !is.na(.data$`Death Date`) ~ lubridate::as_date(.data$`Death Date`),
                        is.na(.data$`CM End Date`) & !is.na(.data$`EOS Date`) ~ lubridate::as_date(.data$`EOS Date`),
                        is.na(.data$`CM End Date`) & is.na(.data$`Death Date`)& is.na(.data$`EOS Date`) ~ lubridate::as_date(EDCextractDate),
                        TRUE ~ lubridate::as_date(NA)))%>%
        dplyr::select(-.data$`Death Date`, -.data$`EOS Date`)
      # ex1 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex1 dataframe"))
      ex1 <- ex_std %>%
        dplyr::mutate(EventType = "start",  Date = lubridate::as_date(.data$`Dose Start Date (Interpolated)`))
      # ex2 Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating ex2 dataframe"))
      ex2 <- ex_std %>%
        dplyr::mutate(EventType = "end",  Date = lubridate::as_date(.data$`Dose End Date (Interpolated)`))

      # PPandCorevars Data Table  ----------------------------------------------------------------
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating PPandCorevars dataframe"))
  PPandCorevars <- dplyr::bind_rows(ae1, ae2, cm1, cm2, ex1, ex2, rs_std, lb_std,
                                        pproc_std, sd_std, death_std, td_std)
      # Add label to remove warning on join
      attr(PPandCorevars$`Subject name or identifier` , "label") <- "Subject name or identifier"

     PPandCorevars <- dplyr::left_join(PPandCorevars, FirstDoseDate,
                                        by = "Subject name or identifier") %>%
        dplyr::mutate(`EOS Day` = dplyr::if_else(!is.na(.data$`EOS Date`),
                                                 as.integer(difftime(.data$`EOS Date`,
                                                                     .data$FirstDoseDate,
                                                                     units = "days")), NULL),
                      `EOT Day` = dplyr::if_else(!is.na(.data$`EOT Date`),
                                                 as.integer(difftime(.data$`EOT Date`,
                                                                     .data$FirstDoseDate,
                                                                     units = "days")), NULL),
                      `Death Day` = dplyr::if_else(!is.na(.data$`Death Date`),
                                                   as.integer(difftime(.data$`Death Date`,
                                                                       .data$FirstDoseDate,
                                                                       units = "days")), NULL),
                      StudyDay = dplyr::case_when(
                        !is.na(.data$`EOT Day`) ~ .data$`EOT Day`,
                        !is.na(.data$`EOS Day`) & .data$subdomain == "EOS" ~ .data$`EOS Day`,
                        !is.na(.data$`Death Day`)& .data$subdomain == "Death"~ .data$`Death Day`,
                        TRUE ~ as.integer(difftime(.data$Date, lubridate::as_date(.data$FirstDoseDate), units = "days"))
                      ))


       PatientProfile <- PPandCorevars %>%
          dplyr::select(
          .data$`Subject name or identifier`, .data$Event, .data$EventType, .data$Date, .data$Serious, .data$`AE Dictionary-Derived Term`, .data$`AE SOC`,
          .data$`Tox Grade`"""

            # Add DLT field if it exists
            if has_dlt:
                template += ", .data$DLT"

            # Add Clinical interest field if it exists
            if has_clinical_interest:
                template += ", .data$`Clinical interest`"

            # Add Special interest field if it exists
            if has_special_interest:
                template += ", .data$`Special interest`"

            # Add immune-related field if it exists
            if has_immune_mediated:
                template += ", .data$`Immune-related`"

            # Add infusion related field if it exists
            if has_infusion_related:
                template += ", .data$`Infusion related`"

            # Add CCQ fields if tumor type is Solid Tumor
            if self.tumor_type == "Solid Tumor":
                template += ", .data$`CCQ Category`, .data$CCQ_flag"

            # Continue with the rest of the fields
            template += """, .data$Outcome, .data$`CM Preferred Term`, .data$`CM Term`,
          .data$`Planned Dose`, .data$`Actual Dose`, .data$`Dose Units`, .data$`Was Dose Missed?`, .data$`Reason Dose Missed`, .data$`Was Dose Interrupted/delayed/withheld?`,
          .data$`Reason Dose Interrupted/delayed/withheld`, .data$`Was Dose Modified/reduced/decreased?`,
          .data$`Reason Dose Modified/reduced/decreased`,  .data$`Was Dose Discontinued?`, .data$`Reason Dose Discontinued`,
          .data$`Overall response (Coded)`, .data$`Lab Category`,.data$AnalyteName, .data$NumericValue, .data$LabUnits, .data$StdValue, .data$StdUnits,.data$`Type or Name of Procedure`,
          .data$`EOS Day`, .data$`EOS Reason`, .data$`EOT Day`, .data$`EOT Reason`, .data$`Death Day`, .data$`Death Cause`, .data$StudyDay,  .data$`CRF Origin`, .data$rowid, .data$domain) %>%
        tibble::add_row(
          `Subject name or identifier` = NA_character_,
          `Event` = ".All",
          `EventType` = ".All",
          `Serious` = ".All",
          `AE Dictionary-Derived Term`= ".All",
          `AE SOC`= ".All",
          `Tox Grade` = ".All","""

            # Add DLT field if it exists
            if has_dlt:
                template += """
          `DLT` = ".All","""

            # Add Clinical interest field if it exists
            if has_clinical_interest:
                template += """
          `Clinical interest` =".All","""

            # Add Special interest field if it exists
            if has_special_interest:
                template += """
          `Special interest` =".All","""

            # Add immune-related field if it exists
            if has_immune_mediated:
                template += """
          `Immune-related` = ".All","""

            # Add infusion related field if it exists
            if has_infusion_related:
                template += """
          `Infusion related` = ".All","""

            # Add CCQ fields if tumor type is Solid Tumor
            if self.tumor_type == "Solid Tumor":
                template += """
          `CCQ Category` = ".All",
          CCQ_flag = ".All","""

            template += """
          `Outcome` = ".All",
          `CM Preferred Term`= ".All",
          `CM Term` = ".All",
          `Planned Dose` = ".All",
          `Actual Dose` = ".All",
          `Dose Units` = ".All",
          `Was Dose Missed?` = ".All",
          `Reason Dose Missed` = ".All",
          `Was Dose Interrupted/delayed/withheld?` = ".All",
          `Reason Dose Interrupted/delayed/withheld` = ".All",
          `Was Dose Modified/reduced/decreased?` = ".All",
          `Reason Dose Modified/reduced/decreased` = ".All",
          `Was Dose Discontinued?` = ".All",
          `Reason Dose Discontinued` = ".All",
          `Overall response (Coded)` = ".All" ,
          `Lab Category` = ".All" ,
           AnalyteName= ".All" ,
          NumericValue = 1.1,
          LabUnits = ".All",
          StdValue = 1.1,
          StdUnits =  ".All",
          `Type or Name of Procedure` = ".All",
          `EOS Day` = 1,
          `EOS Reason` = ".All",
          `EOT Day` = 1,
          `EOT Reason` = ".All",
          `Death Day`= 1,
          `Death Cause`= ".All",
          StudyDay = 1,
          `CRF Origin` = ".All",
          rowid = ".All",
          domain = "EOT/EOS",
          .before = 1
        )

      # aePatientProfile for the standard high-level MDR -------
      aePatientProfile <- PPandCorevars %>%
        dplyr::filter(.data$`CRF Origin` == "{ae}") %>%
        dplyr::select(.data$`Subject name or identifier`, .data$`AE Start Date`, .data$`AE End Date`,
                      .data$`AE Dictionary-Derived Term`, .data$AETerm, .data$`AE SOC`, .data$`Tox Grade`, .data$Outcome,
                      .data$Serious, .data$`AE Death`,  .data$rowid, .data$EventType, .data$StudyDay"""

            # Add DLT field if it exists
            if has_dlt:
                template += ", .data$DLT"

            # Add Clinical interest field if it exists
            if has_clinical_interest:
                template += ", .data$`Clinical interest`"

            # Add Special interest field if it exists
            if has_special_interest:
                template += ", .data$`Special interest`"

            # Add immune-related field if it exists
            if has_immune_mediated:
                template += ", .data$`Immune-related`"

            # Add infusion related field if it exists
            if has_infusion_related:
                template += ", .data$`Infusion related`"

            # Add CCQ fields if tumor type is Solid Tumor
            if self.tumor_type == "Solid Tumor":
                template += ", .data$`CCQ Category`, .data$CCQ_flag"

            template += """) %>%
        dplyr::mutate(`AE Duration (days)` = dplyr::if_else(!is.na(.data$`AE Start Date`) & !is.na(.data$`AE End Date`),
                                                            as.integer(difftime(.data$`AE End Date`,
                                                                                .data$`AE Start Date`,
                                                                                units = "days")), NULL) ) %>%
        dplyr::left_join(Treated %>%
                           dplyr::select(.data$`Subject name or identifier`, .data$Treated) , by = "Subject name or identifier") %>%
        dplyr::filter(.data$Treated == "Yes")




      # Assign PPandCorevars to calling envir ----------------------------------------------------
      assign("PPandCorevars", PPandCorevars, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," PPandCorevars returned"))

      # Assign PatientProfile to calling envir ----------------------------------------------------
      assign("PatientProfile", PatientProfile, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," PatientProfile returned"))

      # Assign aePatientProfile to calling envir ----------------------------------------------------
      assign("aePatientProfile", aePatientProfile, envir = parent.frame())
      log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," aePatientProfile returned"))

      # End of patientProfile Function ----------------------------------------------------
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    }},

    error = function(e){{
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {{
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }}
      stop(paste0("Failure in function :", calledFun))
    }},
    warning = function(w){{
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }}
  )
}}"""

            # Replace placeholders with actual values
            template = template.format(
                study_id=study_id,
                ae=ae,
                cm=cm,
                pproc=pproc
            )

            return template

        except Exception as e:
            raise Exception(f"Error generating function: {str(e)}")
