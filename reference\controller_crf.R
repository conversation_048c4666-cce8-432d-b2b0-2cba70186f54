library(readxl)
library(dplyr)
library(magrittr)
library(stringr)
library(tidyr)


ALS_form<- read_excel("BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx",sheet = "Forms") %>% 
  dplyr::select(.data$OID,.data$DraftFormName) %>% 
  dplyr::mutate(Form =tolower(.data$OID)) %>% 
  dplyr::select(-.data$OID) %>% 
  tidyr::drop_na()


controller_crf <- ALS_form %>% 
  dplyr::filter(grepl("^Adverse Events$|^Adverse Events \\{Mixed form\\}$|Prior/Concomitant Medications|Medical History|Enrollment|Prior/Concomitant Procedures/Surgeries|End of Study|Death Details|End of Treatment|Demographics|Study Drug Administration|Disease History|Time-point Response Assessment|Disease Assessment|Target Lesions|Non-Target Lesions|New Lesions|Vital Signs|ECG Local",.data$DraftFormName)) %>% 
  dplyr::select(-.data$DraftFormName)