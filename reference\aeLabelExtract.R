library(readxl)
library(dplyr)
library(magrittr)
library(stringr)
library(tidyr)

ALS_field<-read_excel("BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx",sheet = "Fields") %>% 
  dplyr::select(.data$FormOID,.data$FieldOID,.data$SASLabel,.data$PreText) %>% 
  dplyr::mutate(Form = tolower(.data$FormOID)) %>% 
  dplyr::select(-.data$FormOID) %>% 
  dplyr::filter(!is.na(.data$Form))


ALS_form<- read_excel("BGB-B455-101_V2.0_CC1_31Mar2025_1515.xls.xlsx",sheet = "Forms") %>% 
  dplyr::select(.data$OID,.data$DraftFormName) %>% 
  dplyr::mutate(Form =tolower(.data$OID)) %>% 
  dplyr::select(-.data$OID) %>% 
  dplyr::filter(!is.na(.data$Form))



aeLabel <- ALS_field %>% 
  dplyr::left_join(ALS_form, by = "Form") %>% 
  dplyr::mutate(SASLabel = ifelse(is.na(.data$SASLabel),.data$PreText,.data$SASLabel)) %>% 
  dplyr::filter(grepl("^Adverse Events$|^Adverse Events \\{Mixed form\\}$",.data$DraftFormName)) %>% 
  dplyr::filter(grepl("Action taken with|Dose not changed|Dose reduced|Drug withdrawn|Drug interrupted|Dose rate reduced|Not applicable",.data$SASLabel)) %>% 
  dplyr::select(-.data$PreText) %>% 
  dplyr::mutate(Drug =ifelse(grepl("Action taken with",.data$SASLabel), sub(".*with\\s+(.*?)\\s*\\(check all that apply\\).*", "\\1",.data$SASLabel), NA_character_)) %>% 
  dplyr::mutate(GroupFlag = ifelse(grepl("\\d+$",.data$FieldOID),as.numeric(sub(".*?(\\d+)$", "\\1", .data$FieldOID)),0)) %>% 
  dplyr::group_by(.data$GroupFlag) %>% 
  dplyr::mutate(Drug = first(na.omit(.data$Drug))) %>% 
  dplyr::ungroup() %>% 
  dplyr::select(-.data$GroupFlag)
