library(shiny)
library(shinyWidgets)
library(readxl)
library(openxlsx)

# Define UI for application
ui <- fluidPage(
  # Application title with a custom style
  titlePanel(
    "MDR Config Combo Generator",
    windowTitle = "Config Combo Generator"
  ),

  # Instructions with a stylish wellPanel
  fluidRow(
    column(12,
           wellPanel(
             style = "background-color: #f0f4f7; padding: 30px; border-radius: 10px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);",
             HTML("<h2 style='color: #2C3E50; font-family: 'Helvetica Neue', sans-serif;'>Instructions:</h2>"),
             HTML("<p style='color: #34495E; font-family: 'Helvetica Neue', sans-serif;'>Step 1: Ensure your ALS file is in <strong>XLSX</strong> format (not XLS).</p>"),
             HTML("<p style='color: #34495E; font-family: 'Helvetica Neue', sans-serif;'>Step 2: Select if this study uses blinded or unblinded data.</p>"),
             HTML("<p style='color: #34495E; font-family: 'Helvetica Neue', sans-serif;'>Step 3: Choose lesion_metric, anchor_type, and study_unit for da_meta_config based on the study protocol.</p>")
           )
    )
  ),

  # Sidebar with inputs and buttons
  sidebarLayout(
    sidebarPanel(
      width = 3,  # Adjusting sidebar width for better space usage
      fileInput("file", "Upload ALS File",
                buttonLabel = "Browse",
                placeholder = "No file selected",
                accept = ".xlsx"),

      selectInput(inputId = "Blind", label = "Blinded data?", choices = c("Blinded" = "b", "Unblinded" = "u")),

      selectInput(inputId = "lesion_metric", label = "Lesion Metric", choices = c("Sum" = "sum", "Max" = "max")),

      selectInput(inputId = "anchor_type", label = "Anchor Type", choices = c("First Dose Date" = "first_dose", "Randomization Date" = "rand")),

      selectInput(inputId = "study_unit", label = "Study Unit for lesion", choices = c("mm", "cm")),

      actionButton("generate", "Generate Config Combo",
                   class = "btn-primary",
                   style = "background-color: #3498db; color: white; font-weight: bold; border-radius: 5px; padding: 10px;")
    ),

    # Main panel for output
    mainPanel(
      width = 9,  # Adjusting width for better layout
      tags$style(HTML("
        .progress-bar {
          background-color: #3498db;
        }
      ")),
      textOutput("status"),
      textOutput("error")
    )
  )
)
