import os
import sys
import pandas as pd
from patient_profile_generator import PatientProfileGenerator

def create_mock_als_file(file_path, interest_field_type=None):
    """Create a mock ALS file with different types of interest fields."""
    # Create a writer to save the Excel file
    writer = pd.ExcelWriter(file_path, engine='openpyxl')
    
    # Create Forms sheet
    forms_data = {
        'OID': ['F.AE', 'F.CM', 'F.RS_R', 'F.PPROC'],
        'DraftFormName': [
            'Adverse Events', 
            'Prior/Concomitant Medications', 
            'Time-point Response Assessment', 
            'Prior/Concomitant Procedures/Surgeries'
        ],
        'Form': ['ae', 'cm', 'rs_r', 'pproc']
    }
    forms_df = pd.DataFrame(forms_data)
    forms_df.to_excel(writer, sheet_name='Forms', index=False)
    
    # Create Fields sheet with different types of interest fields
    fields_data = {
        'FormOID': [
            'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE', 'F.AE',
            'F.CM', 'F.CM', 'F.CM', 'F.CM', 'F.CM',
            'F.RS_R', 'F.RS_R',
            'F.PPROC', 'F.PPROC'
        ],
        'FieldOID': [
            'AESTDAT', 'AEENDAT', 'AETERM', 'AETERM_PT', 'AETERM_SOC', 'AETOXGR', 'AESDTH', 'AESER',
            'CMSTDAT', 'CMENDAT', 'CMTRT', 'CMTRT_PROD', 'CMONGO',
            'RSORRES', 'RSDAT',
            'PRSTDAT', 'PRTRT'
        ],
        'SASLabel': [
            'AE Start date', 'AE End date', 'Adverse event', 'AE Dictionary-Derived Term', 'AE SOC', 'Toxicity grade', 'Result in death', 'Was adverse event serious',
            'CM Start date', 'CM End date', 'Medication name', 'CM Preferred Term', 'Ongoing',
            'Overall response', 'Date of overall response',
            'Date of procedure/surgery', 'Type or name of procedure/surgery'
        ]
    }
    
    # Add DLT field
    fields_data['FormOID'].append('F.AE')
    fields_data['FieldOID'].append('AEDLT')
    fields_data['SASLabel'].append('DLT')
    
    # Add interest field based on the specified type
    if interest_field_type:
        fields_data['FormOID'].append('F.AE')
        fields_data['FieldOID'].append('AECI')
        fields_data['SASLabel'].append(interest_field_type)
    
    fields_df = pd.DataFrame(fields_data)
    fields_df.to_excel(writer, sheet_name='Fields', index=False)
    
    # Create DataDictionaryEntries sheet
    data_dic_data = {
        'DataDictionaryName': ['RSORRES'],
        'CodedData': ['CR', 'PR', 'SD', 'PD', 'NE'],
        'UserDataString': [
            'Complete response (CR)', 
            'Partial response (PR)', 
            'Stable disease (SD)', 
            'Progressive disease (PD)', 
            'Not evaluable (NE)'
        ]
    }
    data_dic_df = pd.DataFrame(data_dic_data)
    data_dic_df.to_excel(writer, sheet_name='DataDictionaryEntries', index=False)
    
    # Save the Excel file
    writer.close()
    
    return file_path

def main():
    # Test with different types of interest fields
    interest_field_types = [
        None,  # No interest field
        'Clinical interest',
        'Special interest'
    ]
    
    mock_als_files = []
    
    # Create mock ALS files for each interest field type
    for i, interest_type in enumerate(interest_field_types):
        file_name = f"mock_als_interest_{i}.xlsx"
        create_mock_als_file(file_name, interest_type)
        mock_als_files.append(file_name)
    
    # Generate R functions for each case
    for i, file_path in enumerate(mock_als_files):
        generator = PatientProfileGenerator(file_path)
        study_id = f"test_interest_{i}"
        r_function = generator.generate_function(study_id)
        
        output_file = f"patientProfile_{study_id}_generated.R"
        with open(output_file, "w") as f:
            f.write(r_function)
        
        interest_type = interest_field_types[i] or "No interest field"
        print(f"\nR function for '{interest_type}' generated and saved to '{output_file}'")
    
    # Clean up
    for file_path in mock_als_files:
        os.remove(file_path)

if __name__ == "__main__":
    main()
