#!/usr/bin/env node

/**
 * Development Setup Script for MDR R Package Generator
 * This script sets up the development environment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const os = require('os');

class DevSetup {
  constructor() {
    this.platform = os.platform();
    this.projectRoot = path.resolve(__dirname, '..');
    this.pythonEnvDir = path.join(this.projectRoot, 'python-env');
  }

  log(message) {
    console.log(`[SETUP] ${new Date().toISOString()} - ${message}`);
  }

  error(message) {
    console.error(`[ERROR] ${new Date().toISOString()} - ${message}`);
  }

  async run() {
    try {
      this.log('Setting up development environment...');
      
      // Check prerequisites
      await this.checkPrerequisites();
      
      // Setup Python environment
      await this.setupPythonEnvironment();
      
      // Install Node.js dependencies
      await this.installNodeDependencies();
      
      // Create development scripts
      await this.createDevScripts();
      
      // Setup git hooks (if git repo)
      await this.setupGitHooks();
      
      this.log('Development environment setup completed!');
      this.printNextSteps();
      
    } catch (error) {
      this.error(`Setup failed: ${error.message}`);
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    this.log('Checking prerequisites...');
    
    // Check Node.js
    try {
      const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
      this.log(`Node.js version: ${nodeVersion}`);
      
      const majorVersion = parseInt(nodeVersion.substring(1).split('.')[0]);
      if (majorVersion < 16) {
        throw new Error('Node.js version 16 or higher is required');
      }
    } catch (error) {
      throw new Error('Node.js is not installed or not in PATH');
    }

    // Check npm
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      this.log(`npm version: ${npmVersion}`);
    } catch (error) {
      throw new Error('npm is not installed or not in PATH');
    }

    // Check Python
    try {
      const pythonVersion = execSync('python --version', { encoding: 'utf8' }).trim();
      this.log(`Python version: ${pythonVersion}`);
      
      const versionMatch = pythonVersion.match(/Python (\d+)\.(\d+)/);
      if (versionMatch) {
        const major = parseInt(versionMatch[1]);
        const minor = parseInt(versionMatch[2]);
        if (major < 3 || (major === 3 && minor < 8)) {
          throw new Error('Python 3.8 or higher is required');
        }
      }
    } catch (error) {
      throw new Error('Python is not installed or not in PATH');
    }

    // Check pip
    try {
      const pipVersion = execSync('python -m pip --version', { encoding: 'utf8' }).trim();
      this.log(`pip version: ${pipVersion}`);
    } catch (error) {
      throw new Error('pip is not available');
    }
  }

  async setupPythonEnvironment() {
    this.log('Setting up Python virtual environment...');
    
    // Create virtual environment if it doesn't exist
    if (!fs.existsSync(this.pythonEnvDir)) {
      this.log('Creating Python virtual environment...');
      execSync(`python -m venv "${this.pythonEnvDir}"`, { 
        cwd: this.projectRoot,
        stdio: 'inherit' 
      });
    } else {
      this.log('Python virtual environment already exists');
    }

    // Get Python executable path
    const pythonExe = this.platform === 'win32' 
      ? path.join(this.pythonEnvDir, 'Scripts', 'python.exe')
      : path.join(this.pythonEnvDir, 'bin', 'python');

    // Upgrade pip
    this.log('Upgrading pip...');
    execSync(`"${pythonExe}" -m pip install --upgrade pip`, { 
      cwd: this.projectRoot,
      stdio: 'inherit' 
    });

    // Install development requirements
    const requirementsFile = path.join(this.projectRoot, 'requirements.txt');
    if (fs.existsSync(requirementsFile)) {
      this.log('Installing Python requirements...');
      execSync(`"${pythonExe}" -m pip install -r "${requirementsFile}"`, { 
        cwd: this.projectRoot,
        stdio: 'inherit' 
      });
    }

    // Install additional development packages
    this.log('Installing development packages...');
    const devPackages = [
      'streamlit>=1.31.1',
      'pytest>=7.0.0',
      'black>=22.0.0',
      'flake8>=4.0.0',
      'pylint>=2.15.0'
    ];
    
    for (const package of devPackages) {
      try {
        execSync(`"${pythonExe}" -m pip install "${package}"`, { 
          cwd: this.projectRoot,
          stdio: 'pipe' 
        });
      } catch (error) {
        this.log(`Warning: Could not install ${package}`);
      }
    }
  }

  async installNodeDependencies() {
    this.log('Installing Node.js dependencies...');
    
    try {
      execSync('npm install', { 
        cwd: this.projectRoot,
        stdio: 'inherit' 
      });
      this.log('Node.js dependencies installed');
    } catch (error) {
      throw new Error('Failed to install Node.js dependencies');
    }
  }

  async createDevScripts() {
    this.log('Creating development scripts...');
    
    const scriptsDir = path.join(this.projectRoot, 'scripts');
    if (!fs.existsSync(scriptsDir)) {
      fs.mkdirSync(scriptsDir, { recursive: true });
    }

    // Create start script for development
    const startScript = this.platform === 'win32' ? 'start-dev.bat' : 'start-dev.sh';
    const startScriptPath = path.join(scriptsDir, startScript);
    
    let startScriptContent;
    if (this.platform === 'win32') {
      startScriptContent = `@echo off
echo Starting MDR R Package Generator in development mode...
cd /d "%~dp0.."
npm run dev
`;
    } else {
      startScriptContent = `#!/bin/bash
echo "Starting MDR R Package Generator in development mode..."
cd "$(dirname "$0")/.."
npm run dev
`;
    }
    
    fs.writeFileSync(startScriptPath, startScriptContent);
    
    if (this.platform !== 'win32') {
      execSync(`chmod +x "${startScriptPath}"`);
    }

    // Create Python activation script
    const activateScript = this.platform === 'win32' ? 'activate-python.bat' : 'activate-python.sh';
    const activateScriptPath = path.join(scriptsDir, activateScript);
    
    let activateScriptContent;
    if (this.platform === 'win32') {
      activateScriptContent = `@echo off
echo Activating Python virtual environment...
call "%~dp0..\\python-env\\Scripts\\activate.bat"
cmd /k
`;
    } else {
      activateScriptContent = `#!/bin/bash
echo "Activating Python virtual environment..."
source "$(dirname "$0")/../python-env/bin/activate"
exec "$SHELL"
`;
    }
    
    fs.writeFileSync(activateScriptPath, activateScriptContent);
    
    if (this.platform !== 'win32') {
      execSync(`chmod +x "${activateScriptPath}"`);
    }
  }

  async setupGitHooks() {
    const gitDir = path.join(this.projectRoot, '.git');
    if (!fs.existsSync(gitDir)) {
      this.log('Not a git repository, skipping git hooks setup');
      return;
    }

    this.log('Setting up git hooks...');
    
    const hooksDir = path.join(gitDir, 'hooks');
    if (!fs.existsSync(hooksDir)) {
      fs.mkdirSync(hooksDir, { recursive: true });
    }

    // Create pre-commit hook
    const preCommitHook = path.join(hooksDir, 'pre-commit');
    const preCommitContent = `#!/bin/sh
# Pre-commit hook for MDR R Package Generator

echo "Running pre-commit checks..."

# Check Python code formatting
if command -v black >/dev/null 2>&1; then
    echo "Checking Python code formatting..."
    python -m black --check . || {
        echo "Python code formatting issues found. Run 'python -m black .' to fix."
        exit 1
    }
fi

# Check Python linting
if command -v flake8 >/dev/null 2>&1; then
    echo "Running Python linting..."
    python -m flake8 . || {
        echo "Python linting issues found."
        exit 1
    }
fi

echo "Pre-commit checks passed!"
`;
    
    fs.writeFileSync(preCommitHook, preCommitContent);
    execSync(`chmod +x "${preCommitHook}"`);
  }

  printNextSteps() {
    console.log(`
🎉 Development environment setup completed!

Next steps:
1. Start development server:
   npm run dev

2. Build the application:
   npm run build

3. Activate Python environment:
   ${this.platform === 'win32' ? 'scripts\\activate-python.bat' : './scripts/activate-python.sh'}

4. Run tests:
   npm test

5. Format Python code:
   python -m black .

6. Lint Python code:
   python -m flake8 .

Happy coding! 🚀
`);
  }
}

// Run the setup
const setup = new DevSetup();
setup.run().catch(error => {
  console.error('Setup failed:', error);
  process.exit(1);
});
