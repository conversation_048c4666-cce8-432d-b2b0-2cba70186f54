import os
import sys
from patient_profile_generator import PatientProfileGenerator

def main():
    # List all xlsx files in the current directory
    als_files = [f for f in os.listdir() if f.endswith('.xlsx')]
    print(f"Available ALS files: {als_files}")
    
    if not als_files:
        print("No ALS files found in the current directory.")
        return
    
    # Use the first ALS file found
    als_file = als_files[0]
    print(f"Using ALS file: {als_file}")
    
    try:
        # Create a PatientProfileGenerator instance
        generator = PatientProfileGenerator(als_file)
        
        # Print the table mappings
        print("\nTable Mappings:")
        for key, value in generator.table_mappings.items():
            print(f"  {key}: {value}")
        
        # Print the RS variables
        rs_vars = generator._get_variable_mapping('rs_std')
        print("\nRS variables:")
        print(rs_vars)
        
        # Generate the R function for a test study ID
        study_id = "test_study"
        print(f"\nGenerating R function for study ID: {study_id}")
        r_function = generator.generate_function(study_id)
        
        # Save the first 100 lines of the R function to a file
        output_file = f"patientProfile_{study_id}_generated.R"
        with open(output_file, "w") as f:
            lines = r_function.split('\n')
            f.write('\n'.join(lines[:100]))
        
        print(f"First 100 lines of R function saved to '{output_file}'")
        
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
