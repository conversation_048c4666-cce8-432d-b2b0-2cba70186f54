#' @title doseMerge_b_bgb_b3227_101
#' @description Merge all dosing crfs together
#'
#' @param studyId This is a character field, studyId
#' @param sourceLocation This is a character field, study analysis directory
#' @param tempLogger This is the logger for the whole job, creating one logfile per jobdatetime
#' @param jobdatetime This is a character field, datetime of the jobrun, format Y_m_d_H_M_OS3
#' @param drug1 This dosing crf #1
#' @param drug2 This dosing crf #2
#' @param develop.f disable email notification for testing
#' @param vpath This is the location of vault
#'
#' @return dose_merged
#'
#' @export doseMerge_b_bgb_b3227_101
#'
#' @importFrom dplyr mutate select bind_rows case_when
#' @importFrom tidyr unite
#' @importFrom writexl write_xlsx
#' @importFrom magrittr %>%
#' @importFrom checkmate assert_data_frame
#' @importFrom log4r info warn error
#' @importFrom mailR send.mail
#' @importFrom secret get_secret local_key
#' @importFrom rlang .data
#'
#' @examples
#' \dontrun{
#'doseMerge_b_bgb_b3227_101(studyId = studyIdVar, sourceLocation = OutFolder,
#'                         tempLogger = CentralLogger, jobdatetime = jobdatetime,
#'                         drug1 = sda1217 , drug2 = sdainf)
#' }
#'
#'
#'
doseMerge_b_bgb_b3227_101 <- function(studyId, sourceLocation, tempLogger, jobdatetime = jobdatetime, drug1, drug2, develop.f = develop.f, vpath = vpath) {
  withCallingHandlers(
    expr = {
      calledFun = "doseMerge_b_bgb_b3227_101"
    # Start Function --------------------------------------------------------------------
    log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- Started."))

    # Assertions -----------------------------------------------------------------------
    # Assert sourceLocation directory exists
    checkmate::assert_directory_exists(sourceLocation)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," The sourceLocation directory exists."))
    # Assert drug1 has min.rows and min.cols
    checkmate::assert_data_frame(drug1, min.rows = 0, min.cols = 2)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm drug1 has min.rows and min.cols."))
    # Assert drug2 has min.rows and min.cols
    checkmate::assert_data_frame(drug2, min.rows = 0, min.cols = 2)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," Confirm drug2 has min.rows and min.cols."))

    # Dose Data Table #1 ----
    # Add CRF Origin and Treatment names
    # UpdateVars
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dose1 dataframe"))
    dose1 <- drug1 %>%
      dplyr::mutate(Treatment = "A1217", `CRF Origin` = "sda1217", `Dose Units` = "") %>%
      dplyr::mutate(SDA12PDO = as.character(.data$SDA12PDO), SDA12ADO = as.character(.data$SDA12ADO)) %>%
      dplyr::select(`Subject name or identifier` = .data$Subject,
                    `Dose Start Date (Interpolated)` = .data$SDA12SDT_INT,
                    `Dose End Date (Interpolated)` = .data$SDA12EDT_INT,
                    `Planned Dose` = .data$SDA12PDO,
                    `Actual Dose` = .data$SDA12ADO,
                    `Cycle Number` = .data$Folder,
                    .data$Treatment,
                    .data$`CRF Origin`,
                    .data$`Dose Units`,
                    `Was Study Drug administered?` = .data$SDA12YN_STD, #Make this yes if acutal dose >0 &
                    `Reason for not administered` = .data$SDA12NRE,
                    `Specify Other for Reason not administered`= .data$SDA12NOT,
                    # `Was Dose Modified?` = .data$SDAID_STD,
                    # `Reason Dose Modified` = .data$SDARRPD,
                    #`Specify Other for Reason Dose Modified`
                    # `Was Dose Missed?` = .data$EXREAS1_STD,
                    # `Reason Dose Missed` = .data$SDARMCD,
                    # `Specify Other for Reason Dose Missed`
                    `Was Dose delayed since the last dose?`= .data$SDA12DOD_STD,
                    `Reason Dose delayed`= 	.data$SDA12DDR,
                    `Specify Other for Reason Dose delayed` = .data$SDA12DOT,
                    `Was Dose interrupted?`= .data$SDA12INR_STD,
                    `Reason Dose interrupted`= .data$SDA12IRE,
                    `Specify Other for Reason Dose interrupted` = .data$SDA12IOS,
                    `Was Dose Discontinued?`= .data$SDA12DIS_STD,
                    `Reason Dose Discontinued`= .data$SDA12DIR,
                    `Specify Other for Reason Dose Discontinued` = .data$SDA12DIO,
                    .data$RecordId,
                    .data$LastChangeDate,
                    .data$WhatChanged
                    #  `Was Dose Decreased?`,
                    #  `Reason Dose Decreased`
                    #`Specify Other for Reason Dose Decreased`
                    ) %>%
        dplyr::mutate(
                      #`Reason for not administered`
                      #`Specify Other for Reason not administered`
                      `Was Dose Modified?`= NA_character_,
                      `Reason Dose Modified`= NA_character_,
                      `Specify Other for Reason Dose Modified`= NA_character_,
                      `Was Dose Missed?` = NA_character_,
                      `Reason Dose Missed` = NA_character_,
                      `Specify Other for Reason Dose Missed`= NA_character_,
                      #`Was Dose delayed since the last dose?` = NA_character_,
                      #`Reason Dose delayed ` = NA_character_,
                      #`Specify Other for Reason Dose delayed`= NA_character_,
                      #`Was Dose interrupted?` = NA_character_,
                      #`Reason Dose interrupted` = NA_character_,
                      #`Specify Other for Reason Dose interrupted`  = NA_character_,
                      #`Was Dose Discontinued?` = NA_character_,
                      #`Reason Dose Discontinued` = NA_character_,
                      #`Specify Other for Reason Dose Discontinued` = NA_character_,
                      `Was Dose Decreased?` = NA_character_,
                      `Reason Dose Decreased` = NA_character_,
                      `Specify Other for Reason Dose Decreased`  = NA_character_)

    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Done creating dose1 dataframe"))

    # Dose Data Table #2----
    # Add CRF Origin and Treatment names
    # UpdateVars

    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Creating dose2 dataframe"))
    dose2 <- drug2 %>%
      dplyr::mutate(Treatment = "Tislelizumab", `CRF Origin` = "sdainf", `Dose Units` = "") %>%
      dplyr::mutate(SDAPLNDS = as.character(.data$SDAPLNDS), 	SDAADOS = as.character(.data$SDAADOS)) %>%
      dplyr::select(`Subject name or identifier` = .data$Subject,
                    `Dose Start Date (Interpolated)` = .data$SDASTIME_INT,
                    `Dose End Date (Interpolated)` = 	.data$SDAETIME_INT,
                    `Planned Dose` = .data$SDAPLNDS,
                    `Actual Dose` = .data$SDAADOS,
                    `Cycle Number` = .data$Folder,
                    .data$Treatment,
                    .data$`CRF Origin` ,
                    .data$`Dose Units` ,
                    `Was Study Drug administered?` = 	.data$SDAYN_STD,
                    `Reason for not administered` = .data$SDANREAS,
                    `Specify Other for Reason not administered` = .data$SDANOTH,
                    ####################
                    # `Was Dose Modified?` = SDAID_STD,
                    #  `Reason Dose Modified` = SDARRPD,
                    # `Specify Other for Reason Dose Modified`
                    #  `Was Dose Missed?` = EXREAS1_STD,
                    #  `Reason Dose Missed` = SDARMCD,
                    # `Specify Other for Reason Dose Missed`= NA_character_,
                    `Was Dose delayed since the last dose?`= .data$SDADODEL_STD,
                    `Reason Dose delayed`= .data$SDADDRES,
                    `Specify Other for Reason Dose delayed` = .data$SDADDOT,
                    `Was Dose interrupted?`= .data$SDAINRP_STD,
                    `Reason Dose interrupted`= .data$SDAIRES,
                    `Specify Other for Reason Dose interrupted` = .data$SDAIOSPY,
                    `Was Dose Discontinued?`= .data$SDADISC_STD,
                    `Reason Dose Discontinued`= .data$SDAYSPY,
                    `Specify Other for Reason Dose Discontinued` = .data$SDAOSPY,
                    .data$RecordId,
                    .data$LastChangeDate,
                    .data$WhatChanged
                    ) %>%
      dplyr::mutate(
        #`Reason for not administered`
        #`Specify Other for Reason not administered`
        `Was Dose Modified?`= NA_character_,
        `Reason Dose Modified`= NA_character_,
        `Specify Other for Reason Dose Modified`= NA_character_,
        `Was Dose Missed?` = NA_character_,
        `Reason Dose Missed` = NA_character_,
        `Specify Other for Reason Dose Missed`= NA_character_,
        #`Was Dose delayed since the last dose?` = NA_character_,
        #`Reason Dose delayed` = NA_character_,
        #`Specify Other for Reason Dose delayed`= NA_character_,
        #`Was Dose interrupted?` = NA_character_,
        #`Reason Dose interrupted` = NA_character_,
        #`Specify Other for Reason Dose interrupted`  = NA_character_,
        #`Was Dose Discontinued?` = NA_character_,
        #`Reason Dose Discontinued` = NA_character_,
        #`Specify Other for Reason Dose Discontinued` = NA_character_,
        `Was Dose Decreased?` = NA_character_,
        `Reason Dose Decreased` = NA_character_,
        `Specify Other for Reason Dose Decreased`  = NA_character_)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Done creating dose2 dataframe"))



    # Bind Doses together ---------------------------------------------------------------
      dose_merged <- dplyr::bind_rows(dose1,dose2) %>%
      dplyr::select(.data$Treatment,
                    .data$`CRF Origin`,
                    .data$`Subject name or identifier`,
                    .data$`Cycle Number`,
                    .data$`Dose Start Date (Interpolated)`,
                    .data$`Dose End Date (Interpolated)`,
                    .data$`Was Study Drug administered?`,
                    .data$`Reason for not administered` ,
                    .data$`Specify Other for Reason not administered`,
                    .data$`Planned Dose`,
                    .data$`Actual Dose`,
                    .data$`Dose Units`,
                    .data$`Was Dose Modified?`,
                    .data$`Reason Dose Modified`,
                    .data$`Specify Other for Reason Dose Modified`,
                    .data$`Was Dose Missed?`,
                    .data$`Reason Dose Missed`,
                    .data$`Specify Other for Reason Dose Missed`,
                    .data$`Was Dose delayed since the last dose?`,
                    .data$`Reason Dose delayed`,
                    .data$`Specify Other for Reason Dose delayed`,
                    .data$`Was Dose interrupted?`,
                    .data$`Reason Dose interrupted`,
                    .data$`Specify Other for Reason Dose interrupted`,
                    .data$`Was Dose Discontinued?`,
                    .data$`Reason Dose Discontinued`,
                    .data$`Specify Other for Reason Dose Discontinued` ,
                    .data$`Was Dose Decreased?`,
                    .data$`Reason Dose Decreased`,
                    .data$`Specify Other for Reason Dose Decreased`,
                    .data$RecordId,
                    .data$LastChangeDate,
                    .data$WhatChanged ) %>%
      dplyr::mutate(`Was Dose Interrupted/delayed/withheld?` = dplyr::case_when(
        substring(.data$`Was Dose interrupted?`,1,1)=="Y"| substring(.data$`Was Dose delayed since the last dose?`,1,1)=="Y" ~"Y",
        substring(.data$`Was Dose interrupted?`,1,1)=="N"| substring(.data$`Was Dose delayed since the last dose?`,1,1)=="N" ~"N",
        TRUE ~ NA_character_)) %>%
      dplyr::mutate(`Was Dose Modified/reduced/decreased?` = dplyr::case_when(
        substring(.data$`Was Dose Modified?`,1,1)=="Y"| substring(.data$`Was Dose Decreased?`,1,1)=="Y" ~"Y",
        substring(.data$`Was Dose Modified?`,1,1)=="N"| substring(.data$`Was Dose Decreased?`,1,1)=="N" ~"N",
        TRUE ~ NA_character_)) %>%
      tidyr::unite(col = "Reason Dose Interrupted/delayed/withheld", c("Reason Dose interrupted", "Reason Dose delayed"), na.rm = TRUE, sep = ",", remove = FALSE) %>%
      tidyr::unite(col = "Reason Dose Modified/reduced/decreased", c("Reason Dose Modified", "Reason Dose Decreased"), na.rm = TRUE, sep = ",", remove = FALSE) %>%
      tidyr::unite(col = "Specify Other Reason Dose Interrupted/delayed/withheld" , c("Specify Other for Reason Dose interrupted", "Specify Other for Reason Dose delayed"), na.rm = TRUE, sep = ",", remove = FALSE) %>%
      tidyr::unite(col = "Specify Other Reason Dose Modified/reduced/decreased", c("Specify Other for Reason Dose Modified", "Specify Other for Reason Dose Decreased"), na.rm = TRUE, sep = ",", remove = FALSE) %>%
      dplyr::select(-.data$`Specify Other Reason Dose Modified/reduced/decreased`,
                    -.data$`Reason Dose Modified/reduced/decreased`,
                    -.data$`Reason Dose Interrupted/delayed/withheld` ,
                    -.data$`Specify Other Reason Dose Interrupted/delayed/withheld`,
                    tidyselect::everything())


    checkmate::assert_data_frame(dose_merged,min.rows = 2)
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|","Bind doses together"))

    # Assign dose_merged to calling envir ----------------------------------------------------
    assign("dose_merged", dose_merged, envir = parent.frame())
    log4r::info(tempLogger, paste0(studyId,"|",calledFun,"|"," dose_merged returned"))

    # End of doseMerge Function ----------------------------------------------------
    log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
    },

    error = function(e){
      #Log that error was caught, print error, log detail error condition, and stop
      log4r::error(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught an error!"))
      log4r::error(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Error : ", conditionMessage(e)))
      log4r::info(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,"- All done, quitting."))
      #SCL 2020_05_06 - EMAIL on Error
      if (is.null(develop.f)) {
        mailR::send.mail(from = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath)$user.name,
                         to = secret::get_secret("mail_to", key = secret::local_key(), vault = vpath),
                         subject = paste0("MDR Transformation Function: ", calledFun, " Failure at jobdatetime: ",jobdatetime),
                         body = paste0("<html><body><p>StudyId: ", studyId, " </p><p>Error DateTime: ",
                                       format(Sys.time(), "%Y-%m-%d %X %Z"), "</p><p>Error : ", conditionMessage(e),"</p></body></html>"),
                         html = TRUE,
                         smtp = secret::get_secret("mail_smtp", key = secret::local_key(), vault = vpath),
                         authenticate = TRUE,
                         send = TRUE)
      }
      stop(paste0("Failure in function :", calledFun))
    },
    warning = function(w){
      # Log that warning was caught, print warning, log detail warning condition
      log4r::warn(tempLogger,paste0(studyId,"|",calledFun,"|",calledFun,": Caught a warning!"))
      log4r::warn(tempLogger, paste0(studyId,"|",calledFun,"|",calledFun," Warning : ", conditionMessage(w)))
      invokeRestart("muffleWarning")
    }
  )
}
